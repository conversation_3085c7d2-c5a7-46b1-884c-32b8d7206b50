# Portail ZeFid Client 

## 1) Utilisation de git

- Mise en place de feature branch à partir 01/10/2018
- Lien vers coucous à venir 

---

## 2) PAQ 

[PAQ](docs/PAQ.md)

---

Feature Branch appliqué :


| Site                    |  Feature Branch |  webservice.prod dans parameters.yml |
| :------------           | :-------------: | :----------------------------------: |
|  Adfid                  |        0        |                0                     |
|  Alphega                |        1        |                0                     |
|  Coiff-et-co            |        0        |                0                     |
|  Delbard                |        0        |                0                     |
|  <PERSON>-sante            |        0        |                0                     |
|  Pharmavie              |        0        |                0                     |
|  Engen                  |        0        |                0                     |
|  Espace Emeraude        |        0        |                0                     |
|  Vertdis                |        1        |                0                     |
|  Haircoif               |        0        |                0                     |  
|  Jardineries du terroir |        0        |                0                     |
|  Mutualpharmacies       |        0        |                0                     |
|  Objectif privilege     |        0        |                0                     |
|  Optic libre            |        0        |                0                     |
|  Pharmavie              |        0        |                0                     |
|  Générique              |        0        |                0                     |
|  Nalods                 |        0        |                0                     |


## Patch symfony/serializer
lib utilisée pour le patch : https://tomasvotruba.com/blog/2020/07/02/how-to-patch-package-in-vendor-yet-allow-its-updates/
code ajouté : https://github.com/symfony/symfony/blob/2fe484402c4a8bace5a65f792c75315058a3ab6a/src/Symfony/Component/Serializer/Normalizer/AbstractObjectNormalizer.php#L618C8-L631C10
issue : https://github.com/symfony/symfony/issues/58479
merge request en attente : https://github.com/symfony/symfony/pull/58488