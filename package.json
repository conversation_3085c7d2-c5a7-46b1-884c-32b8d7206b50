{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@hotwired/stimulus": "^3.0.0", "@symfony/stimulus-bridge": "^3.2.0", "@symfony/webpack-encore": "^5.0.0", "core-js": "^3.38.0", "file-loader": "^6.0.0", "regenerator-runtime": "^0.13.9", "sass": "^1.44.0", "sass-loader": "^16.0.1", "webpack": "^5.74.0", "webpack-cli": "^5.1.0", "webpack-notifier": "^1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"dotenv": "^16.4.7", "jquery": "2", "leaflet": "^1.9.4"}}