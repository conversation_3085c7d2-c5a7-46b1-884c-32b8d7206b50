{"alienor/api-bundle": {"version": "dev-master"}, "alienor/logger-webservice-bundle": {"version": "dev-master"}, "alienor/pdf-bundle": {"version": "dev-master"}, "alienor/requeteur-adresse-bundle": {"version": "dev-master"}, "composer/package-versions-deprecated": {"version": "*********"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.2"}, "doctrine/common": {"version": "3.1.0"}, "doctrine/dbal": {"version": "2.10.4"}, "doctrine/deprecations": {"version": "v1.0.0"}, "doctrine/doctrine-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.0", "ref": "86d2beb9c1f9ee71b9f66a45588517852c7d4620"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.2", "ref": "baaa439e3e3179e69e3da84b671f0a3e4a2f56ad"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.3"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.0.1"}, "doctrine/orm": {"version": "2.8.1"}, "doctrine/persistence": {"version": "2.1.0"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "egulias/email-validator": {"version": "2.1.24"}, "friendsofphp/php-cs-fixer": {"version": "3.69", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.12"}, "laminas/laminas-code": {"version": "4.5.1"}, "meteo-concept/hcaptcha-bundle": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "e94efaa6cd60c87dc9cc6de56b333a1754187d1e"}, "files": ["config/packages/meteo_concept_h_captcha.yaml"]}, "monolog/monolog": {"version": "1.25.5"}, "nikic/php-parser": {"version": "v4.10.3"}, "nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.4.0"}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "10.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "picqer/php-barcode-generator": {"version": "v2.2.0"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.3"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "setasign/fpdf": {"version": "1.8.4"}, "setasign/fpdi": {"version": "v2.3.6"}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5d454ec6cc4c700ed3d963f3803e1d427d9669fb"}, "files": ["public/.htaccess"]}, "symfony/asset": {"version": "v4.4.17"}, "symfony/asset-mapper": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "5ad1308aa756d58f999ffbe1540d1189f5d7d14a"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/asset_mapper.yaml", "importmap.php"]}, "symfony/browser-kit": {"version": "v4.4.17"}, "symfony/cache": {"version": "v4.4.17"}, "symfony/cache-contracts": {"version": "v2.2.0"}, "symfony/config": {"version": "v4.4.17"}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v4.4.17"}, "symfony/debug-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/dependency-injection": {"version": "v4.4.17"}, "symfony/deprecation-contracts": {"version": "v2.2.0"}, "symfony/doctrine-bridge": {"version": "v4.4.17"}, "symfony/dom-crawler": {"version": "v4.4.17"}, "symfony/dotenv": {"version": "v4.4.17"}, "symfony/error-handler": {"version": "v4.4.17"}, "symfony/event-dispatcher": {"version": "v4.4.17"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.9"}, "symfony/expression-language": {"version": "v4.4.17"}, "symfony/filesystem": {"version": "v4.4.17"}, "symfony/finder": {"version": "v4.4.17"}, "symfony/flex": {"version": "1.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "87bcf6f7c55201f345d8895deda46d2adbdbaa89"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.4.17"}, "symfony/http-client-contracts": {"version": "v2.3.1"}, "symfony/http-foundation": {"version": "v4.4.17"}, "symfony/http-kernel": {"version": "v4.4.17"}, "symfony/intl": {"version": "v4.4.17"}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v4.4.17"}, "symfony/monolog-bridge": {"version": "v4.4.17"}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/options-resolver": {"version": "v4.4.17"}, "symfony/password-hasher": {"version": "v5.4.0"}, "symfony/phpunit-bridge": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "07ce01a897311647520b43d4ddddad9537b99ba6"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.23.1"}, "symfony/polyfill-intl-icu": {"version": "v1.20.0"}, "symfony/polyfill-intl-idn": {"version": "v1.20.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.20.0"}, "symfony/polyfill-mbstring": {"version": "v1.20.0"}, "symfony/polyfill-php72": {"version": "v1.20.0"}, "symfony/polyfill-php73": {"version": "v1.20.0"}, "symfony/polyfill-php80": {"version": "v1.20.0"}, "symfony/polyfill-php81": {"version": "v1.25.0"}, "symfony/process": {"version": "v4.4.17"}, "symfony/property-access": {"version": "v4.4.17"}, "symfony/property-info": {"version": "v4.4.17"}, "symfony/routing": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/security-core": {"version": "v4.4.17"}, "symfony/security-csrf": {"version": "v4.4.17"}, "symfony/security-guard": {"version": "v4.4.17"}, "symfony/security-http": {"version": "v4.4.17"}, "symfony/serializer": {"version": "v4.4.17"}, "symfony/service-contracts": {"version": "v2.2.0"}, "symfony/stopwatch": {"version": "v4.4.17"}, "symfony/string": {"version": "v5.4.0"}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.3.0"}, "symfony/twig-bridge": {"version": "v4.4.17"}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.4.17"}, "symfony/var-exporter": {"version": "v4.4.17"}, "symfony/web-link": {"version": "v4.4.17"}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/yaml": {"version": "v4.4.17"}, "twig/extra-bundle": {"version": "v3.1.1"}, "twig/twig": {"version": "v3.1.1"}, "webmozart/assert": {"version": "1.9.1"}, "yellowskies/qr-code-bundle": {"version": "1.2.8"}}