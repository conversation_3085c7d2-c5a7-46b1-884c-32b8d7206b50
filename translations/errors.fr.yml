errorMessage:
    clientNotFound: Erreur lors de la récupération des données client
    -20000: "Erreur inconnue"
    -20001: "Cascading error"
    -20002: "ORA-06502 : erreur numérique ou erreur sur une valeur: tampon de chaîne de caractères trop petit'"
    -20003: "ORA-00001: violation de contrainte unique (...)"
    -20004: "Ancienne carte déjà rattachée  !"
    -20005: "Argument(s) manquant(s)"
    -20006: "Article découverte déjà utilisé !"
    -20007: "Article ne correspond pas (ou plus) à une article découverte !"
    -20008: "Association client - compte Facebook introuvable !"
    -20009: "Association/Campagne déjà supprimée"
    -20010: "Carte nouveau programme déjà fusionnée !"
    -20011: "Ce libellé Assoc/Campagne existe déjà (non supprimée)"
    -20012: "Cet IDFACEBOOK est associé à un autre client !"
    -20013: "Cette Assoc/Campagne existe déjà (non supprimée)"
    -20014: "Champ invalide"
    -20015: "Champ obligatoire"
    -20016: "Champs obligatoires"
    -20017: "CIVILITE obligatoire !"
    -20018: "Client déjà associé à un autre IDFACEBOOK"
    -20019: "Client NON ENCARTE !"
    -20020: "Client non trouvé !"
    -20021: "Code article invalide !"
    -20022: "Code article obligatoire !"
    -20023: "Code carte invalide doit être un EAN sur 13 caractères"
    -20024: "Code client inconnu !"
    -20025: "Code postal invalide !"
    -20026: "CODECLIENT existant"
    -20027: "CodeClient invalide"
    -20028: "CSP invalide"
    -20029: "Date de naissance invalide !"
    -20030: "date de vente invalide"
    -20031: "Date début campagne invalide"
    -20032: "Date d'encaissement obligatoire"
    -20033: "Date encaissement invalide"
    -20034: "Date erronée"
    -20035: "Date fin campagne invalide"
    -20036: "échec dans la création du client"
    -20037: "Email invalide"
    -20038: "Email obligatoire"
    -20039: "EMAILFACEBOOK invalide !"
    -20040: "Erreur de magasin"
    -20041: "Erreur d'identification"
    -20042: "Erreur sur codepostal"
    -20043: "Erreur sur date de naissance"
    -20044: "Erreur sur la civilité"
    -20045: "Erreur sur la civilité et la langue"
    -20046: "Erreur sur la langue"
    -20047: "Erreur sur la longueur bateau"
    -20048: "Erreur sur le nom trop long"
    -20049: "Erreur sur le nom vide"
    -20050: "Erreur sur le prénom trop long"
    -20051: "Erreur sur le proprietaire"
    -20052: "Erreur sur le type bateau"
    -20053: "Erreur sur le type bateau moteur"
    -20054: "Erreur sur l'envoie courrier interne"
    -20055: "Erreur sur l'envoie email externe"
    -20056: "Erreur sur l'envoie sms interne"
    -20057: "Erreur sur une donnée enfant"
    -20058: "FROM_MAGASIN obligatoire !"
    -20059: "fusion interdite"
    -20060: "Heure invalide"
    -20061: "ID article invalide !"
    -20062: "ID article obligatoire !"
    -20063: "IDACTION inconnu !"
    -20064: "IDACTION obligatoire !"
    -20065: "IDANIMAL obligatoire !"
    -20066: "IDASSOCIATION inconnu !"
    -20067: "IDASSOCIATION obligatoire !"
    -20068: "IDCLIENTANIMAL obligatoire !"
    -20069: "Identifiant de campagne invalide !"
    -20070: "IDFACEBOOK ET/OU CODECLIENT OBLIGATOIRE !"
    -20071: "IDFACEBOOK obligatoire !"
    -20072: "IDMODE obligatoire !"
    -20073: "IDSESSION invalide !"
    -20074: "IDSTATUT obligatoire !"
    -20075: "Impossible de supprimer une association paramétrée dans une campagne"
    -20076: "Indicateur invalide INSCR_FID, INSCR_INFOS... valeurs possibles (0,1)"
    -20077: "Indicatif fixe invalide"
    -20078: "Indicatif mobile invalide"
    -20079: "Indice NomComplet invalide"
    -20080: "JSON obligatoire !"
    -20081: "La date de fin doit être postérieure à la date de debut"
    -20082: "la date de vente est incorrecte"
    -20083: "Le cheque n'appartient pas au client !"
    -20084: "Le code carte EAN13 n''est pas cohérent avec le code client"
    -20085: "Le montant saisi est supérieur au montant du chèque !"
    -20086: "Le token n'a pas été passé en paramètre"
    -20087: "Libellé association obligatoire"
    -20088: "magasin de rattachement différent"
    -20089: "Magasin inconnu"
    -20090: "MAGASIN obligatoire"
    -20091: "Masque ENSEIGNE invalide"
    -20092: "message générique lorsqu'un attribut manque sans préciser lequel"
    -20093: "MODE incorrect !"
    -20094: "Montant invalide"
    -20095: "MONTANT obligatoire"
    -20096: "Motif obligatoire"
    -20097: "NBPOINTS peut valoir 500, 1000 ou 1500 !"
    -20098: "NOEPC ou NONATIONAL obligatoire !"
    -20099: "Nom client obligatoire !"
    -20100: "NOM invalide !"
    -20101: "Nombre d'achats incorrect"
    -20102: "Nombre d'achats obligatoire"
    -20103: "nombre de points invalide"
    -20104: "nombre de points négatifs"
    -20105: "nombre de points obligatoire"
    -20106: "Numéro de chèque invalide"
    -20107: "Numéro de chèque obligatoire"
    -20108: "Numéro de CodeClient obligatoire"
    -20109: "Numéro de CodeClient ou CodeCarte obligatoire"
    -20110: "Port d'amarrage trop long"
    -20111: "Prénom client obligatoire !"
    -20112: "PRENOM invalide !"
    -20113: "Quantité dans le ticket dépasse le nombre d'achat total"
    -20114: "Recherche du magasin échouée"
    -20115: "réseau magasin différent (J d'un côté et D de l'autre)"
    -20116: "SITE obligatoire"
    -20117: "Situation familiale invalide"
    -20118: "Solde de points insuffisant !"
    -20119: "Structure d'argument non conforme"
    -20120: "Telephone mobile invalide !"
    -20121: "Téléphone fixe invalide"
    -20122: "Téléphone fixe obligatoire"
    -20123: "Téléphone mobile invalide"
    -20124: "Téléphone mobile obligatoire"
    -20125: "Titre campagne invalide"
    -20126: "Titre campagne obligatoire"
    -20127: "Token invalide"
    -20128: "Token manquant"
    -20129: "une des informations est manquante pour les ventes"
    -20130: "vente en double"
    -20131: "Vente parent inconnue"
    -20132: "Votre enfant a plus de 18 ans !"
    -20133: "NEWSLETTER Obligatoire"
    -20134: "CODE POSTAL Obligatoire"
    -20135: "VILLE Obligatoire"
    -20136: "CODECLIENT ou CODEAVOMARK ou EMAIL ou NOM obligatoires"
    -20137: "Type de grossesse invalide"
    -20153: "SITE invalide"
    -20154: "MAGASIN invalide"
    -20155: "LOGIN obligatoire"
    -20156: "PASSWORD obligatoire"
    -20157: "CODEENSEIGNE obligatoire"
    -20158: "CODEPOSTAL ou DATENAISSANCE obligatoire"
    -20159: " DATENAISSANCE Obligatoire"
    -20160: "Création d'un Client mineur interdite"
    -20161: "VENTELIGNES obligatoire"
    -20162: "CAGNOTTE invalide"
    -20163: "CAGNOTTE invalide (inférieure au seuil)"
    -20164: "Constante absente du paramétrage"
    -20165: "Structure paramètre VENTE invalide"
    -20166: "PRIX invalide"
    -20167: "QUANTITE invalide"
    -20170: "CODEPAYS invalide"
    -20171: "CODECARTE déjà existent"
    -20172: "Aucune enquête n'est liée à cette vente ou ce client"
    -20173: "Enquête déjà répondue"
    -20174: "Question obligatoire non répondue"
    -20175: "Numéro + Voie trop long"
    -20176: "Batiment + Escalier + Lieu dit trop long"
    -20177: "Enquête non répondue"
    -20193: "CodeClient Secondaire invalide"
    -20194: "CodeClient Secondaire obligatoire"
    -20195: "Fusion impossible entre deux ZZZ"
    -20196: "Numéro de carte non valide pour ce magasin"
    -20213: "Groupe de Salon erroné"
    -20214: "Logiciel de caisse erroné"
    -20233: "NUMEROCOMPTE invalide"
    -20253: "CODEFILLEUL obligatoire"
    -20254: "CODEFILLEUL invalide"
    -20273: "VOIE Obligatoire"
    -20274: "NUMERO Obligatoire"
    -20275: "CODEPAYS Obligatoire"
    -20276: "La date de vente est obligatoire"
    -20277: "From_IdSession obligatoire"
    -20278: "CODEPAYSCLIENT Obligatoire"
    -20279: "Civilité Inconnue"
    -20280: "CodePaysClient Inconnu"
    -20281: "Identifiant Habitation Inconnu"
    -20282: "Identifiant Animal Inconnu"
    -20283: "Genre Inconnu"
    -20284: "CODEPARRAIN invalide"
    -20285: "CODEPARRAIN obligatoire"
    -20286: "Ce client est déjà filleul."
    -20287: "Ce parrainage existe déjà."
    -20288: "Ce parrainage est impossible."
    -20289: "Numéro de ticket obligatoire."
    -20293: "Date utilisation obligatoire."
    -20294: "Date utilisation invalide."
    -20295: "Cagnotte utilisée obligatoire."
    -20296: "Cagnotte utilisée invalide."
    -20297: "Numéro de ticket invalide."
    -20298: "CODE CARTE existant ACTIF"
    -20299: "CODE CARTE existant INACTIF"
    -20300: "FAMILLE obligatoire."
    -20301: "SOUSFAMILLE obligatoire."
    -20302: "MONTANT ligne de vente obligatoire"
    -20304: "ACHAT négatif invalide"
    -20305: "CHEQUE déjà encaissé"
    -20306: "CHEQUE inexistant"
    -20307: "TTC invalide"
    -20308: "ENCAISSEMENT chèque(s) invalide"
    -20309: "REGLEMENT obligatoire"
    -20310: "Mode de Reglement obligatoire"
    -20311: "CODECARTE obligatoire"
    -20313: "Lien intérêt-sous intérêt invalide"
    -20314: "Catégorie article invalide"
    -20315: "Code Carte Secondaire obligatoire"
    -20316: "Code Carte Secondaire inexistant"
    -20317: "Le Code Carte Principal est identique au Code Carte Secondaire"
    -20318: "Code Carte Secondaire invalide"
    -20319: "Compte utilisateur inadapté pour cette fonctionnalité"
    -20320: "Code carte dans la bonne tranche, mais pas attribué"
    -20321: "Le code carte n'est pas dans la bonne tranche"
    -20322: "Montant du REGLEMENT obligatoire"
    -20323: "Collaborateur invalide"
    -20324: "Période invalide"
    -20333: "Téléphone mobile ou EMAIL obligatoire"
    -20334: "CODE VENDEUR obligatoire"
    -20353: "ID vente obligatoire"
    -20375: "Code Carte Secondaire déjà secondaire"
    -20393: "Code carte inexistant"
    -20394: "Client déjà supprimé"
    -20413: "Programme obligatoire"
    -20433: "Chèque périmé"
    -20434: "PROGRAMME invalide"
    -20435: "Mot clé invalide"
    -20436: "Ville invalide"
    -20437: "Prénom ou nom invalide"
    -20438: "ID Canal Chèque FID invalide "
    -20439: "Optin invalide"
    -20453: "Numéro Invalide"
    -20473: "LIBELLE obligatoire"
    -20474: "ADRESSE obligatoire"
    -20475: "REGION obligatoire"
    -20476: "GROUPEMAG obligatoire"
    -20477: "CODEINSEE obligatoire"
    -20478: "AGREMENT obligatoire"
    -20493: "NPAI invalide"
    -20513: "Ce magasin n'existe pas sur ce programme"
    -20514: "Fax invalide"
    -20515: "Genre enfant invalide"
    -20516: "Mode de vie invalide"
    -20517: "Top marque invalide"
    -20518: "Centre intérêt Bien-être invalide"
    -20519: "Centre intérêt Loisir invalide"
    -20573: "Centre d'interet invalide"
    -20574: "Code d'acces obligatoire"
    -20575: "Code d'acces invalide"
    -20576: "NOMBW doit faire plus de 3 caracteres ou code carte doit être renseigné"
    -20593: "CODE VENDEUR invalide"
    -20613: "Client supprimé"
    -20614: "Aucun client rattaché à cette carte"
    -20633: "IDEXTERNE Obligatoire"
    -20634: "IDEXTERNE Invalide"
    -20653: "NUMEROCOMPTE obligatoire"
    -20654: "NUMEROCOMPTE ou CHEQUE obligatoire"
    -20673: "Erreur dans la construction des animaux"
    -20693: "Numéro de carte invalide"
    -20694: "Numéro de carte obligatoire"
    -20695: "Numéro de carte déjà utilisé"
    -20697: "Le numéro de carte n'est pas dans la bonne tranche"
    -20698: "Numéro de carte inexistant"
    -20699: "Le montant doit être positif"
    -20700: "Le montant saisi doit être supérieur au montant de la carte"
    -20701: "Paramètre ANNULATION invalide"
    -20702: "La vente a déjà été annulée"
    -20713: "Ligne de vente introuvable"
    -20714: "Ligne de vente invalide"
    -20715: "Nombre de lignes de vente invalide"
    -20716: "Vente introuvable"
    -20717: "SEXE obligatoire"
    -20718: "SEXE invalide"
    -20719: "PRENOM obligatoire"
    -20720: "PRENOM invalide"
    -20721: "CIVILITE invalide"
    -20722: "NUM_OP obligatoire"
    -20723: "NUM_OP invalide"
    -20724: "IDOPECOMM obligatoire"
    -20725: "IDOPECOMM invalide"
    -20726: "IDANIMAL invalide"
    -20727: "IDSEXE invalide"
    -20728: "IDRACE invalide"
    -20729: "IDGROUPE invalide"
    -20730: "IDHABITATION invalide"
    -20731: "Nombre de produits retournés invalide"
    -20732: "Montant total des produits retournés invalide"
    -20733: "Produit introuvable dans la vente"
    -20754: "Téléphone mobile ou EMAIL Obligatoire"
    -20755: "ID vente invalide"
    -20756: "Chien existe déjà"
    -20773: "SITE + MAGASIN ou CODECLIENT obligatoires"
    -20774: "TTC obligatoire"
    -20775: "Bon inexistant"
    -20776: "Bon déjà encaissé"
    -20777: "IDCANAL obligatoire"
    -20779: "IDCANAL inexistant"
    -20780: "Téléphone mobile déjà utilisé"
    -20781: "EMAIL déjà utilisé"
    -20782: "TYPECARBURANT inexistant"
    -20783: "MARQUEVEHICULE inexistant"
    -20784: "TYPEVEHICULE inexistant"
    -20785: "IDDOCUMENT obligatoire"
    -20786: "IDPROSPECTION obligatoire"
    -20787: "IDCATALOGUE obligatoire"
    -20788: "IDCATALOGUE inexistant"
    -20805: "SITE PRINCIPAL invalide"
    -20806: "MAGASIN PRINCIPAL invalide"
    -20807: "SUPERFICIE invalide"
    -20808: "Temps de trajet invalide"
    -20809: "Statut occupation invalide"
    -20810: "Chauffage invalide"
    -20811: "Loisir invalide"
    -20812: "Potagiste invalide"
    -20813: "Agrément invalide"
    -20814: "Coopérative invalide"
