<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Fri Oct 17 15:29:14 2014
 By World Wide Web Server
Copyright (c) 2010 - 2012, <PERSON> (<EMAIL>), <PERSON>(<EMAIL>), <PERSON> (<EMAIL>) with Reserved Font Name "Raleway"
</metadata>
<defs>
<font id="Raleway-Bold" horiz-adv-x="253" >
  <font-face 
    font-family="Raleway"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 0 3 3 1 1 6 0 3"
    ascent="800"
    descent="-200"
    x-height="524"
    cap-height="709"
    bbox="-201 -229 1139 923"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-FB06"
  />
<missing-glyph horiz-adv-x="0" 
 />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="653" 
d="M568 626q-41 0 -59.5 -28t-18.5 -65v-70h138v-103h-138v-360h-134v360h-128v-360h-134v360h-68v103h68v32q0 44 12 82.5t34.5 66.5t56 44t75.5 16q34 0 66.5 -8t62.5 -21q24 30 58 47.5t82 17.5q38 0 68.5 -10t57.5 -21l-27 -101q-14 7 -31.5 12.5t-40.5 5.5zM356 515
q0 4 0.5 12.5t1.5 18t3 17.5t4 13q-15 8 -33 11t-33 3q-20 0 -33.5 -8t-21.5 -20.5t-12 -29t-4 -32.5v-37h128v52z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="832" 
d="M94 360h-68v103h68v32q0 44 12 82.5t34.5 66.5t56 44t75.5 16q34 0 66.5 -8t62.5 -21q28 30 70 47.5t97 17.5q52 0 101.5 -18t94.5 -47l-54 -100q-25 22 -60 36.5t-68 14.5q-49 0 -70.5 -28t-21.5 -65v-70h282v-463h-134v360h-148v-360h-134v360h-128v-360h-134v360z
M356 463v52q0 4 0.5 12.5t1.5 18t3 17.5t4 13q-15 8 -33 11t-33 3q-20 0 -33.5 -8t-21.5 -20.5t-12 -29t-4 -32.5v-37h128z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="922" 
d="M798 -8q-32 0 -56.5 9t-42 24.5t-26.5 35.5t-9 43v408q0 19 -3.5 39t-13 36.5t-26 27.5t-43.5 11q-24 0 -40.5 -10t-27 -26.5t-15 -37.5t-4.5 -43v-46h87v-103h-87v-360h-134v360h-129v-360h-134v360h-68v103h68v35q0 45 12.5 83t36 65.5t56.5 42.5t75 15q35 0 66 -8
t62 -21q28 32 72.5 48.5t101.5 16.5q114 0 168 -54t54 -142v-373q0 -10 2.5 -20.5t8.5 -19t15 -14t22 -5.5q9 0 19 2t18.5 4.5t14.5 4.5t7 2l18 -108q-1 -1 -12 -5t-29 -8.5t-40 -8t-44 -3.5zM357 463v51q0 5 0.5 14t1 19t2 18t3.5 12q-15 8 -31 10.5t-30 2.5
q-39 0 -57 -25.5t-18 -61.5v-40h129z" />
    <glyph glyph-name="f_i" unicode="fi" horiz-adv-x="569" 
d="M94 360h-68v103h68v65q0 43 14.5 82t42 67.5t67 45.5t90.5 17q57 0 104.5 -18.5t89.5 -46.5l-54 -100q-26 23 -61 37t-71 14q-45 0 -66.5 -28t-21.5 -65v-70h281v-463h-134v360h-147v-360h-134v360z" />
    <glyph glyph-name="f_l" unicode="fl" horiz-adv-x="659" 
d="M660 17q-1 -1 -12 -5t-29 -8.5t-40.5 -8t-44.5 -3.5q-31 0 -55.5 9t-42 24.5t-26.5 35.5t-9 43v408q0 19 -3.5 39t-13 36.5t-26 27.5t-43.5 11q-24 0 -40.5 -10t-27 -26.5t-15 -37.5t-4.5 -43v-46h88v-103h-88v-360h-134v360h-68v103h68v77q0 97 58 148.5t161 51.5
q114 0 168 -54t54 -142v-373q0 -10 3 -20.5t9 -19t15 -14t22 -5.5q9 0 18.5 2t18 4.5t14.5 4.5t7 2z" />
    <glyph glyph-name=".notdef" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="239" 
 />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="981" 
d="M621 770l-86 26l54 119h125zM430 710h505v-121h-333v-167h279v-121h-279v-180h342v-121h-478v177h-228l-105 -177h-147zM456 297v258l-159 -258h159z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="255" 
d="M133 585l-86 26l54 119h125z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM373 770l-86 26l54 119h125z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM338 841q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="328" 
d="M164 656q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM184 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="369" 
d="M30 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM187 790v119h111v-119h-111zM379 790v119h111v-119h-111z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="457" 
d="M77 604v119h111v-119h-111zM269 604v119h111v-119h-111z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM211 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="255" 
d="M47 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM189 818v83h300v-83h-300z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="419" 
d="M60 633v83h300v-83h-300z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="675" 
d="M625 -196q-53 8 -79.5 32t-26.5 55q0 58 81 109h-73l-65 177h-249l-65 -177h-146l280 710h112l279 -710q-82 -48 -82 -90q0 -37 62 -40zM338 561l-102 -287h201z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="290" 
d="M171 -196q-53 8 -79.5 32t-26.5 55q0 33 29.5 66.5t89.5 63.5l35 -21q-82 -48 -82 -90q0 -37 62 -40l-28 -66v0z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM241 838q0 37 26.5 57t70.5 20t70.5 -20t26.5 -57t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM338 874q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10
t12.5 26q0 17 -12 26.5t-29 9.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="262" 
d="M34 653q0 37 26.5 57t70.5 20t70.5 -20t26.5 -57t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM131 689q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="675" 
d="M399 808q17 -10 26.5 -25.5t9.5 -36.5q0 -40 -32 -61l270 -685h-146l-65 177h-249l-65 -177h-146l270 686q-14 10 -22.5 25t-8.5 35q0 29 16 47t45 26l49 99h125zM338 561l-102 -287h201zM338 782q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5
q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201zM401 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5
t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="437" 
d="M284 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="680" 
d="M646 183q0 -45 -18 -79t-49.5 -57t-73 -35t-88.5 -12h-343v710h380q37 0 67 -16t51 -41.5t32.5 -58t11.5 -65.5q0 -52 -26 -97.5t-76 -67.5q61 -18 96.5 -64.5t35.5 -116.5zM507 209q0 19 -6.5 36.5t-17.5 30.5t-26 20.5t-33 7.5h-212v-187h205q19 0 35.5 7t28.5 19.5
t19 29.5t7 36zM212 592v-179h184q35 0 59.5 24t24.5 66q0 41 -22.5 65t-55.5 24h-190z" />
    <glyph glyph-name="B.sc" horiz-adv-x="596" 
d="M561 143q0 -35 -16 -62t-43 -45t-63 -27t-77 -9h-295v559h329q32 0 57.5 -12.5t43.5 -33t28 -46t10 -52.5q0 -40 -22.5 -75.5t-65.5 -52.5q53 -14 83.5 -50.5t30.5 -93.5zM439 168q0 27 -20 47t-51 20h-180v-133h174q32 0 54.5 18.5t22.5 47.5zM188 455v-125h157
q29 0 49.5 16t20.5 47q0 29 -19 45.5t-46 16.5h-162z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="670" 
d="M32 360q0 65 23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5t41 -76.5t63.5 -53t81 -20q23 0 47 6t46.5 19t41.5 33.5
t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-74 0 -135.5 32t-106 83.5t-69 117t-24.5 133.5z" />
    <glyph glyph-name="C.sc" horiz-adv-x="596" 
d="M32 284q0 51 20 101t58 89t93 63.5t126 24.5q84 0 144.5 -33.5t89.5 -88.5l-93 -65q-11 23 -27.5 38t-36 24t-40.5 13t-40 4q-43 0 -75 -15.5t-53.5 -40t-32 -56t-10.5 -62.5q0 -35 12.5 -67t35.5 -56.5t54.5 -39t70.5 -14.5q20 0 41.5 4.5t41 14t36 25t26.5 37.5l99 -59
q-14 -31 -41 -55.5t-60.5 -41t-71.5 -25t-75 -8.5q-64 0 -117.5 25t-92.5 66t-60.5 92.5t-21.5 105.5z" />
    <glyph glyph-name="A.sc" horiz-adv-x="593" 
d="M3 0l244 559h99l243 -559h-128l-56 131h-217l-56 -131h-129zM297 412l-82 -176h161z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="670" 
d="M406 770l-86 26l54 119h125zM32 360q0 65 23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5t41 -76.5t63.5 -53t81 -20
q23 0 47 6t46.5 19t41.5 33.5t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-74 0 -135.5 32t-106 83.5t-69 117t-24.5 133.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="670" 
d="M287 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM32 360q0 65 23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5
t41 -76.5t63.5 -53t81 -20q23 0 47 6t46.5 19t41.5 33.5t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-74 0 -135.5 32t-106 83.5t-69 117t-24.5 133.5z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="369" 
d="M100 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="671" 
d="M289 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l44 87q-69 5 -127 38t-99 84t-64 114.5t-23 128.5t23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5
t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5t41 -76.5t63.5 -53t81 -20q23 0 47 6t46.5 19t41.5 33.5t31 49.5l113 -65q-15 -36 -41 -64t-59 -49t-71 -33.5t-76 -16.5l-24 -38q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5
q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="326" 
d="M48 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l61 120l52 -16l-35 -54q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="670" 
d="M217 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM32 360q0 65 23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5
t41 -76.5t63.5 -53t81 -20q23 0 47 6t46.5 19t41.5 33.5t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-74 0 -135.5 32t-106 83.5t-69 117t-24.5 133.5z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="670" 
d="M32 360q0 65 23 128.5t66.5 113.5t106.5 81t144 31q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-49 0 -86 -21t-61.5 -54.5t-37 -76t-12.5 -84.5q0 -47 14.5 -90.5t41 -76.5t63.5 -53t81 -20q23 0 47 6t46.5 19t41.5 33.5
t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-74 0 -135.5 32t-106 83.5t-69 117t-24.5 133.5zM311 789v126h121v-126h-121z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="221" 
d="M50 604v126h121v-126h-121z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="718" 
d="M74 0v710h258q87 0 152.5 -28t109.5 -76t66 -112.5t22 -137.5q0 -81 -24.5 -146.5t-70.5 -112t-110.5 -72t-144.5 -25.5h-258zM543 356q0 51 -14 94t-41 74t-66.5 48t-89.5 17h-120v-468h120q51 0 90.5 18t66 49.5t40.5 74.5t14 93z" />
    <glyph glyph-name="D.sc" horiz-adv-x="633" 
d="M67 0v559h226q76 0 133 -22t95.5 -59.5t58 -88t19.5 -108.5q0 -64 -22 -116t-62 -88.5t-96.5 -56.5t-125.5 -20h-226zM476 281q0 76 -47.5 124t-135.5 48h-105v-347h105q45 0 79 13t57 36.5t35 55.5t12 70z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="718" 
d="M245 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM74 0v710h258q87 0 152.5 -28t109.5 -76t66 -112.5t22 -137.5q0 -81 -24.5 -146.5t-70.5 -112t-110.5 -72t-144.5 -25.5h-258zM543 356q0 51 -14 94t-41 74t-66.5 48t-89.5 17h-120v-468h120q51 0 90.5 18t66 49.5
t40.5 74.5t14 93z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="722" 
d="M79 0v307h-48v100h48v303h258q87 0 152.5 -28t109.5 -76t66 -112.5t22 -137.5q0 -81 -24.5 -146.5t-70.5 -112t-110.5 -72t-144.5 -25.5h-258zM548 356q0 51 -14 94t-41 74t-66.5 48t-89.5 17h-120v-182h139v-100h-139v-186h120q51 0 90.5 18t66 49.5t40.5 74.5t14 93z
" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="E.sc" horiz-adv-x="537" 
d="M498 106v-106h-431v559h423v-106h-302v-119h261v-99h-261v-129h310z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="604" 
d="M361 770l-86 26l54 119h125zM567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355zM326 841q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="604" 
d="M241 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="604" 
d="M171 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="604" 
d="M174 790v119h111v-119h-111zM366 790v119h111v-119h-111zM567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355zM266 789v126h121v-126h-121z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355zM198 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355zM177 818v83h300v-83h-300z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="749" 
d="M212 451v-451h-138v710h106l358 -460v459h137v-718q0 -42 -14.5 -76.5t-39.5 -59t-58 -38t-70 -13.5q-38 0 -72 11.5t-61 35.5l58 91q11 -11 26.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v42z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="604" 
d="M567 121v-121h-209q-82 -48 -82 -90q0 -37 62 -40l-28 -66q-53 8 -79.5 32t-26.5 55q0 58 81 109h-211v710h484v-121h-346v-171h299v-112h-299v-185h355z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="722" 
d="M79 0v307h-48v100h48v303h258q87 0 152.5 -28t109.5 -76t66 -112.5t22 -137.5q0 -81 -24.5 -146.5t-70.5 -112t-110.5 -72t-144.5 -25.5h-258zM548 356q0 51 -14 94t-41 74t-66.5 48t-89.5 17h-120v-182h139v-100h-139v-186h120q51 0 90.5 18t66 49.5t40.5 74.5t14 93z
" />
    <glyph glyph-name="Etilde" unicode="&#x1ebc;" horiz-adv-x="604" 
d="M567 121v-121h-493v710h484v-121h-346v-171h299v-112h-299v-185h355zM389 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17
t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="780" 
d="M126 436q11 55 38.5 105t69.5 88.5t98.5 61.5t125.5 23q95 0 164.5 -43t102.5 -112l-106 -73q-12 30 -31 50.5t-41.5 32.5t-46.5 17.5t-46 5.5q-38 0 -68.5 -12.5t-53.5 -34t-39 -50t-25 -59.5h259l-30 -73h-240q0 -31 3 -52h235l-30 -74h-182q25 -54 69.5 -87.5
t104.5 -33.5q23 0 47 6t46.5 19t41.5 33.5t31 49.5l113 -65q-16 -40 -46.5 -70.5t-69.5 -51.5t-82 -32t-85 -11q-57 0 -106.5 19.5t-90 52.5t-70.5 77t-47 94h-105l30 74h57q-3 24 -3 52h-71l30 73h49z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="571" 
d="M74 0v710h477v-121h-339v-183h282v-112h-282v-294h-138z" />
    <glyph glyph-name="F.sc" horiz-adv-x="506" 
d="M67 0v559h417v-106h-296v-130h247v-99h-247v-224h-121z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="709" 
d="M550 80q-82 -85 -189 -85q-68 0 -128 30t-104.5 80.5t-70 116.5t-25.5 137q0 73 25.5 137.5t71 113t107 77t133.5 28.5q97 0 166 -42t104 -113l-103 -76q-26 53 -71.5 81t-100.5 28q-44 0 -79.5 -19t-60.5 -52t-39 -76t-14 -91q0 -50 15 -93.5t42 -75.5t63.5 -50.5
t80.5 -18.5q98 0 177 97v50h-143v101h257v-365h-114v80z" />
    <glyph glyph-name="G.alt" horiz-adv-x="722" 
d="M382 -5q-77 0 -141.5 30.5t-111 81.5t-72 116.5t-25.5 135.5q0 69 24.5 133t69.5 113.5t108.5 79.5t142.5 30q91 0 160.5 -39.5t111.5 -109.5l-109 -86q-8 15 -22.5 34.5t-35.5 37t-49 29.5t-64 12q-47 0 -84 -20.5t-62 -54t-38 -76.5t-13 -87q0 -42 12.5 -84.5t37 -76.5
t61.5 -55.5t86 -21.5q34 0 64.5 11.5t54 31t40 44t23.5 51.5h-156v120h305v-11t-0.5 -22t-0.5 -22.5v-11.5q-3 -63 -26.5 -120t-65 -100t-99 -68t-126.5 -25z" />
    <glyph glyph-name="G.sc" horiz-adv-x="622" 
d="M478 57q-70 -61 -162 -61q-59 0 -110.5 23.5t-90 63.5t-60.5 91.5t-22 108.5q0 58 22 109t61 89t92.5 60t115.5 22q84 0 144 -33t90 -89l-92 -68q-21 39 -60 60.5t-86 21.5q-37 0 -67.5 -14t-52 -38t-33.5 -56t-12 -68q0 -38 13 -70t36 -55t54.5 -36.5t68.5 -13.5
q83 0 151 71v27h-124v88h224v-290h-100v57z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="709" 
d="M550 80q-82 -85 -189 -85q-68 0 -128 30t-104.5 80.5t-70 116.5t-25.5 137q0 73 25.5 137.5t71 113t107 77t133.5 28.5q97 0 166 -42t104 -113l-103 -76q-26 53 -71.5 81t-100.5 28q-44 0 -79.5 -19t-60.5 -52t-39 -76t-14 -91q0 -50 15 -93.5t42 -75.5t63.5 -50.5
t80.5 -18.5q98 0 177 97v50h-143v101h257v-365h-114v80zM369 841q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="709" 
d="M214 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM550 80q-82 -85 -189 -85q-68 0 -128 30t-104.5 80.5t-70 116.5t-25.5 137q0 73 25.5 137.5t71 113t107 77t133.5 28.5q97 0 166 -42t104 -113l-103 -76q-26 53 -71.5 81t-100.5 28q-44 0 -79.5 -19t-60.5 -52
t-39 -76t-14 -91q0 -50 15 -93.5t42 -75.5t63.5 -50.5t80.5 -18.5q98 0 177 97v50h-143v101h257v-365h-114v80z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="709" 
d="M280 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM550 80q-82 -85 -189 -85q-68 0 -128 30t-104.5 80.5t-70 116.5t-25.5 137q0 73 25.5 137.5t71 113t107 77t133.5 28.5q97 0 166 -42t104 -113l-103 -76q-26 53 -71.5 81t-100.5 28
q-44 0 -79.5 -19t-60.5 -52t-39 -76t-14 -91q0 -50 15 -93.5t42 -75.5t63.5 -50.5t80.5 -18.5q98 0 177 97v50h-143v101h257v-365h-114v80z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="201" 
d="M32 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="709" 
d="M550 80q-82 -85 -189 -85q-68 0 -128 30t-104.5 80.5t-70 116.5t-25.5 137q0 73 25.5 137.5t71 113t107 77t133.5 28.5q97 0 166 -42t104 -113l-103 -76q-26 53 -71.5 81t-100.5 28q-44 0 -79.5 -19t-60.5 -52t-39 -76t-14 -91q0 -50 15 -93.5t42 -75.5t63.5 -50.5
t80.5 -18.5q98 0 177 97v50h-143v101h257v-365h-114v80zM308 789v126h121v-126h-121z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="745" 
d="M671 710v-710h-137v303h-322v-303h-138v710h138v-287h322v287h137z" />
    <glyph glyph-name="H.sc" horiz-adv-x="656" 
d="M588 559v-559h-120v232h-280v-232h-121v559h121v-220h280v220h120z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="752" 
d="M675 710v-101h49v-66h-49v-543h-137v303h-322v-303h-138v543h-50v66h50v101h138v-101h322v101h137zM538 423v120h-322v-120h322z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="745" 
d="M221 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM671 710v-710h-137v303h-322v-303h-138v710h138v-287h322v287h137z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138z" />
    <glyph glyph-name="I.sc" horiz-adv-x="255" 
d="M67 0v559h121v-559h-121z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="781" 
d="M74 0v709h138v-709h-138zM328 145q14 -10 47 -22t76 -12q42 0 67 14.5t38 44.5t17 74.5t4 104.5v360h138v-360q0 -81 -8.5 -147.5t-36 -113.5t-79.5 -73t-140 -26q-90 0 -154 44z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="495" 
d="M42 145q14 -10 47 -22t76 -12q42 0 67 14.5t38 44.5t17 74.5t4 104.5v360h138v-360q0 -81 -8.5 -147.5t-36 -113.5t-79.5 -73t-140 -26q-90 0 -154 44z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM177 770l-86 26l54 119h125z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM142 841q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM-12 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM-9 790v119h111v-119h-111zM183 790v119h111v-119h-111z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM82 789v126h121v-126h-121z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM15 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM-6 818v83h300v-83h-300z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="286" 
d="M164 -196q-53 8 -79.5 32t-26.5 55q0 58 81 109h-65v709h138v-709q-82 -48 -82 -90q0 -37 62 -40l-28 -66v0z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="286" 
d="M74 0v709h138v-709h-138zM206 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43
t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="J.sc" horiz-adv-x="440" 
d="M41 124q12 -6 41 -15.5t67 -9.5q36 0 58 10.5t33 32t14.5 55t3.5 78.5v284h121v-284q0 -64 -7 -116.5t-31 -89.5t-69.5 -57t-122.5 -20q-80 0 -135 33z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="495" 
d="M207 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM42 145q14 -10 47 -22t76 -12q42 0 67 14.5t38 44.5t17 74.5t4 104.5v360h138v-360q0 -81 -8.5 -147.5t-36 -113.5t-79.5 -73t-140 -26q-90 0 -154 44z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="667" 
d="M74 0v709h138v-346l307 347h143l-272 -315l289 -395h-146l-226 317l-95 -103v-214h-138z" />
    <glyph glyph-name="K.sc" horiz-adv-x="593" 
d="M67 0v559h121v-261l267 261h127l-238 -249l252 -310h-127l-198 242l-83 -74v-168h-121z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="667" 
d="M263 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM74 0v709h138v-346l307 347h143l-272 -315l289 -395h-146l-226 317l-95 -103v-214h-138z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="589" 
d="M74 0v710h138v-589h362v-121h-500z" />
    <glyph glyph-name="L.sc" horiz-adv-x="491" 
d="M67 0v559h121v-453h286v-106h-407z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="589" 
d="M393 770l-86 26l54 119h125zM74 0v710h138v-589h362v-121h-500z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="590" 
d="M413 585l-86 26l54 119h125zM74 0v710h138v-589h362v-121h-500z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="255" 
d="M133 585l-86 26l54 119h125z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="589" 
d="M247 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM74 0v710h138v-589h362v-121h-500z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="590" 
d="M74 0v710h138v-589h362v-121h-500zM365 331v151h110v-151h-110z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="224" 
d="M57 242v151h110v-151h-110z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="599" 
d="M84 0v275l-52 -33l-34 73l86 54v341h138v-254l109 69l33 -73l-142 -90v-241h362v-121h-500z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="865" 
d="M653 0v470l-183 -350h-75l-183 350v-470h-138v710h148l210 -404l212 404h147v-710h-138z" />
    <glyph glyph-name="M.sc" horiz-adv-x="760" 
d="M573 0v349l-160 -256h-66l-159 256v-349h-121v559h131l183 -302l183 302h129v-559h-120z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="763" 
d="M212 455v-455h-138v710h107l370 -466v465h138v-709h-112z" />
    <glyph glyph-name="N.sc" horiz-adv-x="671" 
d="M188 335v-335h-121v559h94l322 -344v344h121v-559h-98z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="675" 
d="M2 0l280 710h112l279 -710h-146l-65 177h-249l-65 -177h-146zM338 561l-102 -287h201z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="763" 
d="M416 770l-86 26l54 119h125zM212 455v-455h-138v710h107l370 -466v465h138v-709h-112z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="763" 
d="M297 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM212 455v-455h-138v710h107l370 -466v465h138v-709h-112z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="763" 
d="M292 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM212 455v-455h-138v710h107l370 -466v465h138v-709h-112z" />
    <glyph glyph-name="Ndotaccent" unicode="&#x1e44;" horiz-adv-x="763" 
d="M321 789v126h121v-126h-121zM212 455v-455h-138v710h107l370 -466v465h138v-709h-112z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="763" 
d="M212 455v-455h-138v710h107l370 -466v465h138v-709h-112zM444 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74
q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="740" 
d="M369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5
t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="O.sc" horiz-adv-x="653" 
d="M326 -4q-66 0 -120 24t-92.5 64t-60 91t-21.5 105q0 55 22.5 106t62 90t93.5 62.5t118 23.5q66 0 120.5 -24.5t92.5 -64.5t59 -91.5t21 -104.5q0 -55 -22 -106t-61 -90t-93.5 -62t-118.5 -23zM155 279q0 -34 11.5 -65.5t34 -56t54.5 -39.5t73 -15t73 15.5t53.5 40.5
t32.5 56.5t11 64.5q0 34 -12 65.5t-34 55.5t-53.5 38.5t-71.5 14.5q-41 0 -73 -15.5t-54 -40t-33.5 -56t-11.5 -63.5z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1090" 
d="M1053 121v-121h-479v105q-34 -48 -87 -79t-117 -31q-75 0 -137.5 30.5t-106.5 81t-68.5 115.5t-24.5 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q65 0 117 -31t85 -78v104h470v-121h-333v-167h279v-121h-279v-180h342zM371 117q48 0 84.5 20.5t61.5 54.5t38 77t13 87
q0 46 -14 88.5t-39.5 75.5t-62 53t-82.5 20q-48 0 -84.5 -20.5t-62 -54.5t-38.5 -76.5t-13 -86.5q0 -46 13.5 -89t39 -76t62.5 -53t84 -20z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="740" 
d="M407 770l-86 26l54 119h125zM369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76
t62.5 -53t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="740" 
d="M369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5
t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5zM372 841q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="740" 
d="M218 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355
q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="740" 
d="M220 790v119h111v-119h-111zM412 790v119h111v-119h-111zM369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355
q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="740" 
d="M369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5
t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5zM244 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="740" 
d="M309 771l-66 26l48 119h104zM469 771l-65 26l47 119h105zM369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355
q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="366" 
d="M99 585l-66 26l48 119h104zM259 585l-65 26l47 119h105z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="740" 
d="M223 818v83h300v-83h-300zM369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76t62.5 -53
t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="Oogonek" unicode="&#x1ea;" horiz-adv-x="740" 
d="M353 -197q-53 8 -79.5 32t-26.5 55q0 27 19.5 54.5t59.5 53.5q-66 9 -120.5 42t-92.5 82t-59 109.5t-21 123.5q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132t-23 -129t-64 -110.5t-98 -79.5t-125 -37q-78 -47 -78 -88
q0 -37 62 -40zM172 355q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="742" 
d="M693 710l-77 -110q44 -51 67.5 -115.5t23.5 -131.5q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5q-82 0 -150 37l-22 -32h-148l75 108q-44 51 -67.5 114.5t-23.5 132.5q0 71 25.5 136t71 115t107.5 79.5t135 29.5q42 0 79 -10t70 -28l23 33h149zM172 355
q0 -74 32 -133l245 352q-17 8 -37 13t-42 5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5zM568 355q0 72 -32 131l-245 -352q36 -17 80 -17q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="698" 
d="M672 710l-496 -710h-148l495 710h149z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="742" 
d="M693 710l-77 -110q44 -51 67.5 -115.5t23.5 -131.5q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5q-82 0 -150 37l-22 -32h-148l75 108q-44 51 -67.5 114.5t-23.5 132.5q0 71 25.5 136t71 115t107.5 79.5t135 29.5q42 0 79 -10t70 -28l23 33h149zM172 355
q0 -74 32 -133l245 352q-17 8 -37 13t-42 5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5zM568 355q0 72 -32 131l-245 -352q36 -17 80 -17q48 0 84.5 20.5t61.5 54.5t38 76.5t13 86.5zM407 770l-86 26l54 119h125z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="740" 
d="M369 -5q-75 0 -137 30.5t-106.5 81t-68.5 115.5t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -71 -25.5 -135.5t-70.5 -114t-107 -79t-135 -29.5zM172 355q0 -46 13.5 -89t39 -76t62.5 -53t84 -20q48 0 84.5 20.5
t61.5 54.5t38 76.5t13 86.5q0 46 -14 89t-39.5 76t-62 52.5t-82.5 19.5q-48 0 -84.5 -20.5t-62 -54t-38.5 -76t-13 -86.5zM435 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5
q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="621" 
d="M74 0v710h301q49 0 90.5 -20.5t71.5 -54t47 -76t17 -85.5q0 -45 -16 -87.5t-45 -75.5t-70 -53t-90 -20h-168v-238h-138zM212 359h160q38 0 63.5 31t25.5 84q0 27 -8 48.5t-21 36.5t-30.5 22.5t-35.5 7.5h-154v-230z" />
    <glyph glyph-name="P.sc" horiz-adv-x="549" 
d="M67 0v559h262q43 0 80 -16.5t63 -43t41 -60.5t15 -68q0 -36 -14 -70t-39.5 -60t-61 -42t-78.5 -16h-147v-183h-121zM188 289h139q34 0 56 21t22 61q0 41 -25.5 61.5t-57.5 20.5h-134v-164z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="742" 
d="M547 48q-38 -25 -82.5 -39t-95.5 -14q-75 0 -137 31t-106.5 81t-68.5 115t-24 133q0 71 25.5 136t71 115t107.5 79.5t135 29.5q75 0 137 -31.5t106 -82.5t68 -116t24 -132q0 -68 -22.5 -129.5t-64.5 -110.5l101 -113h-131zM371 117q51 0 92 25l-107 121h131l44 -50
q37 65 37 142q0 46 -14 89t-39.5 76t-62 53t-82.5 20q-48 0 -84.5 -20.5t-62 -54.5t-38.5 -76.5t-13 -86.5q0 -46 13.5 -89t39 -76t62.5 -53t84 -20z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="655" 
d="M482 38q-33 -20 -72 -31t-84 -11q-66 0 -120 24t-92.5 64t-60 91t-21.5 105q0 55 22.5 106.5t62 90.5t93.5 62.5t118 23.5q66 0 120.5 -24.5t92.5 -65t59 -92t21 -104.5q0 -52 -19.5 -100t-54.5 -86l87 -91h-115zM328 104q43 0 75 17l-89 94h115l38 -40q31 48 31 105
q0 34 -12 65.5t-34 56t-53.5 39t-71.5 14.5q-41 0 -73 -15.5t-54 -40.5t-33.5 -56.5t-11.5 -63.5q0 -34 11.5 -65.5t34 -56t54.5 -39t73 -14.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="674" 
d="M74 0v710h314q49 0 90.5 -20.5t71.5 -54t47 -76t17 -85.5q0 -69 -34.5 -125.5t-93.5 -81.5l167 -267h-156l-149 238h-136v-238h-138zM212 359h173q19 0 35.5 9t28.5 24.5t19 36.5t7 45q0 25 -8 46t-21.5 36.5t-31 24t-35.5 8.5h-167v-230z" />
    <glyph glyph-name="R.sc" horiz-adv-x="595" 
d="M67 0v559h275q43 0 79 -16.5t62.5 -43t41 -60.5t14.5 -68q0 -53 -30 -97t-81 -65l146 -209h-138l-130 183h-118v-183h-121zM188 289h151q33 0 55.5 23.5t22.5 58.5q0 18 -7 33t-18.5 26t-26.5 17t-31 6h-146v-164z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="674" 
d="M354 771l-86 26l54 119h125zM74 0v710h314q49 0 90.5 -20.5t71.5 -54t47 -76t17 -85.5q0 -69 -34.5 -125.5t-93.5 -81.5l167 -267h-156l-149 238h-136v-238h-138zM212 359h173q19 0 35.5 9t28.5 24.5t19 36.5t7 45q0 25 -8 46t-21.5 36.5t-31 24t-35.5 8.5h-167v-230z
" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="674" 
d="M234 916l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM74 0v710h314q49 0 90.5 -20.5t71.5 -54t47 -76t17 -85.5q0 -69 -34.5 -125.5t-93.5 -81.5l167 -267h-156l-149 238h-136v-238h-138zM212 359h173q19 0 35.5 9t28.5 24.5t19 36.5t7 45q0 25 -8 46t-21.5 36.5
t-31 24t-35.5 8.5h-167v-230z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="674" 
d="M253 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM74 0v710h314q49 0 90.5 -20.5t71.5 -54t47 -76t17 -85.5q0 -69 -34.5 -125.5t-93.5 -81.5l167 -267h-156l-149 238h-136v-238h-138zM212 359h173q19 0 35.5 9t28.5 24.5t19 36.5t7 45
q0 25 -8 46t-21.5 36.5t-31 24t-35.5 8.5h-167v-230z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="615" 
d="M498 524q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5q0 -57 -21.5 -97.5t-57.5 -65.5t-84 -36.5t-100 -11.5q-80 0 -158 24t-140 68l61 119
q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5q75 0 138 -23.5t110 -55.5z" />
    <glyph glyph-name="S.sc" horiz-adv-x="536" 
d="M429 402q-7 6 -22.5 15.5t-37 18t-48 14.5t-56.5 6q-43 0 -66.5 -13.5t-23.5 -39.5q0 -16 9.5 -26.5t27 -18.5t42.5 -14.5t57 -13.5q44 -10 80 -23t61 -32t38.5 -46t13.5 -66q0 -47 -18.5 -79t-49.5 -52.5t-71.5 -29.5t-85.5 -9q-68 0 -134.5 19t-119.5 52l55 105
q10 -10 29.5 -22t46.5 -22t59 -16.5t66 -6.5q93 0 93 50q0 15 -9 25t-26 18.5t-42 15.5t-58 15q-50 13 -85.5 27t-58.5 32.5t-34 43t-11 57.5q0 43 17.5 76.5t47.5 55.5t70 33.5t85 11.5q64 0 118.5 -19t93.5 -43z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="615" 
d="M357 778l-86 26l54 119h125zM498 524q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5q0 -57 -21.5 -97.5t-57.5 -65.5t-84 -36.5t-100 -11.5q-80 0 -158 24
t-140 68l61 119q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5q75 0 138 -23.5t110 -55.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="615" 
d="M238 923l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM498 524q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5q0 -57 -21.5 -97.5t-57.5 -65.5t-84 -36.5
t-100 -11.5q-80 0 -158 24t-140 68l61 119q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5q75 0 138 -23.5t110 -55.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="615" 
d="M168 822l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM498 524q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5q0 -57 -21.5 -97.5t-57.5 -65.5t-84 -36.5
t-100 -11.5q-80 0 -158 24t-140 68l61 119q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5q75 0 138 -23.5t110 -55.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="620" 
d="M605 589h-226v-589h-138v589h-227v121h591v-121z" />
    <glyph glyph-name="T.sc" horiz-adv-x="552" 
d="M534 453h-197v-453h-121v453h-198v106h516v-106z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="649" 
d="M620 589h-226v-210h189v-66h-189v-313h-138v313h-190v66h190v210h-227v121h591v-121z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="620" 
d="M231 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM605 589h-226v-589h-138v589h-227v121h591v-121z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="606" 
d="M358 582q49 0 90.5 -20.5t71.5 -53.5t47 -75.5t17 -86.5q0 -46 -16 -88.5t-45 -75.5t-69.5 -52.5t-89.5 -19.5h-152v-110h-138v710h138v-128h146zM357 231q19 0 35.5 9t28.5 25t19 36.5t7 44.5q0 25 -8 46t-21.5 36.5t-30.5 24t-36 8.5h-139v-230h145z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="U.sc" horiz-adv-x="648" 
d="M323 103q42 0 70.5 14.5t45.5 38.5t24 54.5t7 62.5v286h121v-286q0 -58 -15.5 -108.5t-47.5 -88t-83 -59t-121 -21.5q-73 0 -123.5 22.5t-82.5 61t-46.5 88.5t-14.5 105v286h121v-286q0 -33 7.5 -63.5t24.5 -54.5t44.5 -38t68.5 -14z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="748" 
d="M409 770l-86 26l54 119h125zM373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5zM374 841q23 0 40 21t20 53h71
q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="748" 
d="M219 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5
t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="748" 
d="M222 790v119h111v-119h-111zM414 790v119h111v-119h-111zM373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5
t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5zM246 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5zM310 771l-66 26l48 119h104zM470 771
l-65 26l47 119h105z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5zM225 818v83h300v-83h-300z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -70 -16 -132t-50.5 -109.5t-88 -77t-128.5 -33.5q-40 -23 -58.5 -45t-18.5 -42q0 -37 62 -40l-28 -66q-53 8 -79.5 32t-26.5 55q0 27 19.5 54.5t59.5 53.5q-73 7 -124 38.5t-83 79.5t-46.5 107.5
t-14.5 124.5v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="748" 
d="M276 838q0 37 26.5 57t70.5 20t70.5 -20t26.5 -57t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM373 874q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5zM373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137
v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="748" 
d="M373 117q49 0 82.5 20t54 52t29 73.5t8.5 85.5v362h137v-362q0 -73 -17.5 -137t-55 -112t-96.5 -76t-141 -28q-85 0 -144.5 29.5t-96.5 78t-53.5 112.5t-16.5 133v362h138v-362q0 -45 8.5 -86.5t29 -73.5t53.5 -51.5t81 -19.5zM437 787q-23 0 -40 7t-31.5 15.5t-29 15.5
t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="673" 
d="M145 710l192 -537l190 537h145l-277 -710h-116l-279 710h145z" />
    <glyph glyph-name="V.sc" horiz-adv-x="595" 
d="M131 559l167 -389l165 389h127l-242 -559h-102l-243 559h128z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1053" 
d="M319 708h127l80 -235l80 235h128l-121 -323l90 -227l201 552h150l-289 -710h-116l-123 298l-123 -298h-116l-288 710h149l202 -552l88 227z" />
    <glyph glyph-name="W.alt" horiz-adv-x="1101" 
d="M1096 710l-223 -710h-142l-182 485l-178 -485h-143l-222 710h155l152 -504l161 504h149l166 -503l151 503h156z" />
    <glyph glyph-name="W.sc" horiz-adv-x="928" 
d="M282 557h112l69 -181l70 181h112l-105 -257l73 -155l180 414h132l-252 -559h-102l-107 225l-107 -225h-102l-252 559h131l181 -414l71 155z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1053" 
d="M560 770l-86 26l54 119h125zM319 708h127l80 -235l80 235h128l-121 -323l90 -227l201 552h150l-289 -710h-116l-123 298l-123 -298h-116l-288 710h149l202 -552l88 227z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1053" 
d="M371 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM319 708h127l80 -235l80 235h128l-121 -323l90 -227l201 552h150l-289 -710h-116l-123 298l-123 -298h-116l-288 710h149l202 -552l88 227z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1053" 
d="M374 790v119h111v-119h-111zM566 790v119h111v-119h-111zM319 708h127l80 -235l80 235h128l-121 -323l90 -227l201 552h150l-289 -710h-116l-123 298l-123 -298h-116l-288 710h149l202 -552l88 227z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1053" 
d="M319 708h127l80 -235l80 235h128l-121 -323l90 -227l201 552h150l-289 -710h-116l-123 298l-123 -298h-116l-288 710h149l202 -552l88 227zM398 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="644" 
d="M146 710l177 -264l176 264h149l-253 -360l245 -350h-149l-168 254l-169 -254h-150l245 350l-253 360h150z" />
    <glyph glyph-name="X.sc" horiz-adv-x="577" 
d="M135 559l154 -198l153 198h132l-222 -283l215 -276h-131l-147 190l-147 -190h-132l214 276l-221 283h132z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="569" 
d="M131 559l153 -261l155 261h132l-226 -366v-193h-121v194l-223 365h130z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150zM356 770l-86 26l54 119h125z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150zM167 814l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150zM170 790v119h111v-119h-111zM362 790v119h111v-119h-111z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150zM194 916h124l55 -119l-86 -26z" />
    <glyph glyph-name="Ytilde" unicode="&#x1ef8;" horiz-adv-x="644" 
d="M146 710l175 -342l178 342h149l-258 -462v-248h-137v250l-257 460h150zM384 787q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16
t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="623" 
d="M28 104l401 485h-390v121h549v-104l-388 -485h391v-121h-563v104z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="560" 
d="M32 91l349 362h-339v106h480v-91l-338 -362h340v-106h-492v91z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="623" 
d="M359 770l-86 26l54 119h125zM28 104l401 485h-390v121h549v-104l-388 -485h391v-121h-563v104z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="623" 
d="M239 915l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM28 104l401 485h-390v121h549v-104l-388 -485h391v-121h-563v104z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="623" 
d="M28 104l401 485h-390v121h549v-104l-388 -485h391v-121h-563v104zM264 789v126h121v-126h-121z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5z" />
    <glyph glyph-name="a.alt" horiz-adv-x="634" 
d="M277 -10q-55 0 -100.5 21t-78.5 58t-51 86.5t-18 106.5t18 106.5t49.5 86t74.5 57.5t93 21q59 0 104.5 -27.5t71.5 -74.5v86h134v-517h-133v85q-26 -45 -66.5 -70t-97.5 -25zM441 316q-18 46 -54.5 74.5t-80.5 28.5q-31 0 -56.5 -13.5t-44 -36t-28.5 -51t-10 -59.5
q0 -32 10.5 -60.5t30 -49.5t45.5 -33t58 -12q45 0 77.5 24t52.5 64v124z" />
    <glyph glyph-name="a.alt2" horiz-adv-x="564" 
d="M381 0v73q-30 -42 -76 -62.5t-101 -20.5q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 40 18 71.5t49 53.5t71 33t85 11q42 0 75 -7t53 -16v30q0 51 -29.5 79.5t-87.5 28.5q-42 0 -82 -15t-82 -44l-41 85q48 32 102.5 49.5t115.5 17.5q115 0 176.5 -56.5t61.5 -159.5
v-317h-129zM354 132q22 17 22 38v60q-25 10 -53 15t-53 5q-22 0 -43.5 -5t-38 -15.5t-26.5 -26t-10 -36.5q0 -35 26.5 -57t63.5 -22q30 0 61.5 10.5t50.5 33.5z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM307 585l-86 26l54 119h125z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM272 656q23 0 40 21t20 53h71q0 -29 -10 -53.5
t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM118 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63z
" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM121 604v119h111v-119h-111zM313 604v119h111v-119
h-111z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="938" 
d="M205 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q32 0 63 -5t55 -15q1 9 3 22.5t5 21.5q-9 91 -115 91q-42 0 -82 -15t-83 -44l-40 85q101 67 218 67q62 0 108 -19.5t75 -54.5q33 34 80 54t106 20q64 0 115 -22t86.5 -59
t54.5 -86t19 -104q0 -11 -0.5 -23t-2.5 -20h-410q2 -31 14.5 -55t32 -41t45 -26t52.5 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5t-151.5 -38.5q-75 0 -132 29.5t-92 78.5q-18 -29 -43.5 -49t-54 -33.5t-59 -19.5t-57.5 -6zM243 88q34 0 64.5 12t48.5 32
q9 8 15 17.5t6 18.5q-2 4 -4 13t-3 18.5l-2 19t-1 14.5q-22 8 -47.5 12.5t-48.5 4.5q-52 0 -85.5 -23.5t-33.5 -59.5q0 -34 26.5 -56.5t64.5 -22.5zM779 302q-5 60 -43.5 97t-95.5 37t-95 -37t-43 -97h277z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="938" 
d="M490 585l-86 26l54 119h125zM205 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q32 0 63 -5t55 -15q1 9 3 22.5t5 21.5q-9 91 -115 91q-42 0 -82 -15t-83 -44l-40 85q101 67 218 67q62 0 108 -19.5t75 -54.5q33 34 80 54
t106 20q64 0 115 -22t86.5 -59t54.5 -86t19 -104q0 -11 -0.5 -23t-2.5 -20h-410q2 -31 14.5 -55t32 -41t45 -26t52.5 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5t-151.5 -38.5q-75 0 -132 29.5t-92 78.5q-18 -29 -43.5 -49t-54 -33.5t-59 -19.5t-57.5 -6z
M243 88q34 0 64.5 12t48.5 32q9 8 15 17.5t6 18.5q-2 4 -4 13t-3 18.5l-2 19t-1 14.5q-22 8 -47.5 12.5t-48.5 4.5q-52 0 -85.5 -23.5t-33.5 -59.5q0 -34 26.5 -56.5t64.5 -22.5zM779 302q-5 60 -43.5 97t-95.5 37t-95 -37t-43 -97h277z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM145 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM123 633v83h300v-83h-300z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="716" 
d="M536 0l-56 59q-44 -34 -95 -51.5t-105 -17.5q-55 0 -99 17t-75 46t-48 67.5t-17 80.5q0 33 10 61t26.5 52t39 44.5t46.5 38.5q-21 24 -35 43.5t-23 37.5t-13 35t-4 36q0 38 14.5 69.5t40.5 54t61.5 34.5t77.5 12q37 0 71 -10t60.5 -29.5t42 -49t15.5 -68.5
q0 -58 -34 -101.5t-87 -81.5l137 -143q15 32 23.5 70.5t8.5 82.5h109q-1 -71 -17.5 -129.5t-45.5 -105.5l147 -154h-176zM294 95q62 0 114 40l-164 174q-34 -25 -54 -51t-20 -58q0 -21 9 -40.5t25.5 -33.5t39.5 -22.5t50 -8.5zM210 553q0 -17 12 -34.5t45 -52.5
q39 25 60.5 45.5t21.5 48.5q0 27 -19 43.5t-48 16.5q-32 0 -52 -19.5t-20 -47.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="578" 
d="M498 -196q-53 8 -79.5 32t-26.5 55q0 55 73 104q-29 4 -45 19.5t-21 40.5l-3 29q-35 -46 -85 -70t-107 -24q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15
t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114q-82 -48 -82 -90q0 -37 62 -40zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5z" />
    <glyph glyph-name="apostrophe" unicode="&#x2bc;" horiz-adv-x="376" 
d="M201 585l-85 26l34 73l-7 46h151z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM175 653q0 37 26.5 57t70.5 20t70.5 -20t26.5 -57
t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM272 689q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM307 755l-86 26l54 119h125zM175 653q0 37 26.5 57
t70.5 20t70.5 -20t26.5 -57t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM272 689q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="587" 
d="M40 303l198 407h112l197 -407h-110l-143 294l-146 -294h-108z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="562" 
d="M54 228q0 9 7 30.5t23 44t43 40t66 17.5q27 0 49.5 -8.5t43.5 -18.5t41 -18.5t42 -8.5q21 0 34.5 7.5t21 16.5t10.5 17.5t3 10.5h74q0 -5 -7 -26t-23.5 -44t-44.5 -41.5t-69 -18.5q-27 0 -50 8t-44 18t-41 18t-41 8q-20 0 -32.5 -8t-19.5 -17.5t-9.5 -17.5t-2.5 -9h-74v0
z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="345" 
d="M75 520l47 71l-71 22l18 58l73 -31l-5 77h71l-5 -77l72 31l18 -58l-70 -22l47 -71l-52 -34l-45 75l-46 -75z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="836" 
d="M423 603q74 0 142 -25t119.5 -73t82 -119t30.5 -163q0 -22 -4 -62t-19.5 -79.5t-46.5 -68.5t-85 -29q-27 0 -44.5 7.5t-28 19.5t-15.5 26.5t-7 28.5q-31 -31 -74.5 -51t-97.5 -20q-77 0 -116.5 41t-39.5 101q0 42 17 70t44.5 45.5t61.5 25t68 7.5q42 0 71 -8t44 -15
q0 27 -5 50t-18 39t-35.5 25t-56.5 9q-41 0 -74.5 -12t-58.5 -32l-25 71q74 50 172 50q64 0 100.5 -20t54.5 -51t22 -67.5t4 -70.5v-96q0 -23 1.5 -43t6.5 -36t15 -25t28 -9q27 0 43.5 20.5t25.5 49t12.5 60t3.5 53.5q0 68 -23.5 127t-66 102.5t-102 68t-131.5 24.5
q-70 0 -128.5 -24t-100.5 -66.5t-65.5 -101t-23.5 -127.5q0 -67 22 -125.5t62.5 -102t97.5 -69t128 -25.5q47 0 82 10.5t72 27.5l17 -52q-42 -20 -86 -30t-89 -10q-75 0 -141 27.5t-115.5 77t-78 119t-28.5 153.5q0 88 31.5 157.5t84 117t121 72.5t143.5 25zM470 90
q31 15 42.5 34t11.5 45v30q-14 5 -44 13t-68 8q-48 0 -79.5 -17.5t-31.5 -57.5q0 -29 19.5 -53t62.5 -24q22 0 45 6.5t42 15.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="578" 
d="M204 -10q-38 0 -71 12.5t-57 35t-37.5 52.5t-13.5 66q0 37 16.5 68.5t46 53.5t70.5 34.5t90 12.5q35 0 68.5 -6t59.5 -17v30q0 52 -29.5 80t-87.5 28q-42 0 -82 -15t-82 -44l-41 85q101 67 218 67q113 0 175.5 -55.5t62.5 -160.5v-163q0 -21 8 -30t28 -10v-114
q-19 -4 -35.5 -5.5t-28.5 -1.5q-38 1 -58 17.5t-25 44.5l-3 29q-35 -46 -85 -70t-107 -24zM242 88q34 0 64.5 12t47.5 32q22 17 22 38v60q-24 9 -52 14.5t-54 5.5q-52 0 -85 -23.5t-33 -59.5q0 -34 26 -56.5t64 -22.5zM335 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7
q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="633" 
d="M358 -10q-60 0 -107 27t-74 74v-91h-117v730h134v-298q28 48 73.5 74.5t105.5 26.5q51 0 93.5 -22t73 -59t47 -87t16.5 -105q0 -57 -18.5 -106t-51.5 -85.5t-78 -57.5t-97 -21zM321 104q32 0 58.5 12.5t46 33.5t30 49t10.5 60t-10 61t-28.5 51t-43.5 35t-55 13
q-45 0 -80.5 -29.5t-54.5 -72.5v-125q7 -20 20.5 -36t30.5 -27.5t37 -18t39 -6.5z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="638" 
d="M175 710l435 -710h-148l-436 710h149z" />
    <glyph glyph-name="bar" unicode="|" 
d="M69 -130v905h114v-905h-114z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="306" 
d="M210 633v-204q0 -20 -9.5 -37.5t-25.5 -31.5q15 -14 25 -31.5t10 -37.5v-215h55v-107h-153q-7 0 -15 6t-8 24v248q0 26 -15 44.5t-35 22.5v94q10 0 19.5 6t16 16t10.5 21t4 22v237q0 19 9 24.5t14 5.5h153v-107h-55z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="306" 
d="M42 633v107h153q5 0 14 -5.5t9 -24.5v-237q0 -11 4 -22t10.5 -21t15.5 -16t20 -6v-94q-20 -4 -35 -22.5t-15 -44.5v-248q0 -18 -8.5 -24t-14.5 -6h-153v107h55v215q0 20 9.5 37.5t25.5 31.5q-16 14 -25.5 31.5t-9.5 37.5v204h-55z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="301" 
d="M71 -40v780h188v-107h-64v-566h64v-107h-188z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="301" 
d="M42 -40v107h63v566h-63v107h187v-780h-187z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="257" 
d="M185 264v-394h-114v394h114zM185 775v-394h-114v394h114z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="363" 
d="M286 350q0 -22 -8 -41t-22 -33.5t-33 -22.5t-41 -8q-21 0 -40.5 8t-33.5 22.5t-22.5 33.5t-8.5 41q0 21 8.5 40t22.5 33.5t33.5 22.5t40.5 8q22 0 41 -8t33 -22.5t22 -33.5t8 -40z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="564" 
d="M28 262q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44l131 -40q-27 -60 -89 -98t-149 -38q-64 0 -115 22
t-87 59.5t-55.5 87t-19.5 103.5z" />
    <glyph glyph-name="c_t" horiz-adv-x="945" 
d="M938 27q-27 -12 -66 -24t-83 -12q-27 0 -51.5 7t-43 22.5t-29.5 40t-11 59.5v301h-69v103h69q0 36 -2.5 65t-11 48.5t-25.5 30t-47 10.5q-47 0 -67 -27.5t-20 -66.5q0 -24 7 -51t16.5 -52t19.5 -47t17 -35l-131 -40q-18 30 -46 45t-63 15q-30 0 -55 -12.5t-43 -34
t-27.5 -50t-9.5 -61.5q0 -34 10.5 -62.5t29 -49.5t44 -33t54.5 -12q32 0 63.5 16.5t44.5 45.5l131 -40q-28 -62 -91.5 -99t-147.5 -37q-65 0 -116 22.5t-86.5 60t-54.5 87t-19 102.5t18.5 102t53.5 86.5t87 60t119 22.5q48 0 80.5 -10t64.5 -30q-11 23 -19 47t-8 50
q0 69 46 108.5t136 39.5q68 0 105 -18.5t54 -48.5t20 -68.5t3 -78.5h110v-103h-110v-256q0 -28 14.5 -39.5t36.5 -11.5q20 0 40 7t32 12z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="564" 
d="M338 585l-86 26l54 119h125zM28 262q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44l131 -40q-27 -60 -89 -98
t-149 -38q-64 0 -115 22t-87 59.5t-55.5 87t-19.5 103.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="564" 
d="M219 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM28 262q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44
l131 -40q-27 -60 -89 -98t-149 -38q-64 0 -115 22t-87 59.5t-55.5 87t-19.5 103.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="566" 
d="M226 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l42 83q-59 4 -105.5 27.5t-79 60.5t-50 84t-17.5 99q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5
t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44l131 -40q-25 -54 -78 -90.5t-128 -43.5l-21 -34q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="564" 
d="M149 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM28 262q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44
l131 -40q-27 -60 -89 -98t-149 -38q-64 0 -115 22t-87 59.5t-55.5 87t-19.5 103.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="564" 
d="M28 262q0 55 19 104t55 86t87 59t115 22q86 0 146.5 -37t90.5 -97l-131 -40q-17 29 -45 44.5t-62 15.5q-29 0 -54 -11.5t-43.5 -32t-29 -49.5t-10.5 -64t11 -64t29.5 -50t43.5 -32.5t53 -11.5q36 0 66.5 18t42.5 44l131 -40q-27 -60 -89 -98t-149 -38q-64 0 -115 22
t-87 59.5t-55.5 87t-19.5 103.5zM243 604v126h121v-126h-121z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="595" 
d="M278 -120v111q-52 4 -95.5 28.5t-75 61.5t-49 83t-17.5 94q0 51 15.5 97t46 82.5t74.5 61.5t101 33v113h60v-113q70 -2 127 -36t89 -97l-130 -40q-14 26 -37 40t-49 16v-308q27 5 53.5 20.5t34.5 38.5l131 -40q-15 -34 -41 -59.5t-57 -42.5t-63 -25.5t-58 -8.5v-110h-60z
M179 258q0 -25 7 -48t20 -42t31 -33.5t41 -21.5v296q-23 -5 -41.5 -19t-31.5 -34.5t-19.5 -45.5t-6.5 -52z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="243" 
d="M67 374v144h109v-144h-109zM67 0v144h109v-144h-109z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="236" 
d="M32 -18q7 0 20 5.5t13 25.5v131h110v-155q0 -30 -14 -49.5t-35 -31.5t-46 -16.5t-48 -4.5v95z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="843" 
d="M418 -7q-81 0 -149.5 27.5t-118.5 76t-78 114.5t-28 144q0 77 28 143t78 114t118.5 75.5t149.5 27.5q82 0 152 -27.5t121 -75.5t80 -114t29 -143q0 -78 -29 -144t-80 -114.5t-121 -76t-152 -27.5zM418 43q69 0 128.5 22.5t104 64t69.5 98.5t25 126q0 66 -24.5 122.5
t-68.5 98.5t-104 66t-130 24q-69 0 -127.5 -24t-101 -66t-67 -98.5t-24.5 -122.5t24 -122.5t67 -98.5t101.5 -66t127.5 -24zM427 116q-56 0 -102.5 18.5t-79.5 50.5t-51.5 75.5t-18.5 94.5q0 45 15.5 88t47 76.5t78.5 53.5t109 20q80 0 137 -34.5t84 -93.5l-130 -40
q-15 31 -41 42.5t-51 11.5q-26 0 -47 -10t-35.5 -27t-22 -39.5t-7.5 -47.5q0 -30 10 -53.5t26 -39t36 -24t41 -8.5q29 0 55 15t40 41l130 -39q-12 -26 -32 -49.5t-48.5 -41.5t-64.5 -29t-78 -11z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="649" 
d="M277 -10q-54 0 -99.5 21t-78.5 58t-51.5 86.5t-18.5 106.5t17.5 106t48.5 86t74 58t94 21q57 0 103.5 -28t72.5 -73v298h134v-576q0 -21 8 -30t28 -10v-114q-38 -7 -64 -7q-37 1 -57.5 17t-25.5 45l-3 36q-29 -50 -78 -75.5t-104 -25.5zM312 104q19 0 39 6.5t37 18.5
t31 28t21 35v125q-17 44 -55.5 73t-80.5 29q-30 0 -55.5 -13t-43.5 -35.5t-28 -51.5t-10 -60q0 -33 11 -61t30.5 -49t46 -33t57.5 -12z" />
    <glyph glyph-name="d.alt" horiz-adv-x="633" 
d="M440 0v82q-26 -44 -67 -68t-96 -24q-54 0 -99.5 21t-78.5 58t-51.5 86.5t-18.5 106.5t17.5 106t48.5 86t73.5 58t93.5 21q59 0 104.5 -28t72.5 -73v298h134v-730h-133zM440 317q-17 45 -56 73.5t-81 28.5q-30 0 -55 -13.5t-43 -35.5t-28 -51t-10 -60q0 -33 11 -61
t30.5 -49t46 -33t57.5 -12q19 0 39 6.5t37.5 18.5t31 28t20.5 35v125z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="466" 
d="M169 -130v341h-132v120h132v444h128v-444h131v-120h-131v-341h-128z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="476" 
d="M175 -130v232h-132v120h132v112h-132v121h132v320h127v-320h131v-121h-131v-112h131v-120h-131v-232h-127z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="649" 
d="M687 585l-86 26l54 119h125zM277 -10q-54 0 -99.5 21t-78.5 58t-51.5 86.5t-18.5 106.5t17.5 106t48.5 86t74 58t94 21q57 0 103.5 -28t72.5 -73v298h134v-576q0 -21 8 -30t28 -10v-114q-38 -7 -64 -7q-37 1 -57.5 17t-25.5 45l-3 36q-29 -50 -78 -75.5t-104 -25.5z
M312 104q19 0 39 6.5t37 18.5t31 28t21 35v125q-17 44 -55.5 73t-80.5 29q-30 0 -55.5 -13t-43.5 -35.5t-28 -51.5t-10 -60q0 -33 11 -61t30.5 -49t46 -33t57.5 -12z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="649" 
d="M277 -10q-54 0 -99.5 21t-78.5 58t-51.5 86.5t-18.5 106.5t17.5 106t48.5 86t74 58t94 21q57 0 103.5 -28t72.5 -73v165h-99v79h99v54h134v-54h64v-79h-64v-443q0 -21 8 -30t28 -10v-114q-38 -7 -64 -7q-37 1 -57.5 17t-25.5 45l-3 36q-29 -50 -78 -75.5t-104 -25.5z
M312 104q19 0 39 6.5t37 18.5t31 28t21 35v125q-17 44 -55.5 73t-80.5 29q-30 0 -55.5 -13t-43.5 -35.5t-28 -51.5t-10 -60q0 -33 11 -61t30.5 -49t46 -33t57.5 -12z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="287" 
d="M41 625q0 42 28.5 70.5t73.5 28.5t74 -28.5t29 -70.5q0 -48 -29 -77t-74 -29t-73.5 29.5t-28.5 76.5zM143 659q-15 0 -26 -10.5t-11 -27.5q0 -15 11 -26t26 -11t26 11t11 26q0 17 -10 27.5t-27 10.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="509" 
d="M198 419v128h113v-128h-113zM198 48v129h113v-129h-113zM53 244v107h403v-107h-403z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="634" 
d="M509 524q-6 7 -22.5 17t-39 20.5t-49.5 19t-56 11.5v-173l25 -6q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5q0 -55 -19.5 -94t-53 -64t-77.5 -37.5t-93 -14.5v-110h-60v111q-70 5 -137 28.5t-121 61.5l61 119q8 -8 27.5 -21t47.5 -26t61.5 -23.5t69.5 -14.5v172l-37 10
q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 51 17.5 90.5t48 67t72.5 43.5t91 20v102h60v-101q65 -5 120.5 -27.5t97.5 -50.5zM448 189q0 35 -28 54t-78 35v-162q106 3 106 73zM205 514q0 -34 24 -51.5t72 -31.5v162q-96 -6 -96 -79z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" 
d="M60 0v524h134v-524h-134z" />
    <glyph glyph-name="dotlessj" unicode="&#x237;" 
d="M12 -196q-38 0 -72 11.5t-61 35.5l57 91q12 -11 27.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v538h134v-528q0 -42 -14 -77.5t-39 -61t-58 -39.5t-71 -14z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="600" 
d="M337 585l-86 26l54 119h125zM304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32
q-29 -60 -92.5 -98.5t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272zM302 656q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="600" 
d="M218 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9
q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="600" 
d="M148 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9
q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="600" 
d="M151 604v119h111v-119h-111zM343 604v119h111v-119h-111zM304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9
q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272zM242 604v126h121v-126h-121z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272zM175 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="605" 
d="M560 210q0 -50 -20.5 -90t-56 -69t-82.5 -44.5t-100 -15.5t-99.5 16.5t-81.5 46.5t-55.5 71t-20.5 90q0 30 10 55.5t26 46.5t36 37t40 26q-38 18 -64 54t-26 79q0 48 21.5 84.5t56 61.5t76 38t82.5 13t83 -12.5t76.5 -37.5t56 -61.5t21.5 -84.5q0 -43 -26.5 -80
t-64.5 -54q21 -11 41.5 -27.5t36 -38t25 -47.5t9.5 -57zM424 218q0 25 -10.5 45t-27.5 33.5t-39 20.5t-45 7q-24 0 -46 -7.5t-39 -21.5t-27 -33.5t-10 -43.5t10.5 -43.5t27.5 -33.5t39.5 -21.5t45.5 -7.5q24 0 45.5 8t38.5 22t27 33.5t10 42.5zM202 509q0 -21 9 -36.5
t23.5 -26t32 -16t35.5 -5.5t36 5.5t32.5 16.5t23.5 26.5t9 36.5q0 37 -30 59t-71 22t-70.5 -22.5t-29.5 -59.5z" />
    <glyph glyph-name="eight.lnum" horiz-adv-x="605" 
d="M560 210q0 -50 -20.5 -90t-56 -69t-82.5 -44.5t-100 -15.5t-99.5 16.5t-81.5 46.5t-55.5 71t-20.5 90q0 30 10 55.5t26 46.5t36 37t40 26q-38 18 -64 54t-26 79q0 48 21.5 84.5t56 61.5t76 38t82.5 13t83 -12.5t76.5 -37.5t56 -61.5t21.5 -84.5q0 -43 -26.5 -80
t-64.5 -54q21 -11 41.5 -27.5t36 -38t25 -47.5t9.5 -57zM424 218q0 25 -10.5 45t-27.5 33.5t-39 20.5t-45 7q-24 0 -46 -7.5t-39 -21.5t-27 -33.5t-10 -43.5t10.5 -43.5t27.5 -33.5t39.5 -21.5t45.5 -7.5q24 0 45.5 8t38.5 22t27 33.5t10 42.5zM202 509q0 -21 9 -36.5
t23.5 -26t32 -16t35.5 -5.5t36 5.5t32.5 16.5t23.5 26.5t9 36.5q0 37 -30 59t-71 22t-70.5 -22.5t-29.5 -59.5z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="421" 
d="M388 123q0 -28 -14.5 -51.5t-38.5 -40.5t-56.5 -26.5t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 51q0 39 25.5 62.5t59.5 38.5q-27 11 -48 30.5t-21 50.5q0 25 14.5 46t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -29 -20 -50t-50 -32
q38 -16 62 -40.5t24 -62.5zM307 132q0 13 -8.5 24t-22 18.5t-31 11.5t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18.5t8 24zM128 300q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 9
t19.5 14t7.5 19q0 20 -25 32.5t-57 12.5q-33 0 -57.5 -12.5t-24.5 -32.5z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="421" 
d="M388 432q0 -28 -14.5 -51.5t-38.5 -41t-56.5 -27t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 52q0 39 25.5 62.5t59.5 37.5q-27 11 -48 30.5t-21 50.5q0 26 14.5 47t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -30 -20 -51t-50 -32
q38 -15 62 -40t24 -62zM307 440q0 13 -8.5 24t-22 19t-31 12t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18t8 23.5zM128 609q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 8.5t19.5 14
t7.5 19.5q0 21 -25 33t-57 12q-33 0 -57.5 -12.5t-24.5 -32.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="601" 
d="M57 0v144h110v-144h-110zM246 0v144h109v-144h-109zM433 0v144h111v-144h-111z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272zM153 633v83h300v-83h-300z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="927" 
d="M57 214v121h812v-121h-812z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="602" 
d="M57 214v121h488v-121h-488z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="601" 
d="M365 -196q-38 0 -72.5 11.5t-61.5 35.5l58 91q12 -11 27.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v308q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 79.5 -18t48 -47t23.5 -66t6 -75v-331
q0 -42 -14 -77.5t-39 -61t-58 -39.5t-70 -14z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="602" 
d="M325 -198q-53 8 -79.5 32t-26.5 55q0 53 70 102q-60 3 -108 25t-82 58.5t-52 84t-18 100.5q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5
l115 -32q-23 -49 -69.5 -83.5t-109.5 -47.5q-79 -47 -79 -88q0 -37 62 -40zM441 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="437" 
d="M62 144v87h313v-87h-313zM62 321v87h313v-87h-313z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="613" 
d="M525 660l-71 -39q64 -73 95 -160.5t31 -170.5q0 -70 -21 -125.5t-58 -94.5t-88 -59.5t-111 -20.5q-57 0 -106.5 18.5t-86 51t-57.5 77t-21 96.5q0 49 19 93t51.5 76t76.5 51t94 19q53 0 98 -21.5t73 -56.5q-11 48 -32 93t-58 87l-94 -52l-38 52l84 47q-32 28 -71 55.5
t-89 53.5h172q50 -26 91 -63l79 46zM168 234q0 -28 10.5 -51.5t28.5 -41t43 -27.5t54 -10q30 0 55.5 10.5t44 28.5t29 42t10.5 52t-10.5 51t-29 40t-43.5 26.5t-54 9.5q-30 0 -55 -10t-43.5 -27.5t-29 -41t-10.5 -51.5z" />
    <glyph glyph-name="etilde" unicode="&#x1ebd;" horiz-adv-x="600" 
d="M304 -10q-63 0 -114 21.5t-87 58.5t-55.5 86t-19.5 103q0 56 19 105.5t55 87t87.5 59.5t115.5 22t114.5 -22t86 -59t54 -86t18.5 -102q0 -13 -0.5 -25t-2.5 -20h-405q3 -31 15 -55t31 -41t43 -26t50 -9q40 0 75.5 19.5t48.5 51.5l115 -32q-29 -60 -92.5 -98.5
t-151.5 -38.5zM440 306q-5 59 -43.5 94.5t-93.5 35.5q-27 0 -50.5 -9.5t-41.5 -26.5t-29.5 -41t-13.5 -53h272zM366 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5
t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="289" 
d="M78 267v450h134v-450h-134zM78 0v151h134v-151h-134z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="290" 
d="M212 445v-450h-134v450h134zM212 712v-151h-134v151h134z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="381" 
d="M94 0v360h-68v103h68v65q0 98 47.5 155t127.5 57q64 0 129 -31l-27 -101q-15 8 -35.5 13.5t-37.5 5.5q-70 0 -70 -94v-70h128v-103h-128v-360h-134z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="555" 
d="M289 319q50 0 93 -16.5t74.5 -46.5t49 -72t17.5 -93q0 -54 -20 -98t-55 -75.5t-83 -49t-104 -17.5q-78 0 -139.5 33t-93.5 90l77 80q29 -39 70.5 -62t86.5 -23q54 0 88.5 32.5t34.5 86.5q0 52 -32.5 84t-82.5 32q-33 0 -61.5 -15t-45.5 -41h-117q0 3 4 22.5t10 50t13 68
t15 76.5q18 92 40 206h350v-122h-253l-28 -159q12 13 35 21t57 8z" />
    <glyph glyph-name="five.lnum" horiz-adv-x="571" 
d="M297 459q50 0 93 -17t74.5 -47t49 -72t17.5 -92q0 -54 -19.5 -98t-54.5 -76t-83 -49.5t-105 -17.5q-78 0 -139.5 33t-93.5 91l77 80q29 -39 70.5 -62t86.5 -23q54 0 88.5 32.5t34.5 86.5q0 25 -8.5 46.5t-24 37t-36.5 24t-46 8.5q-32 0 -61 -15t-46 -41h-117q0 3 4 22.5
t10 50t13 68t15 76.5q18 91 40 205h350v-122h-253l-28 -159q12 14 35 22t57 8z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="982" 
d="M944 123q0 -28 -14.5 -51.5t-38.5 -40.5t-56.5 -26.5t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 51q0 39 25.5 62.5t59.5 38.5q-27 11 -48 30.5t-21 50.5q0 25 14.5 46t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -29 -20 -50t-50 -32
q38 -16 62 -40.5t24 -62.5zM863 132q0 13 -8.5 24t-22 18.5t-31 11.5t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18.5t8 24zM684 300q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 9
t19.5 14t7.5 19q0 20 -25 32.5t-57 12.5q-33 0 -57.5 -12.5t-24.5 -32.5zM214 582q35 0 65 -10t52 -28t34.5 -43t12.5 -55q0 -32 -13.5 -58t-38 -45t-57.5 -29.5t-72 -10.5q-53 0 -95.5 19.5t-65.5 52.5l47 53q20 -23 50 -35.5t68 -12.5q41 0 68.5 17.5t27.5 52.5
q0 18 -8 31.5t-21 22.5t-28.5 13.5t-30.5 4.5q-26 0 -52 -12.5t-39 -32.5h-70q5 27 13 61t16.5 68.5t16 67t11.5 56.5h242v-79h-184l-24 -87q11 8 30 13t45 5zM182 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="396" 
d="M204 582q35 0 65 -10t52 -28t34.5 -43t12.5 -55q0 -32 -13.5 -58t-38 -45t-57.5 -29.5t-72 -10.5q-53 0 -95.5 19.5t-65.5 52.5l47 53q20 -23 50 -35.5t68 -12.5q41 0 68.5 17.5t27.5 52.5q0 18 -8 31.5t-21 22.5t-28.5 13.5t-30.5 4.5q-26 0 -52 -12.5t-39 -32.5h-70
q5 27 13 61t16.5 68.5t16 67t11.5 56.5h242v-79h-184l-24 -87q11 8 30 13t45 5z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="200" 
d="M-201 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="396" 
d="M203 273q35 0 65 -10t52 -28t34.5 -43t12.5 -55q0 -32 -13.5 -58t-38 -44.5t-57.5 -29t-72 -10.5q-53 0 -95.5 19t-65.5 52l47 53q20 -23 50 -36t68 -13q41 0 68.5 18t27.5 53q0 18 -8 31.5t-21 22.5t-28.5 13.5t-30.5 4.5q-26 0 -52 -12.5t-39 -32.5h-70q5 27 13 61
t16.5 68.5t16 67.5t11.5 57h242v-80h-184l-24 -87q11 8 30 13t45 5z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="548" 
d="M162 -196q-37 0 -71.5 11.5t-61.5 35.5l58 91q11 -11 27 -16.5t32 -5.5q26 0 45 18.5t19 47.5v374h-68v103h68v65q0 98 47.5 155t127.5 57q64 0 129 -31l-27 -101q-15 8 -35.5 13.5t-37.5 5.5q-70 0 -70 -94v-70h128v-103h-128v-371q0 -41 -14.5 -75t-39 -58.5t-58 -38
t-70.5 -13.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="565" 
d="M313 -127v149h-292v119l334 430h92v-429h88v-120h-88v-149h-134zM155 142h173v226z" />
    <glyph glyph-name="four.lnum" horiz-adv-x="583" 
d="M324 0v161h-299v120l341 429h92v-428h88v-121h-88v-161h-134zM160 282h179v226z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="410" 
d="M242 0v93h-221v77l246 252h54v-251h61v-78h-61v-93h-79zM98 171h147v153z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="410" 
d="M242 310v91h-221v78l246 251h54v-251h61v-78h-61v-91h-79zM98 479h147v154z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="633" 
d="M265 -3q-52 0 -95.5 21t-74.5 57.5t-48 84.5t-17 102q0 57 18 106t50 86t76.5 58t98.5 21q62 0 108 -27.5t75 -73.5v92h117v-499q0 -58 -21.5 -104t-60.5 -78t-92 -49t-116 -17q-87 0 -145.5 28.5t-100.5 80.5l73 71q30 -37 75.5 -58t97.5 -21q31 0 59.5 8.5t50 26.5
t34 46t12.5 66v66q-26 -45 -73 -69.5t-101 -24.5zM311 104q22 0 42 7t36.5 19t29.5 28t20 34v125q-18 46 -56 74t-80 28q-31 0 -56.5 -13.5t-43.5 -36t-27.5 -51.5t-9.5 -60q0 -32 11 -60t30.5 -49t46 -33t57.5 -12z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="633" 
d="M265 -3q-52 0 -95.5 21t-74.5 57.5t-48 84.5t-17 102q0 57 18 106t50 86t76.5 58t98.5 21q62 0 108 -27.5t75 -73.5v92h117v-499q0 -58 -21.5 -104t-60.5 -78t-92 -49t-116 -17q-87 0 -145.5 28.5t-100.5 80.5l73 71q30 -37 75.5 -58t97.5 -21q31 0 59.5 8.5t50 26.5
t34 46t12.5 66v66q-26 -45 -73 -69.5t-101 -24.5zM311 104q22 0 42 7t36.5 19t29.5 28t20 34v125q-18 46 -56 74t-80 28q-31 0 -56.5 -13.5t-43.5 -36t-27.5 -51.5t-9.5 -60q0 -32 11 -60t30.5 -49t46 -33t57.5 -12zM340 656q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5
t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="633" 
d="M186 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM265 -3q-52 0 -95.5 21t-74.5 57.5t-48 84.5t-17 102q0 57 18 106t50 86t76.5 58t98.5 21q62 0 108 -27.5t75 -73.5v92h117v-499q0 -58 -21.5 -104t-60.5 -78t-92 -49t-116 -17q-87 0 -145.5 28.5t-100.5 80.5
l73 71q30 -37 75.5 -58t97.5 -21q31 0 59.5 8.5t50 26.5t34 46t12.5 66v66q-26 -45 -73 -69.5t-101 -24.5zM311 104q22 0 42 7t36.5 19t29.5 28t20 34v125q-18 46 -56 74t-80 28q-31 0 -56.5 -13.5t-43.5 -36t-27.5 -51.5t-9.5 -60q0 -32 11 -60t30.5 -49t46 -33t57.5 -12z
" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="633" 
d="M375 585l-86 26l54 119h125zM265 -3q-52 0 -95.5 21t-74.5 57.5t-48 84.5t-17 102q0 57 18 106t50 86t76.5 58t98.5 21q62 0 108 -27.5t75 -73.5v92h117v-499q0 -58 -21.5 -104t-60.5 -78t-92 -49t-116 -17q-87 0 -145.5 28.5t-100.5 80.5l73 71q30 -37 75.5 -58
t97.5 -21q31 0 59.5 8.5t50 26.5t34 46t12.5 66v66q-26 -45 -73 -69.5t-101 -24.5zM311 104q22 0 42 7t36.5 19t29.5 28t20 34v125q-18 46 -56 74t-80 28q-31 0 -56.5 -13.5t-43.5 -36t-27.5 -51.5t-9.5 -60q0 -32 11 -60t30.5 -49t46 -33t57.5 -12z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="633" 
d="M265 -3q-52 0 -95.5 21t-74.5 57.5t-48 84.5t-17 102q0 57 18 106t50 86t76.5 58t98.5 21q62 0 108 -27.5t75 -73.5v92h117v-499q0 -58 -21.5 -104t-60.5 -78t-92 -49t-116 -17q-87 0 -145.5 28.5t-100.5 80.5l73 71q30 -37 75.5 -58t97.5 -21q31 0 59.5 8.5t50 26.5
t34 46t12.5 66v66q-26 -45 -73 -69.5t-101 -24.5zM311 104q22 0 42 7t36.5 19t29.5 28t20 34v125q-18 46 -56 74t-80 28q-31 0 -56.5 -13.5t-43.5 -36t-27.5 -51.5t-9.5 -60q0 -32 11 -60t30.5 -49t46 -33t57.5 -12zM280 604v126h121v-126h-121z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="558" 
d="M246 117q31 0 57 6.5t45 20t29.5 33.5t10.5 48q0 26 -9.5 46t-25 34t-36.5 21t-43 7h-20v120h19q36 0 56.5 20.5t20.5 53.5q0 21 -8 35t-19.5 22.5t-25.5 12t-26 3.5q-24 0 -40 -9.5t-26 -24t-14 -32.5t-4 -35v-499h-127v522q0 42 17 77t46.5 60.5t70 40t87.5 14.5
q42 0 78 -11.5t62 -32.5t41 -51.5t15 -68.5q0 -26 -7 -50t-19 -43.5t-27.5 -34t-33.5 -20.5q65 -18 102 -69t37 -123q0 -57 -23.5 -97.5t-62.5 -66.5t-90 -38.5t-107 -13.5v123z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="509" 
d="M477 223l-422 -250v150l227 136l-227 142v142l422 -255v-65z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="649" 
d="M37 288l263 189v-109l-157 -109l157 -103v-110l-263 177v65zM328 288l264 189v-109l-158 -109l158 -103v-110l-264 177v65z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="649" 
d="M612 223l-264 -177v110l158 103l-158 109v109l264 -189v-65zM321 223l-264 -177v110l158 103l-158 109v109l264 -189v-65z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="357" 
d="M37 288l263 189v-109l-157 -109l157 -103v-110l-263 177v65z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="357" 
d="M321 223l-264 -177v110l158 103l-158 109v109l264 -189v-65z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="602" 
d="M547 0h-134v294q0 62 -23 91.5t-65 29.5q-18 0 -38 -8t-38 -22.5t-33 -34.5t-22 -44v-306h-134v730h134v-303q29 51 78.5 78.5t109.5 27.5q51 0 83 -17.5t50 -46.5t25 -66t7 -76v-327z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="602" 
d="M547 0h-134v294q0 62 -23 91.5t-65 29.5q-18 0 -38 -8t-38 -22.5t-33 -34.5t-22 -44v-306h-134v597h-45v79h45v54h134v-54h118v-79h-118v-170q29 51 78.5 78.5t109.5 27.5q51 0 83 -17.5t50 -46.5t25 -66t7 -76v-327z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="602" 
d="M243 630l93 98h86l93 -98l-61 -32l-75 62l-74 -62zM547 0h-134v294q0 62 -23 91.5t-65 29.5q-18 0 -38 -8t-38 -22.5t-33 -34.5t-22 -44v-306h-134v730h134v-303q29 51 78.5 78.5t109.5 27.5q51 0 83 -17.5t50 -46.5t25 -66t7 -76v-327z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="417" 
d="M57 214v121h303v-121h-303z" />
    <glyph glyph-name="i" unicode="i" 
d="M60 0v524h134v-524h-134zM60 597v133h134v-133h-134z" />
    <glyph glyph-name="iacute" unicode="&#xed;" 
d="M161 585l-86 26l54 119h125zM60 0v524h134v-524h-134z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" 
d="M60 0v524h134v-524h-134zM126 656q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" 
d="M-28 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM60 0v524h134v-524h-134z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" 
d="M-25 604v119h111v-119h-111zM167 604v119h111v-119h-111zM60 0v524h134v-524h-134z" />
    <glyph glyph-name="igrave" unicode="&#xec;" 
d="M60 0v524h134v-524h-134zM-1 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="506" 
d="M60 0v524h134v-524h-134zM60 597v133h134v-133h-134zM265 -196q-38 0 -72 11.5t-61 35.5l57 91q12 -11 27.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v538h134v-528q0 -42 -14 -77.5t-39 -61t-58 -39.5t-71 -14zM313 597v133h134v-133h-134z" />
    <glyph glyph-name="j" unicode="j" 
d="M12 -196q-38 0 -72 11.5t-61 35.5l57 91q12 -11 27.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v538h134v-528q0 -42 -14 -77.5t-39 -61t-58 -39.5t-71 -14zM60 597v133h134v-133h-134z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" 
d="M60 0v524h134v-524h-134zM-22 633v83h300v-83h-300z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="254" 
d="M147 -196q-53 8 -79.5 32t-26.5 55q0 58 81 109h-61v524h134v-524q-82 -48 -82 -90q0 -37 62 -40zM61 597v133h134v-133h-134z" />
    <glyph glyph-name="itilde" unicode="&#x129;" 
d="M60 0v524h134v-524h-134zM189 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43
t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="j.alt" 
d="M60 517h134v-730h-134v730zM60 726h133v-129h-133v129z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" 
d="M12 -196q-38 0 -72 11.5t-61 35.5l57 91q12 -11 27.5 -16.5t31.5 -5.5q26 0 45.5 18.5t19.5 47.5v538h134v-528q0 -42 -14 -77.5t-39 -61t-58 -39.5t-71 -14zM-28 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="557" 
d="M418 0l-152 227l-72 -71v-156h-134v730h134v-443l210 236h143l-196 -222l210 -301h-143z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="557" 
d="M199 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM418 0l-152 227l-72 -71v-156h-134v730h134v-443l210 236h143l-196 -222l210 -301h-143z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="557" 
d="M418 0l-152 227l-72 -71v-156h-134v524h134v-237l210 236h143l-196 -222l210 -301h-143z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="317" 
d="M59 730h134v-556q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v609z" />
    <glyph glyph-name="l.alt" horiz-adv-x="259" 
d="M62 730h134v-730h-134v730z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="317" 
d="M158 764l-86 26l54 119h125zM59 730h134v-556q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v609z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="317" 
d="M320 585l-86 26l54 119h125zM59 730h134v-556q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v609z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="317" 
d="M100 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM59 730h134v-556q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v609z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="396" 
d="M59 730h134v-556q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v609zM269 283v151h110v-151h-110z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="509" 
d="M32 288l422 255v-142l-227 -142l227 -136v-150l-422 250v65z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="615" 
d="M551 400v-294h-114v174h-374v120h488z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="343" 
d="M81 299l-51 -30l-34 72l85 51v338h134v-258l86 52l34 -72l-120 -72v-206q0 -61 55 -61q11 0 25.5 4t27.5 9l18 -107q-27 -13 -63 -20t-65 -7q-63 0 -97.5 33.5t-34.5 95.5v178z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="929" 
d="M874 0h-134v294q0 63 -22 92t-60 29q-39 0 -74 -30.5t-50 -79.5v-305h-134v294q0 63 -21.5 92t-59.5 29q-39 0 -74.5 -30t-50.5 -79v-306h-134v524h121v-97q29 51 79.5 78.5t115.5 27.5q66 0 101.5 -32t46.5 -79q32 54 81 82.5t112 28.5q48 0 79 -18t48 -47t23.5 -66
t6.5 -75v-327z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="528" 
d="M62 274v107h404v-107h-404z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="637" 
d="M70 524h134v-299q0 -121 90 -121q37 0 73.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5t-28 -1q-35 0 -56.5 16t-26.5 45l-2 43q-9 -18 -25.5 -37t-38 -35t-46.5 -26t-51 -10q-20 0 -35.5 6t-26.5 14.5t-18 19t-10 19.5l9 -261h-135v736z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="478" 
d="M426 172l-85 -83l-103 103l-103 -103l-82 84l101 102l-98 99l85 83l98 -98l99 99l82 -84l-98 -99z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="602" 
d="M547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="602" 
d="M350 585l-86 26l54 119h125zM547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="602" 
d="M61 585l-85 26l34 73l-7 46h151zM547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="602" 
d="M231 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="602" 
d="M211 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="ndotaccent" unicode="&#x1e45;" horiz-adv-x="602" 
d="M255 604v126h121v-126h-121zM547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="589" 
d="M32 324q0 52 20 97t54.5 78.5t82 52.5t102.5 19q60 0 108.5 -22t82.5 -64t52.5 -102.5t18.5 -137.5q0 -97 -19.5 -171.5t-55 -125t-86.5 -76.5t-114 -26q-67 0 -124 29t-96 83l77 84q24 -38 62 -59t83 -21q67 0 106.5 53t41.5 150q-20 -36 -59 -57t-90 -21q-53 0 -98 18
t-78 50t-52 75.5t-19 93.5zM291 458q-27 0 -50.5 -10.5t-41.5 -29t-28.5 -43t-10.5 -51.5t10.5 -50.5t28 -41.5t41.5 -28t51 -10t51 10t41.5 28t28 41.5t10.5 50.5t-10.5 51.5t-28.5 42.5t-41.5 29t-50.5 11z" />
    <glyph glyph-name="nine.lnum" horiz-adv-x="607" 
d="M40 468q0 52 20 97t54.5 78.5t82 52.5t102.5 19q60 0 108.5 -22t82.5 -63.5t52.5 -102t18.5 -137.5q0 -97 -19.5 -171.5t-55 -125.5t-86.5 -77t-114 -26q-68 0 -124.5 29.5t-95.5 83.5l77 84q24 -38 62 -59t83 -21q67 0 106.5 53t41.5 150q-20 -36 -59 -57t-90 -21
q-53 0 -98 18t-78 50t-52 75t-19 93zM299 603q-27 0 -50.5 -11t-41.5 -29t-28.5 -42.5t-10.5 -51.5t10.5 -50.5t28.5 -41.5t41.5 -28t50.5 -10t51 10t41.5 27.5t28 41t10.5 50.5t-10.5 51.5t-28.5 43t-41.5 29.5t-50.5 11z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="425" 
d="M30 278q0 31 14 57.5t38 46.5t57 31.5t71 11.5q83 0 132 -51.5t49 -145.5q0 -113 -51.5 -173t-139.5 -60q-46 0 -85.5 16t-64.5 48l47 55q15 -21 43.5 -32.5t62.5 -11.5q51 0 82 28t32 80q-15 -19 -47 -30t-65 -11q-38 0 -70 11t-55.5 29.5t-36.5 44.5t-13 56zM209 352
q-21 0 -40 -6t-33 -16.5t-22 -23.5t-8 -28t8 -27.5t22.5 -22.5t33.5 -15.5t40 -5.5t39.5 5.5t33 15.5t22.5 22.5t8 27.5t-8.5 28t-22.5 23.5t-33 16.5t-40 6z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="425" 
d="M30 583q0 31 14 57.5t38 46.5t57 31.5t71 11.5q83 0 132 -51.5t49 -145.5q0 -113 -51.5 -173.5t-139.5 -60.5q-46 0 -85.5 17t-64.5 48l47 55q15 -21 43.5 -33t62.5 -12q51 0 82 29t32 80q-15 -19 -47 -29.5t-65 -10.5q-38 0 -70 11t-55.5 29.5t-36.5 44t-13 55.5z
M209 657q-21 0 -40 -6t-33 -16t-22 -23.5t-8 -28.5t8 -27.5t22.5 -22.5t33.5 -15.5t40 -5.5t39.5 5.5t33 15.5t22.5 22.5t8 27.5t-8.5 28.5t-22.5 23.5t-33 16t-40 6z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="602" 
d="M547 0h-134v294q0 63 -22 92t-61 29q-20 0 -41 -8t-39.5 -22.5t-33.5 -34.5t-22 -44v-306h-134v524h121v-97q29 50 84 78t124 28q49 0 80 -18t48 -47t23.5 -66t6.5 -75v-327zM378 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5
t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="724" 
d="M686 424h-145l-33 -137h131v-100h-155l-46 -187h-108l47 187h-120l-45 -187h-108l46 187h-112v100h136l33 137h-122v94h145l47 192h108l-48 -192h120l46 192h108l-47 -192h122v-94zM401 287l32 137h-119l-33 -137h120z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="605" 
d="M303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5
t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="605" 
d="M337 585l-86 26l54 119h125zM303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5
t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="605" 
d="M303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5
t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63zM302 656q23 0 40 21t20 53h71q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="605" 
d="M148 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5
t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="605" 
d="M151 604v119h111v-119h-111zM343 604v119h111v-119h-111zM303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261
q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1012" 
d="M301 -10q-59 0 -109 20.5t-86.5 56.5t-57 85.5t-20.5 108.5t20.5 109t57 86t86.5 56.5t109 20.5q68 0 120 -31.5t87 -90.5q75 122 210 122q58 0 107.5 -19.5t85.5 -55.5t56.5 -86t22.5 -110q0 -11 -0.5 -23t-2.5 -20h-404q2 -29 14 -53.5t31 -42.5t44 -28t54 -10
q42 0 77.5 21t48.5 53l111 -32q-15 -32 -39.5 -57t-56 -43t-69 -27.5t-77.5 -9.5q-63 0 -115 30t-97 88q-46 -60 -95.5 -89t-112.5 -29zM303 104q30 0 55 12t43 33t28.5 49.5t10.5 62.5t-10.5 63t-28.5 50t-43 33t-55 12q-29 0 -54 -12t-43.5 -33.5t-29 -50.5t-10.5 -63
t10.5 -62.5t28.5 -49.5t43 -32.5t55 -11.5zM853 302q-3 30 -14.5 54.5t-30 42.5t-43 27.5t-51.5 9.5q-56 0 -93.5 -37t-41.5 -97h274z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="605" 
d="M303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5
t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63zM176 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="605" 
d="M240 585l-66 26l48 119h104zM400 585l-65 26l47 119h105zM303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261
q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="605" 
d="M154 633v83h300v-83h-300zM303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5
t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="492" 
d="M469 121v-121h-406v121h142v303q-9 -12 -28 -24.5t-42 -23.5t-48 -18t-44 -7v125q18 0 44 11.5t51 27.5t44 31.5t23 24.5h137v-450h127z" />
    <glyph glyph-name="one.lnum" horiz-adv-x="488" 
d="M467 121v-121h-403v121h139v462q-8 -12 -27 -26.5t-42 -28t-48 -22.5t-45 -9v124q27 0 55.5 14.5t52 32t38.5 32.5t16 17h137v-596h127z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="955" 
d="M919 123q0 -28 -14.5 -51.5t-38.5 -40.5t-56.5 -26.5t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 51q0 39 25.5 62.5t59.5 38.5q-27 11 -48 30.5t-21 50.5q0 25 14.5 46t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -29 -20 -50t-50 -32
q38 -16 62 -40.5t24 -62.5zM838 132q0 13 -8.5 24t-22 18.5t-31 11.5t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18.5t8 24zM659 300q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 9
t19.5 14t7.5 19q0 20 -25 32.5t-57 12.5q-33 0 -57.5 -12.5t-24.5 -32.5zM289 384v-78h-229v78h78v261q-5 -7 -16 -15.5t-24.5 -16.5t-27.5 -13.5t-26 -5.5v80q15 0 31.5 8.5t30.5 19t23 19.5t9 10h80v-347h71zM146 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="298" 
d="M277 384v-78h-229v78h78v261q-5 -7 -16 -15.5t-24.5 -16.5t-27.5 -13.5t-26 -5.5v80q15 0 31.5 8.5t30.5 19t23 19.5t9 10h80v-347h71z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="958" 
d="M586 0q0 41 6.5 71.5t21 53.5t38 40.5t58.5 32.5q21 9 43 18t40 20t29 24.5t11 33.5q0 26 -21 40.5t-60 14.5q-41 0 -69.5 -16.5t-45.5 -33.5l-48 58q7 10 22.5 22t37.5 22.5t51 17.5t64 7q75 0 114.5 -35t39.5 -98q0 -33 -12.5 -55.5t-31 -37.5t-40.5 -24.5t-40 -15.5
q-48 -16 -67 -37.5t-19 -44.5h211v-78h-333zM288 384v-78h-229v78h78v261q-5 -7 -16 -15.5t-24.5 -16.5t-27.5 -13.5t-26 -5.5v80q15 0 31.5 8.5t30.5 19t23 19.5t9 10h80v-347h71zM138 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="422" 
d="M30 0q0 41 7 71.5t22.5 53.5t40.5 40.5t62 32.5q23 9 46.5 18t42 20t30.5 24.5t12 33.5q0 26 -22 40.5t-64 14.5q-44 0 -75 -16.5t-48 -33.5l-51 58q8 10 24 22t39.5 22.5t54 17.5t68.5 7q81 0 122.5 -35t41.5 -98q0 -33 -13.5 -55.5t-33 -37.5t-43 -24.5t-42.5 -15.5
q-51 -16 -71 -37.5t-20 -44.5h224v-78h-354z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="332" 
d="M313 78v-78h-265v78h96v262q-6 -7 -19 -16t-29 -16.5t-33 -13t-31 -5.5v79q18 0 38 8.5t36 19t27 19.5t11 10h80v-347h89z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="931" 
d="M754 0v93h-221v77l246 252h54v-251h61v-78h-61v-93h-79zM610 171h147v153zM287 384v-78h-229v78h78v261q-5 -7 -16 -15.5t-24.5 -16.5t-27.5 -13.5t-26 -5.5v80q15 0 31.5 8.5t30.5 19t23 19.5t9 10h80v-347h71zM175 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="959" 
d="M740 -5q-63 0 -106.5 20t-57.5 59l43 58q2 -6 10.5 -16t23.5 -19.5t36.5 -16t48.5 -6.5q42 0 72.5 13t30.5 39q0 29 -40 42.5t-109 13.5h-24v72h24q60 0 97 12.5t37 39.5q0 12 -8 21t-20 14.5t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 63q23 29 68 44.5t96 15.5
q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -45q0 -36 -25 -59t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37t-58 -21.5t-67 -7zM290 384v-78h-229v78h78v261q-5 -7 -16 -15.5t-24.5 -16.5t-27.5 -13.5t-26 -5.5v80q15 0 31.5 8.5t30.5 19t23 19.5t9 10h80v-347h71z
M134 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="408" 
d="M193 -5q-63 0 -106.5 20t-57.5 59l43 58q2 -6 10.5 -16t23.5 -19.5t36.5 -16t48.5 -6.5q42 0 72.5 13t30.5 39q0 29 -40 42.5t-109 13.5h-24v72h24q60 0 97 12.5t37 39.5q0 12 -8 21t-20 14.5t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 63q23 29 68 44.5t96 15.5
q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -45q0 -36 -25 -59t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37t-58 -21.5t-67 -7z" />
    <glyph glyph-name="oogonek" unicode="&#x1eb;" horiz-adv-x="607" 
d="M319 -199q-53 8 -79.5 32t-26.5 55q0 26 17.5 52t53.5 51q-60 3 -107 26t-80 60t-50.5 84.5t-17.5 99.5q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -47 -14.5 -91t-42 -79.5t-67 -60.5t-90.5 -34q-79 -48 -79 -89q0 -37 62 -40z
M167 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="444" 
d="M160 339q-26 0 -47.5 8.5t-38 24t-25.5 36.5t-9 45q0 26 11 48t31 37.5t47 24.5t60 9q49 0 84 -16v19q0 33 -18.5 51.5t-56.5 18.5q-29 0 -55 -10t-55 -30l-30 61q69 47 149 47q78 0 120.5 -38t42.5 -112v-104q0 -28 26 -29v-85q-14 -2 -26 -3t-21 -1q-51 1 -59 44l-2 19
q-24 -32 -57 -48.5t-71 -16.5zM189 411q20 0 39.5 7.5t30.5 19.5q14 12 14 26v39q-15 6 -32.5 9t-34.5 3q-32 0 -53 -14.5t-21 -38.5q0 -22 16.5 -36.5t40.5 -14.5z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="443" 
d="M221 339q-44 0 -78.5 15.5t-58.5 41t-36.5 59.5t-12.5 71q0 38 12.5 72t36.5 59.5t58.5 40.5t78.5 15t78.5 -15t58.5 -40.5t36.5 -59.5t12.5 -72q0 -37 -12.5 -71t-36.5 -59.5t-58.5 -41t-78.5 -15.5zM135 525q0 -45 24.5 -73.5t61.5 -28.5t62 29t25 74t-25 73.5
t-62 28.5t-61.5 -28.5t-24.5 -74.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="605" 
d="M108 64q-39 38 -59.5 89t-20.5 108q0 55 19 104t54.5 86.5t86.5 59.5t115 22q42 0 78 -10t66 -27l27 32h76l-56 -67q40 -38 61.5 -89.5t21.5 -110.5q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22q-44 0 -81 10t-68 29l-28 -33h-76zM303 104q29 0 54 11.5t43.5 32.5
t29 50t10.5 64q0 29 -7.5 54.5t-21.5 45.5l-181 -236q33 -22 73 -22zM166 261q0 -57 27 -98l179 236q-31 20 -69 20q-29 0 -54 -12t-43.5 -33t-29 -50t-10.5 -63z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="605" 
d="M108 64q-39 38 -59.5 89t-20.5 108q0 55 19 104t54.5 86.5t86.5 59.5t115 22q42 0 78 -10t66 -27l27 32h76l-56 -67q40 -38 61.5 -89.5t21.5 -110.5q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22q-44 0 -81 10t-68 29l-28 -33h-76zM303 104q29 0 54 11.5t43.5 32.5
t29 50t10.5 64q0 29 -7.5 54.5t-21.5 45.5l-181 -236q33 -22 73 -22zM166 261q0 -57 27 -98l179 236q-31 20 -69 20q-29 0 -54 -12t-43.5 -33t-29 -50t-10.5 -63zM337 585l-86 26l54 119h125z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="605" 
d="M303 -10q-64 0 -115 22t-86.5 59.5t-54.5 86.5t-19 103q0 55 19 104t54.5 86.5t86.5 59.5t115 22t114.5 -22t86 -59.5t54.5 -86.5t19 -104q0 -54 -19 -103t-54 -86.5t-86 -59.5t-115 -22zM166 261q0 -35 10.5 -63.5t29 -49.5t43.5 -32.5t54 -11.5t54 11.5t43.5 32.5
t29 50t10.5 64q0 34 -10.5 63t-29 50t-43.5 32.5t-54 11.5t-54 -12t-43.5 -33t-29 -50t-10.5 -63zM366 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16
t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="632" 
d="M373 -10q-61 0 -107 27.5t-72 74.5v-305h-134v737h117v-90q29 46 75 72.5t106 26.5q53 0 98 -21.5t77.5 -58t51 -86t18.5 -104.5q0 -57 -17 -107t-47.5 -87t-73 -58t-92.5 -21zM329 104q30 0 55.5 13t43.5 35t28 51t10 60q0 33 -11 61.5t-30.5 49.5t-46 33t-57.5 12
q-19 0 -39 -6.5t-37 -18.5t-30.5 -28t-20.5 -35v-123q18 -45 55 -74.5t80 -29.5z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="652" 
d="M35 456q0 121 74 187.5t208 66.5h295v-100h-70v-690h-114v288h-51v-288h-114v288q-52 0 -94 18t-72 50.5t-46 78t-16 101.5zM147 459q0 -35 9.5 -62t25 -46t36.5 -30t45 -13v302q-55 -3 -85.5 -44t-30.5 -107zM428 610h-51v-302h51v302z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="308" 
d="M34 347q0 51 11.5 101.5t31.5 101.5t47 102.5t58 104.5l103 -43q-16 -24 -37.5 -65.5t-41 -92t-33.5 -106t-14 -106.5q0 -81 34 -168t88 -180l-97 -49q-32 46 -59 95.5t-47.5 100.5t-32 102t-11.5 102z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="308" 
d="M274 347q0 -51 -11.5 -102t-32 -102t-48 -100.5t-58.5 -95.5l-98 49q55 93 89 180t34 168q0 51 -14 106.5t-33.5 106t-41 92t-37.5 65.5l103 43q31 -53 58 -104.5t47 -102.5t31.5 -101.5t11.5 -101.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="793" 
d="M203 417q-34 0 -64 12t-52.5 32.5t-35.5 48t-13 59.5q0 31 13 59t35.5 48t52.5 32t64 12q35 0 65 -12t52.5 -32t35 -48t12.5 -59q0 -32 -12.5 -59.5t-35 -48t-52.5 -32.5t-65 -12zM203 484q15 0 27.5 7t22.5 18.5t15.5 27t5.5 32.5q0 35 -21 59.5t-50 24.5
q-15 0 -27.5 -7t-22.5 -18.5t-15.5 -26.5t-5.5 -32q0 -18 5.5 -33.5t15.5 -27t23 -18t27 -6.5zM589 -10q-34 0 -64 12t-52 32t-35 47.5t-13 59.5t13 59.5t35 48t52 32.5t64 12q35 0 65 -12t52.5 -32.5t35 -48t12.5 -59.5t-12.5 -59.5t-35 -47.5t-52.5 -32t-65 -12zM589 57
q30 0 51 25t21 59q0 35 -21.5 60t-50.5 25q-15 0 -27.5 -7t-22.5 -18.5t-15.5 -27t-5.5 -32.5q0 -35 21 -59.5t50 -24.5zM95 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="224" 
d="M57 0v144h110v-144h-110z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1172" 
d="M207 417q-35 0 -65 12t-52 32.5t-35 48t-13 59.5q0 31 13 59t35 48t52 32t65 12q34 0 64.5 -12t52.5 -32t35 -48t13 -59q0 -32 -13 -59.5t-35 -48t-52.5 -32.5t-64.5 -12zM207 484q15 0 27.5 7t22.5 18.5t15.5 27t5.5 32.5q0 35 -21 59.5t-50 24.5q-15 0 -27.5 -7
t-22.5 -18.5t-15.5 -26.5t-5.5 -32q0 -18 5.5 -33.5t15.5 -27t23 -18t27 -6.5zM593 -10q-35 0 -65 12t-52 32t-35 47.5t-13 59.5t13 59.5t35 48t52 32.5t65 12q34 0 64.5 -12t52.5 -32.5t35 -48t13 -59.5t-13 -59.5t-35 -47.5t-52.5 -32t-64.5 -12zM593 57q15 0 27.5 7
t22.5 18.5t15.5 26.5t5.5 32q0 18 -6 33.5t-15.5 27t-22.5 18t-27 6.5q-15 0 -27.5 -7t-22.5 -18.5t-15.5 -27t-5.5 -32.5t5.5 -32.5t15.5 -27t22.5 -18t27.5 -6.5zM974 -10q-34 0 -64 12t-52 32t-35 47.5t-13 59.5t13 59.5t35 48t52 32.5t64 12q35 0 65 -12t52.5 -32.5
t35 -48t12.5 -59.5t-12.5 -59.5t-35 -47.5t-52.5 -32t-65 -12zM974 57q30 0 50.5 25t20.5 59q0 18 -6 33.5t-15.5 27t-22.5 18t-27 6.5q-15 0 -27.5 -7t-22.5 -18.5t-15.5 -27t-5.5 -32.5q0 -35 21 -59.5t50 -24.5zM98 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="424" 
d="M375 409v-108h-103v-113h-120v113h-104v108h104v113h120v-113h103z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="472" 
d="M59 28v108h353v-108h-353zM413 430v-107h-117v-127h-120v127h-117v107h117v127h120v-127h117z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="632" 
d="M258 -10q-50 0 -92 21.5t-72.5 58.5t-47.5 86.5t-17 106.5t18.5 106t51.5 85.5t78 57.5t97 21q60 0 106 -27.5t75 -72.5v91h117v-576q0 -22 8 -30.5t28 -9.5v-114q-17 -3 -38 -4.5t-36 -1.5q-40 0 -68 25.5t-28 65.5v213q-29 -51 -75 -76.5t-105 -25.5zM308 104
q43 0 75 24t55 62v124q-6 22 -19.5 41t-31.5 33.5t-39 22.5t-42 8q-31 0 -56.5 -13t-44 -35.5t-29 -51.5t-10.5 -60q0 -33 10.5 -61t29 -49t44.5 -33t58 -12z" />
    <glyph glyph-name="q.alt" horiz-adv-x="632" 
d="M258 -10q-50 0 -92 21.5t-72.5 58.5t-47.5 86.5t-17 106.5t18.5 106t51.5 85.5t78 57.5t97 21q60 0 106 -27.5t75 -72.5v91h117v-737h-134v305q-29 -51 -75 -76.5t-105 -25.5zM308 104q43 0 75 24t55 62v124q-6 22 -19.5 41t-31.5 33.5t-39 22.5t-42 8q-31 0 -56.5 -13
t-44 -35.5t-29 -51.5t-10.5 -60q0 -33 10.5 -61t29 -49t44.5 -33t58 -12z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="490" 
d="M124 233q0 49 16 88t66 68q15 9 37 20.5t43 27.5t35.5 37.5t14.5 49.5q0 21 -8 37t-21 27t-30.5 16.5t-35.5 5.5q-22 0 -40 -7.5t-33 -19t-26 -26t-19 -27.5l-87 60q13 31 34.5 55t49 40.5t59.5 25t66 8.5q38 0 75 -11t66.5 -35t48 -61.5t18.5 -89.5q0 -32 -7.5 -56.5
t-21.5 -44.5t-33 -36t-43 -31q-20 -12 -40 -23t-36.5 -24.5t-27 -31t-10.5 -42.5h-110zM126 0v138h111v-138h-111z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="483" 
d="M363 273q0 -49 -16 -88t-66 -68q-15 -9 -37 -20.5t-43 -27.5t-35.5 -37.5t-14.5 -49.5q0 -21 8 -37t21 -27t30.5 -16.5t35.5 -5.5q22 0 40 7.5t33 19t26 25.5t19 28l87 -60q-13 -31 -34.5 -55t-49 -40.5t-59.5 -25t-66 -8.5q-38 0 -75 11t-66.5 35t-48 61.5t-18.5 89.5
q0 32 7.5 56.5t21.5 44.5t33 36t43 31q20 12 40 23t36.5 24.5t27 30.5t10.5 43h110zM361 506v-138h-111v138h111z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="386" 
d="M57 539v184h114v-184h-114zM215 539v184h114v-184h-114z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="418" 
d="M32 -18q7 0 20 5.5t13 25.5v131h110v-155q0 -30 -14 -49.5t-35 -31.5t-46 -16.5t-48 -4.5v95zM214 -18q7 0 20 5.5t13 25.5v131h110v-155q0 -30 -14 -49.5t-35 -31.5t-46 -16.5t-48 -4.5v95z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="236" 
d="M32 -18q7 0 20 5.5t13 25.5v131h110v-155q0 -30 -14 -49.5t-35 -31.5t-46 -16.5t-48 -4.5v95z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="436" 
d="M190 496q-23 0 -48.5 4.5t-46.5 16.5t-35 31.5t-14 49.5v132h110v-107q2 -21 14.5 -26.5t19.5 -5.5v-95zM384 496q-23 0 -48.5 4.5t-46.5 16.5t-35 31.5t-14 49.5v132h110v-107q2 -21 14.5 -26.5t19.5 -5.5v-95z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="243" 
d="M190 496q-23 0 -48.5 4.5t-46.5 16.5t-35 31.5t-14 49.5v132h110v-107q2 -21 14.5 -26.5t19.5 -5.5v-95z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="438" 
d="M44 730q23 0 48.5 -4.5t46.5 -16.5t35 -31.5t14 -49.5v-132h-110v107q-2 21 -14.5 26.5t-19.5 5.5v95zM237 730q23 0 48.5 -4.5t46.5 -16.5t35 -31.5t14 -49.5v-132h-110v107q-2 21 -14.5 26.5t-19.5 5.5v95z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="244" 
d="M44 730q23 0 48.5 -4.5t46.5 -16.5t35 -31.5t14 -49.5v-132h-110v107q-2 21 -14.5 26.5t-19.5 5.5v95z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="222" 
d="M54 545v178h114v-178h-114z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="387" 
d="M372 408q-61 -1 -109 -23.5t-69 -67.5v-317h-134v524h123v-112q27 54 70.5 87t91.5 33q11 0 16.5 -0.5t10.5 -1.5v-122z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="387" 
d="M266 585l-86 26l54 119h125zM372 408q-61 -1 -109 -23.5t-69 -67.5v-317h-134v524h123v-112q27 54 70.5 87t91.5 33q11 0 16.5 -0.5t10.5 -1.5v-122z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="387" 
d="M147 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM372 408q-61 -1 -109 -23.5t-69 -67.5v-317h-134v524h123v-112q27 54 70.5 87t91.5 33q11 0 16.5 -0.5t10.5 -1.5v-122z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="387" 
d="M33 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM372 408q-61 -1 -109 -23.5t-69 -67.5v-317h-134v524h123v-112q27 54 70.5 87t91.5 33q11 0 16.5 -0.5t10.5 -1.5v-122z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="843" 
d="M420 -7q-81 0 -150 27.5t-119.5 76t-78.5 114.5t-28 144q0 77 28 143t78.5 114t119.5 75.5t150 27.5t150.5 -27.5t120.5 -75.5t80 -114t29 -143q0 -78 -29 -144t-80 -114.5t-120.5 -76t-150.5 -27.5zM420 42q68 0 127 23t102.5 64t68.5 98.5t25 125.5q0 66 -24.5 123
t-67.5 99t-102 66t-129 24t-128.5 -24t-101 -66t-66.5 -98.5t-24 -121.5q0 -66 24 -123t66.5 -99.5t101 -66.5t128.5 -24zM258 576h195q31 0 56.5 -13t44 -34t29 -47t10.5 -53q0 -41 -22 -77t-58 -50l103 -166h-107l-92 148h-64v-148h-95v440zM445 366q25 0 40 16.5t15 46.5
q0 32 -17 47.5t-40 15.5h-90v-126h92z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="508" 
d="M267 -10q-32 0 -66.5 5t-67.5 14t-63 22t-53 29l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5t67 34.5t83.5 12q56 0 111.5 -17.5t103.5 -47.5l-55 -83q-44 28 -83 40.5t-78 12.5
q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -38 -15.5 -69t-43.5 -52.5t-68.5 -33t-89.5 -11.5z" />
    <glyph glyph-name="s_t" unicode="&#xfb06;" horiz-adv-x="864" 
d="M857 27q-27 -12 -66.5 -24t-81.5 -12q-28 0 -53 7t-43.5 22.5t-29 40t-10.5 59.5v301h-69v103h69q0 43 -3 72.5t-11.5 47.5t-23.5 26t-40 8q-50 0 -72.5 -25.5t-22.5 -62.5q0 -19 5 -37.5t12 -34.5t15 -28t14 -19l-52 -84q-43 29 -79.5 40.5t-68.5 11.5q-35 0 -61 -13
t-26 -46q0 -18 9 -29.5t24.5 -19t35.5 -11.5t42 -8q49 -8 86.5 -20t62.5 -30t38 -43t13 -61q0 -42 -16 -73.5t-44.5 -52.5t-66.5 -31.5t-82 -10.5q-32 0 -65.5 5.5t-65 14.5t-59.5 21.5t-49 27.5l51 91q98 -64 185 -64q36 0 63 13t27 45q0 18 -9.5 29t-26 18t-38.5 11.5
t-46 10.5q-45 10 -80 21.5t-59 28t-36 40t-12 57.5q0 42 17 74.5t44.5 54.5t63 33.5t72.5 11.5q26 0 63 -6.5t67 -25.5q-11 22 -18 42.5t-7 49.5q0 30 10 56t32 45.5t57.5 31t85.5 11.5q62 0 97.5 -14t53.5 -41t23 -67t5 -92h110v-103h-110v-256q0 -28 14.5 -39.5
t36.5 -11.5q20 0 40 7t32 12z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="508" 
d="M288 585l-86 26l54 119h125zM267 -10q-32 0 -66.5 5t-67.5 14t-63 22t-53 29l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5t67 34.5t83.5 12q56 0 111.5 -17.5t103.5 -47.5l-55 -83
q-44 28 -83 40.5t-78 12.5q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -38 -15.5 -69t-43.5 -52.5t-68.5 -33t-89.5 -11.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="508" 
d="M168 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM267 -10q-32 0 -66.5 5t-67.5 14t-63 22t-53 29l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5t67 34.5t83.5 12q56 0 111.5 -17.5
t103.5 -47.5l-55 -83q-44 28 -83 40.5t-78 12.5q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -38 -15.5 -69t-43.5 -52.5t-68.5 -33t-89.5 -11.5z" />
    <glyph glyph-name="schwa" unicode="&#x259;" horiz-adv-x="600" 
d="M297 -10q-64 0 -115 22t-86.5 59t-54.5 86t-19 104q0 11 0.5 23t2.5 20h405q-3 31 -15 55t-31 41t-43 26t-50 9q-40 0 -75.5 -19.5t-48.5 -51.5l-115 32q29 60 92.5 98.5t151.5 38.5q63 0 114 -21.5t87 -58.5t55.5 -86t19.5 -104q0 -56 -19 -105.5t-54.5 -86.5t-86.5 -59
t-115 -22zM299 87q55 0 91.5 37.5t41.5 96.5h-271q2 -30 14 -54.5t30.5 -42.5t42.5 -27.5t51 -9.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="508" 
d="M98 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM267 -10q-32 0 -66.5 5t-67.5 14t-63 22t-53 29l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5t67 34.5t83.5 12q56 0 111.5 -17.5
t103.5 -47.5l-55 -83q-44 28 -83 40.5t-78 12.5q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -38 -15.5 -69t-43.5 -52.5t-68.5 -33t-89.5 -11.5z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="533" 
d="M456 215q16 -14 25.5 -39t9.5 -63q0 -54 -22 -92.5t-55 -62.5t-72 -35t-73 -11q-40 0 -74 11t-61.5 27t-48 34t-32.5 32l86 82q30 -32 60.5 -45.5t63.5 -13.5q17 0 33 4.5t28.5 13.5t20 21.5t7.5 29.5q0 18 -7.5 28.5t-19 16t-25.5 7.5t-28 3q-100 7 -153.5 54t-53.5 124
q0 29 7 46.5t12 27.5q-14 14 -27.5 42.5t-13.5 67.5t16.5 73.5t45.5 60.5t67 41t82 15q43 0 77 -12.5t59.5 -31t44 -40t31.5 -39.5l-103 -49q-14 26 -40.5 39.5t-60.5 13.5q-36 0 -60 -18.5t-24 -50.5q0 -37 24.5 -49t59.5 -13q37 -1 75.5 -10t69.5 -28.5t50.5 -51.5
t19.5 -78q0 -29 -6.5 -50.5t-14.5 -31.5zM195 323q0 -29 15.5 -42.5t37 -18.5t45 -6.5t39.5 -7.5q8 8 13 20.5t5 24.5q0 18 -8 30.5t-20.5 20t-27 10.5t-27.5 3q-17 0 -32.5 3t-23.5 6q-7 -7 -11.5 -18.5t-4.5 -24.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="250" 
d="M71 374v144h110v-144h-110zM38 -18q7 0 20 5.5t13 25.5v131h110v-155q0 -30 -14 -49.5t-35 -31.5t-46 -16.5t-48 -4.5v95z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="574" 
d="M360 461h-323v122h534l-353 -710h-151z" />
    <glyph glyph-name="seven.lnum" horiz-adv-x="562" 
d="M356 588h-322v122h534l-353 -710h-152z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="931" 
d="M895 123q0 -28 -14.5 -51.5t-38.5 -40.5t-56.5 -26.5t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 51q0 39 25.5 62.5t59.5 38.5q-27 11 -48 30.5t-21 50.5q0 25 14.5 46t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -29 -20 -50t-50 -32
q38 -16 62 -40.5t24 -62.5zM814 132q0 13 -8.5 24t-22 18.5t-31 11.5t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18.5t8 24zM635 300q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 9
t19.5 14t7.5 19q0 20 -25 32.5t-57 12.5q-33 0 -57.5 -12.5t-24.5 -32.5zM95 47l282 329l260 342l61 -54l-276 -322l-266 -349zM281 651h-239v79h364l-248 -420h-90z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="385" 
d="M263 651h-239v79h364l-248 -420h-90z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="387" 
d="M264 342h-239v80h363l-248 -422h-90z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="607" 
d="M567 237q0 -52 -20 -97t-54.5 -78.5t-82 -52.5t-102.5 -19q-60 0 -108.5 22t-82.5 64t-52.5 102.5t-18.5 137.5q0 97 19.5 171.5t55 125t86.5 76.5t114 26q67 0 124 -29t96 -83l-77 -84q-24 38 -62 59t-83 21q-67 0 -106.5 -53t-41.5 -150q20 36 59 57t90 21
q52 0 97.5 -18t78.5 -50t52 -75.5t19 -93.5zM308 103q27 0 50.5 10.5t41.5 29t28.5 43t10.5 51.5t-10.5 50.5t-28 41.5t-41.5 28t-51 10t-51 -10t-41.5 -28t-28 -41.5t-10.5 -50.5t10.5 -51.5t28.5 -42.5t41.5 -29t50.5 -11z" />
    <glyph glyph-name="six.lnum" horiz-adv-x="607" 
d="M567 237q0 -52 -20 -97t-54.5 -78.5t-82 -52.5t-102.5 -19q-60 0 -108.5 22t-82.5 64t-52.5 102.5t-18.5 137.5q0 97 19.5 171.5t55 125t86.5 76.5t114 26q67 0 124 -29t96 -83l-77 -84q-24 38 -62 59t-83 21q-67 0 -106.5 -53t-41.5 -150q20 36 59 57t90 21
q52 0 97.5 -18t78.5 -50t52 -75.5t19 -93.5zM308 103q27 0 50.5 10.5t41.5 29t28.5 43t10.5 51.5t-10.5 50.5t-28 41.5t-41.5 28t-51 10t-51 -10t-41.5 -28t-28 -41.5t-10.5 -50.5t10.5 -51.5t28.5 -42.5t41.5 -29t50.5 -11z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="425" 
d="M396 141q0 -31 -14 -57.5t-38 -46t-57 -31t-72 -11.5q-83 0 -132 51t-49 145q0 113 52 173.5t140 60.5q46 0 85 -16.5t64 -48.5l-46 -55q-16 21 -44.5 32.5t-61.5 11.5q-51 0 -82 -28t-33 -80q16 20 47.5 30t63.5 10q39 0 71.5 -11t56 -29.5t36.5 -44t13 -55.5zM217 67
q21 0 39.5 6t33 16t22.5 23t8 28q0 30 -30 51t-74 21q-21 0 -40 -5.5t-33.5 -15.5t-22.5 -22.5t-8 -27.5t8.5 -28.5t23 -23.5t33.5 -16t40 -6z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="425" 
d="M396 446q0 -31 -14 -57.5t-38 -46.5t-57 -31.5t-72 -11.5q-83 0 -132 51.5t-49 145.5q0 113 52 173.5t140 60.5q46 0 85 -16.5t64 -47.5l-46 -56q-16 21 -44.5 33t-61.5 12q-51 0 -82 -28.5t-33 -80.5q16 20 47.5 30t63.5 10q39 0 71.5 -11t56 -29.5t36.5 -44t13 -55.5z
M217 372q21 0 39.5 6t33 16t22.5 23.5t8 28.5q0 30 -30 50.5t-74 20.5q-43 0 -73.5 -20.5t-30.5 -50.5q0 -15 8.5 -28.5t23 -23.5t33.5 -16t40 -6z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="981" 
d="M430 710h505v-121h-333v-167h279v-121h-279v-180h342v-121h-478v177h-228l-105 -177h-147zM456 297v258l-159 -258h159z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="547" 
d="M56 381h72q-18 38 -35.5 76t-17.5 80q0 37 16 69.5t44 56.5t65.5 38t80.5 14q61 0 115.5 -28t91.5 -77l-70 -82q-25 35 -63.5 56.5t-75.5 21.5t-60.5 -23.5t-23.5 -58.5q0 -17 5.5 -34t13.5 -34.5t17.5 -36t18.5 -38.5h184v-102h-161q0 -46 -19.5 -86.5t-65.5 -88.5
q21 7 39.5 9.5t35.5 2.5q37 0 69.5 -8.5t66.5 -8.5q18 0 35.5 4t39.5 12l33 -100q-24 -14 -55 -22t-60 -8q-23 0 -45.5 4.5t-45.5 10t-46.5 10t-47.5 4.5q-26 0 -60.5 -6t-66.5 -16l-30 95q107 89 107 193h-101v101z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="382" 
d="M375 27q-27 -12 -66.5 -24t-82.5 -12q-28 0 -52.5 7t-43 22.5t-29.5 40t-11 59.5v301h-68v103h68v170h134v-170h111v-103h-111v-256q0 -28 15 -39.5t36 -11.5t41 7t32 12z" />
    <glyph glyph-name="t.alt" horiz-adv-x="366" 
d="M95 0v449h-79v120h79v163h137v-163h122v-120h-122v-449h-137z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="383" 
d="M376 27q-27 -12 -66.5 -24t-82.5 -12q-28 0 -52.5 7t-43 22.5t-29.5 40t-11 59.5v144h-68v80h68v77h-68v103h68v170h134v-170h111v-103h-111v-77h111v-80h-111v-99q0 -28 15 -39.5t36 -11.5t41 7t32 12z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="382" 
d="M357 585l-86 26l54 119h125zM375 27q-27 -12 -66.5 -24t-82.5 -12q-28 0 -52.5 7t-43 22.5t-29.5 40t-11 59.5v301h-68v103h68v170h134v-170h111v-103h-111v-256q0 -28 15 -39.5t36 -11.5t41 7t32 12z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="600" 
d="M573 256q0 -56 -21 -106.5t-57 -89t-83.5 -61.5t-101.5 -23q-46 0 -74 17t-39 34v-240h-137v923h137v-227q13 19 41 37.5t80 18.5q58 0 105.5 -25t80.5 -65.5t51 -91t18 -101.5zM435 258q0 32 -8.5 61t-24.5 51t-39.5 35t-52.5 13q-41 0 -68 -23.5t-45 -58.5v-160
q0 -14 10 -28t25.5 -25.5t35 -18.5t38.5 -7q28 0 51.5 13t41 34.5t27 51t9.5 62.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="559" 
d="M381 236q63 -11 101.5 -59.5t38.5 -118.5q0 -47 -18.5 -85t-52.5 -64.5t-82 -41t-107 -14.5q-77 0 -135.5 24.5t-96.5 71.5l76 95q26 -33 61.5 -51t89.5 -18q61 0 93.5 24t32.5 74q0 52 -37 80t-112 28h-28v100h31q61 0 95 27.5t34 70.5q0 41 -27.5 61t-72.5 20
q-46 0 -82.5 -21t-59.5 -59l-84 94q15 23 40 41.5t56.5 32t68.5 21t76 7.5q51 0 93 -13t72 -36t46.5 -55.5t16.5 -72.5q0 -30 -9 -57t-26 -49t-40.5 -37t-51.5 -20z" />
    <glyph glyph-name="three.lnum" horiz-adv-x="587" 
d="M274 -9q-87 0 -147.5 36t-84.5 103l71 88q3 -14 14 -31.5t30.5 -34t48 -27.5t66.5 -11q26 0 49.5 6.5t41.5 18.5t28.5 29.5t10.5 40.5q0 51 -54 76t-151 25h-33v112h33q40 0 74 5.5t59 17t39 29.5t14 43q0 22 -9.5 38t-24.5 26t-33.5 15t-36.5 5q-30 0 -55 -7.5t-44 -19
t-32.5 -26t-21.5 -28.5l-86 97q17 24 43.5 43t58 32t66.5 20t70 7q54 0 99.5 -15t78.5 -41.5t51.5 -62t18.5 -76.5q0 -60 -35.5 -98.5t-98.5 -52.5q34 -6 61.5 -22.5t47 -41t30 -55t10.5 -63.5q0 -51 -23 -88.5t-60.5 -62t-85.5 -37t-98 -12.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="968" 
d="M206 298q-63 0 -106.5 20.5t-57.5 58.5l43 59q2 -6 10.5 -16t23.5 -19t36.5 -15.5t48.5 -6.5q42 0 72.5 12.5t30.5 38.5q0 29 -40 42.5t-109 13.5h-24v73h24q60 0 97 12t37 40q0 12 -8 20.5t-20 14t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 64q23 29 68 44
t96 15q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -46q0 -35 -25 -58t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37.5t-58 -22t-67 -7zM931 123q0 -28 -14.5 -51.5t-38.5 -40.5t-56.5 -26.5t-68.5 -9.5q-38 0 -70.5 10t-56.5 27.5t-37.5 41.5t-13.5 51
q0 39 25.5 62.5t59.5 38.5q-27 11 -48 30.5t-21 50.5q0 25 14.5 46t37.5 36t52 23.5t58 8.5t58 -8t52 -23t37.5 -36t14.5 -46q0 -29 -20 -50t-50 -32q38 -16 62 -40.5t24 -62.5zM850 132q0 13 -8.5 24t-22 18.5t-31 11.5t-35.5 4q-19 0 -37 -4.5t-31.5 -12t-21.5 -18.5
t-8 -24t9 -23.5t23 -18t31.5 -11.5t35.5 -4t35.5 4t31 11.5t21.5 18.5t8 24zM671 300q0 -11 7.5 -19.5t19 -14t26 -8.5t29.5 -3q14 0 28.5 3t26.5 9t19.5 14t7.5 19q0 20 -25 32.5t-57 12.5q-33 0 -57.5 -12.5t-24.5 -32.5zM182 47l282 329l260 342l61 -54l-276 -322
l-266 -349z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="408" 
d="M193 298q-63 0 -106.5 20.5t-57.5 58.5l43 59q2 -6 10.5 -16t23.5 -19t36.5 -15.5t48.5 -6.5q42 0 72.5 12.5t30.5 38.5q0 29 -40 42.5t-109 13.5h-24v73h24q60 0 97 12t37 40q0 12 -8 20.5t-20 14t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 64q23 29 68 44
t96 15q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -46q0 -35 -25 -58t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37.5t-58 -22t-67 -7z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="945" 
d="M204 298q-63 0 -106.5 20.5t-57.5 58.5l43 59q2 -6 10.5 -16t23.5 -19t36.5 -15.5t48.5 -6.5q42 0 72.5 12.5t30.5 38.5q0 29 -40 42.5t-109 13.5h-24v73h24q60 0 97 12t37 40q0 12 -8 20.5t-20 14t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 64q23 29 68 44
t96 15q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -46q0 -35 -25 -58t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37.5t-58 -22t-67 -7zM764 0v93h-221v77l246 252h54v-251h61v-78h-61v-93h-79zM620 171h147v153zM208 47l282 329l260 342l61 -54l-276 -322l-266 -349
z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="826" 
d="M337 625h-95v-214h-88v214h-95v87h278v-87zM627 710h129v-299h-87v187l-64 -145h-71l-64 145v-187h-87v299h128l58 -157z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="569" 
d="M41 0q0 45 7 81.5t25 67.5t48 58t77 53q40 22 71.5 38.5t53 30.5t32.5 28.5t11 33.5q0 30 -24 51.5t-69 21.5q-24 0 -44 -6.5t-37.5 -17.5t-32 -24.5t-26.5 -28.5l-86 99q13 14 37 30.5t56.5 31t72 24t84.5 9.5q51 0 91 -12.5t67.5 -35t42 -54t14.5 -68.5q0 -35 -13 -62
t-33 -48t-43.5 -36.5t-45.5 -27.5q-18 -10 -38.5 -22t-40.5 -26.5t-36.5 -31.5t-25.5 -36h287v-121h-482z" />
    <glyph glyph-name="two.lnum" horiz-adv-x="612" 
d="M44 0q0 69 9.5 120.5t31 90.5t56.5 68t86 54q32 16 64.5 30.5t58 33.5t41.5 43.5t16 59.5q0 45 -27 71.5t-77 26.5q-29 0 -54.5 -8t-47 -20.5t-39.5 -27.5t-31 -29l-84 88q13 16 38 36.5t59.5 38.5t78.5 30.5t97 12.5q58 0 102.5 -16t75 -44.5t46 -68.5t15.5 -88
q0 -46 -14 -79.5t-38 -59t-56 -45t-67 -37.5q-38 -19 -66 -36.5t-47 -36.5t-28.5 -40t-9.5 -47h327v-121h-516z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="417" 
d="M31 305q0 41 7 71.5t22 53.5t40 40.5t61 32.5q24 10 47.5 19t42 19.5t30.5 24t12 33.5q0 55 -85 55q-44 0 -75 -16t-49 -33l-50 57q7 10 23 22t39.5 22.5t54 17.5t68.5 7q81 0 122.5 -35t41.5 -98q0 -33 -13 -55.5t-33 -38t-43 -25t-43 -15.5q-50 -15 -70.5 -36.5
t-20.5 -44.5h211v-78h-340z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="975" 
d="M755 -5q-63 0 -106.5 20t-57.5 59l43 58q2 -6 10.5 -16t23.5 -19.5t36.5 -16t48.5 -6.5q42 0 72.5 13t30.5 39q0 29 -40 42.5t-109 13.5h-24v72h24q60 0 97 12.5t37 39.5q0 12 -8 21t-20 14.5t-27 8.5t-30 3q-40 0 -67.5 -13.5t-45.5 -35.5l-51 63q23 29 68 44.5t96 15.5
q36 0 67.5 -9t54 -24.5t35.5 -36.5t13 -45q0 -36 -25 -59t-70 -31q49 -7 77 -37t28 -71q0 -31 -15.5 -53.5t-40.5 -37t-58 -21.5t-67 -7zM43 305q0 41 6.5 71.5t21 53.5t38 40.5t57.5 32.5q22 10 44 19t40 19.5t29 24t11 33.5q0 55 -80 55q-42 0 -71 -16t-46 -33l-47 57
q7 10 22 22t37 22.5t51 17.5t64 7q76 0 115.5 -35t39.5 -98q0 -33 -12.5 -55.5t-31.5 -38t-40.5 -25t-40.5 -15.5q-47 -15 -66 -36.5t-19 -44.5h198v-78h-320zM189 47l282 329l260 342l61 -54l-276 -322l-266 -349z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27z" />
    <glyph glyph-name="u.alt" horiz-adv-x="605" 
d="M417 0v90q-29 -51 -80.5 -75.5t-108.5 -24.5q-47 0 -80 14.5t-53.5 41.5t-30 65t-9.5 85v328h134v-299q0 -26 4.5 -48.5t15 -38.5t28 -25t43.5 -9q38 0 73.5 25t58.5 70v325h134v-524h-129z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="622" 
d="M334 585l-86 26l54 119h125zM220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27zM299 656q23 0 40 21t20 53h71
q0 -29 -10 -53.5t-27 -42.5t-41.5 -28t-52.5 -10q-29 0 -53 10t-41 28t-27 42.5t-10 53.5h72q0 -30 18 -52t41 -22z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="622" 
d="M145 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81
t-121.5 -27z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="622" 
d="M148 604v119h111v-119h-111zM340 604v119h111v-119h-111zM220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81
t-121.5 -27z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27zM173 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27zM237 585l-66 26l48 119h104zM397 585l-65 26
l47 119h105z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27zM151 633v83h300v-83h-300z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="382" 
d="M8 -121v121h366v-121h-366z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="239" 
 />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="602" 
d="M57 214v121h488v-121h-488z" />
    <glyph glyph-name="uni015E" unicode="&#x15e;" horiz-adv-x="615" 
d="M240 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l43 85q-74 4 -145 27t-128 64l61 119q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5
t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5q75 0 138 -23.5t110 -55.5l-61 -112q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5
q0 -53 -18.5 -91t-50 -63.5t-73 -39t-88.5 -16.5l-23 -35q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="uni015F" unicode="&#x15f;" horiz-adv-x="509" 
d="M187 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l42 83q-60 3 -121.5 21.5t-103.5 47.5l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5
t67 34.5t83.5 12q56 0 111.5 -17.5t103.5 -47.5l-55 -83q-44 28 -83 40.5t-78 12.5q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -71 -49.5 -114t-135.5 -50l-21 -34q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5
q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="620" 
d="M235 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l47 92h-54v589h-227v121h591v-121h-226v-589h-26l-27 -42q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="382" 
d="M147 -127q12 -7 37 -13t52 -6q26 0 41 7.5t15 25.5t-14.5 25.5t-37.5 7.5q-21 0 -44.5 -4.5t-35.5 -7.5l43 84q-48 5 -80.5 35.5t-32.5 92.5v301h-68v103h68v170h134v-170h111v-103h-111v-256q0 -28 15 -39.5t36 -11.5t41 7t32 12l27 -106q-22 -10 -51.5 -19.5
t-62.5 -14.5l-23 -35q7 2 15.5 3t16.5 1q35 0 60 -16.5t25 -53.5q0 -38 -31.5 -63t-96.5 -25q-33 0 -60 6t-47 14z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="615" 
d="M236 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM498 524q-7 7 -25.5 19t-45 23.5t-57.5 19.5t-63 8q-57 0 -85 -21t-28 -59q0 -22 10.5 -36.5t30.5 -25.5t50.5 -20t70.5 -19q52 -14 94.5 -30t72 -40t45.5 -57.5t16 -82.5
q0 -57 -21.5 -97.5t-57.5 -65.5t-84 -36.5t-100 -11.5q-80 0 -158 24t-140 68l61 119q9 -9 32.5 -23.5t55.5 -29t71 -24.5t80 -10q114 0 114 73q0 24 -13 40t-37 28t-58 22t-76 21q-51 14 -88.5 30.5t-62.5 38.5t-37.5 52t-12.5 71q0 54 20 96t55.5 70t82.5 42.5t101 14.5
q75 0 138 -23.5t110 -55.5z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="508" 
d="M177 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM267 -10q-32 0 -66.5 5t-67.5 14t-63 22t-53 29l54 91q100 -65 193 -65q42 0 66 15t24 43t-28.5 41.5t-95.5 29.5q-51 12 -87 25t-58 30t-32 39t-10 52q0 40 16 72t44.5 54.5t67 34.5
t83.5 12q56 0 111.5 -17.5t103.5 -47.5l-55 -83q-44 28 -83 40.5t-78 12.5q-36 0 -60.5 -14t-24.5 -45q0 -27 23.5 -39t83.5 -27q56 -14 95.5 -28t65 -32t37 -41.5t11.5 -56.5q0 -38 -15.5 -69t-43.5 -52.5t-68.5 -33t-89.5 -11.5z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="620" 
d="M226 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM605 589h-226v-589h-138v589h-227v121h591v-121z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="382" 
d="M137 -136q33 0 33 25v64h109v-69q0 -25 -11 -39.5t-30 -22.5t-45 -10.5t-56 -2.5v55zM375 27q-27 -12 -66.5 -24t-82.5 -12q-28 0 -52.5 7t-43 22.5t-29.5 40t-11 59.5v301h-68v103h68v170h134v-170h111v-103h-111v-256q0 -28 15 -39.5t36 -11.5t41 7t32 12z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="629" 
d="M60 524h134v-299q0 -121 90 -121q37 0 73 23.5t59 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -36.5 -5.5t-28.5 -1.5q-35 0 -56 16.5t-26 45.5l-3 43q-8 -18 -24.5 -37t-38 -35t-47 -26t-51.5 -10q-20 0 -35 6t-26.5 14.5t-18.5 19t-10 19.5l9 -261h-134v736z
" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="616" 
d="M-16 56l571 662l77 -64l-571 -661z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="224" 
d="M57 242v151h110v-151h-110z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-82 -48 -82 -90q0 -37 62 -40l-28 -66q-53 8 -79.5 32t-26.5 55q0 55 72 103q-57 8 -65 61l-3 43q-36 -54 -90.5 -81t-121.5 -27z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="622" 
d="M203 653q0 37 26.5 57t70.5 20t70.5 -20t26.5 -57t-26.5 -57t-70.5 -20t-70.5 20t-26.5 57zM300 689q-17 0 -28.5 -9.5t-11.5 -26.5q0 -15 11.5 -25.5t28.5 -10.5q16 0 28.5 10t12.5 26q0 17 -12 26.5t-29 9.5zM220 -10q-81 0 -123 52t-42 154v328h134v-299
q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="622" 
d="M220 -10q-81 0 -123 52t-42 154v328h134v-299q0 -121 87 -121q40 0 76.5 23.5t59.5 71.5v325h134v-370q0 -21 8 -30t28 -10v-114q-21 -4 -37 -5.5t-28 -1.5q-36 0 -56.5 16.5t-25.5 45.5l-3 43q-36 -54 -90.5 -81t-121.5 -27zM363 602q-23 0 -40 7t-31.5 15.5t-29 15.5
t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="543" 
d="M199 0l-193 524h138l133 -415l134 415h126l-193 -524h-145z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="845" 
d="M713 524h128l-219 -524h-110l-90 230l-89 -230h-110l-219 524h127l156 -398l64 170l-92 227h108l55 -161l56 161h108l-91 -227l63 -170z" />
    <glyph glyph-name="w.alt" horiz-adv-x="867" 
d="M533 0l-100 316l-100 -316h-145l-180 524h138l120 -364l104 364h126l104 -364l120 364h138l-180 -524h-145z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="845" 
d="M457 585l-86 26l54 119h125zM713 524h128l-219 -524h-110l-90 230l-89 -230h-110l-219 524h127l156 -398l64 170l-92 227h108l55 -161l56 161h108l-91 -227l63 -170z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="845" 
d="M268 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63zM713 524h128l-219 -524h-110l-90 230l-89 -230h-110l-219 524h127l156 -398l64 170l-92 227h108l55 -161l56 161h108l-91 -227l63 -170z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="845" 
d="M271 604v119h111v-119h-111zM463 604v119h111v-119h-111zM713 524h128l-219 -524h-110l-90 230l-89 -230h-110l-219 524h127l156 -398l64 170l-92 227h108l55 -161l56 161h108l-91 -227l63 -170z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="845" 
d="M713 524h128l-219 -524h-110l-90 230l-89 -230h-110l-219 524h127l156 -398l64 170l-92 227h108l55 -161l56 161h108l-91 -227l63 -170zM295 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="528" 
d="M143 524l112 -164l9 -18l9 18l112 164h138l-186 -263l189 -261h-138l-115 163l-9 17l-9 -17l-115 -163h-138l190 261l-187 263h138z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116z" />
    <glyph glyph-name="y.alt" horiz-adv-x="544" 
d="M538 524l-255 -737h-155q0 1 10 26t23.5 59t27.5 70t24 58l-208 524h139l142 -408l126 408h126z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116zM316 585l-86 26l54 119h125z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116zM127 629l106 101h97l106 -101l-69 -32l-85 63l-85 -63z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116zM130 604v119h111v-119h-111zM322 604v119h111v-119h-111z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="713" 
d="M506 402h73v-87h-120l-36 -67v-30h157v-87h-157v-131h-137v131h-155v87h155v32l-34 65h-120v87h74l-176 308h149l176 -342l177 342h150z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116zM154 730h124l55 -119l-86 -26z" />
    <glyph glyph-name="ytilde" unicode="&#x1ef9;" horiz-adv-x="544" 
d="M67 -102q18 -5 33 -7t26 -2q13 0 23 4.5t19.5 16.5t19 33.5t20.5 56.5l-208 524h138l143 -408l126 408h126l-221 -628q-21 -60 -67 -92.5t-107 -32.5q-35 0 -71 11v116zM344 602q-23 0 -40 7t-31.5 15.5t-29 15.5t-30.5 7q-17 0 -26.5 -7t-14.5 -16t-6 -16.5t-1 -8.5h-74
q0 9 6 30.5t19.5 43t36 38t55.5 16.5q23 0 39.5 -7.5t31 -16t29 -16t32.5 -7.5t28.5 7t16 16t7 17t1.5 10h74q0 -7 -6 -28t-20.5 -43t-38 -39.5t-58.5 -17.5z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="490" 
d="M25 82l291 348h-280v94h421v-82l-290 -348h295v-94h-437v82z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="490" 
d="M283 585l-86 26l54 119h125zM25 82l291 348h-280v94h421v-82l-290 -348h295v-94h-437v82z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="490" 
d="M163 730l85 -63l85 63l69 -32l-106 -101h-97l-106 101zM25 82l291 348h-280v94h421v-82l-290 -348h295v-94h-437v82z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="490" 
d="M25 82l291 348h-280v94h421v-82l-290 -348h295v-94h-437v82zM187 604v126h121v-126h-121z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="615" 
d="M566 293q0 -66 -19.5 -121.5t-54 -96t-82 -63t-102.5 -22.5q-56 0 -103.5 22.5t-82 63t-54 96t-19.5 121.5t19.5 121.5t54 96t82 63.5t103.5 23q55 0 102.5 -23t82 -63.5t54 -96t19.5 -121.5zM431 293q0 40 -9 73.5t-25.5 57.5t-39 37t-49.5 13q-28 0 -51 -13t-39.5 -37
t-25.5 -57.5t-9 -73.5t9 -73t25.5 -57t39.5 -37t51 -13q27 0 49.5 13t39 37t25.5 57t9 73z" />
    <glyph glyph-name="zero.lnum" horiz-adv-x="702" 
d="M351 -10q-76 1 -134 34.5t-97 86t-58.5 116t-19.5 123.5q0 64 21.5 128.5t61.5 116t97 83.5t129 32q76 0 134 -34.5t97 -87.5t58.5 -116.5t19.5 -121.5q0 -65 -21.5 -129t-61.5 -115.5t-97 -83.5t-129 -32zM181 350q0 -42 10.5 -84t31.5 -76.5t53 -56t75 -21.5
q45 0 77 23t52.5 58.5t30.5 77t10 79.5q0 42 -11 84t-32 76.5t-53 56t-74 21.5q-45 0 -77 -23t-52.5 -58.5t-30.5 -77.5t-10 -79z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="488" 
d="M244 -5q-53 0 -92.5 20t-66 51t-40 68.5t-13.5 73.5q0 38 14.5 76t42 68.5t67 49.5t88.5 19q52 0 92 -20.5t66.5 -51.5t40 -69t13.5 -72q0 -39 -14.5 -77t-42 -68t-67 -49t-88.5 -19zM114 208q0 -24 8 -47.5t24.5 -43t40.5 -31.5t57 -12q34 0 59 13t41 32.5t23.5 43.5
t7.5 45q0 24 -8.5 47.5t-25 43t-41 31.5t-56.5 12q-34 0 -58.5 -13t-40.5 -33t-23.5 -43.5t-7.5 -44.5z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="488" 
d="M244 303q-53 0 -92.5 20t-66 51t-40 69t-13.5 73q0 38 14.5 76.5t42 69t67 49.5t88.5 19q52 0 92 -20.5t66.5 -52t40 -69.5t13.5 -72q0 -38 -14.5 -76t-42 -68.5t-67 -49.5t-88.5 -19zM114 516q0 -23 8 -47t24.5 -43t40.5 -31t57 -12q34 0 59 13t41 32.5t23.5 43
t7.5 44.5q0 24 -8.5 48t-25 43.5t-41 31.5t-56.5 12q-34 0 -58.5 -13t-40.5 -33t-23.5 -44t-7.5 -45z" />
    <hkern u1="&#x20;" g2="y.alt" k="27" />
    <hkern u1="&#x20;" u2="y" k="27" />
    <hkern u1="&#x20;" g2="w.alt" k="26" />
    <hkern u1="&#x20;" u2="w" k="28" />
    <hkern u1="&#x20;" u2="v" k="26" />
    <hkern u1="&#x20;" u2="&#xc6;" k="35" />
    <hkern u1="&#x20;" u2="&#x27;" k="23" />
    <hkern u1="&#x20;" u2="&#x2019;" k="22" />
    <hkern u1="&#x20;" u2="&#x22;" k="14" />
    <hkern u1="&#x20;" u2="&#x142;" k="22" />
    <hkern u1="&#x20;" u2="Y" k="37" />
    <hkern u1="&#x20;" u2="X" k="4" />
    <hkern u1="&#x20;" u2="W" k="35" />
    <hkern u1="&#x20;" u2="V" k="35" />
    <hkern u1="&#x20;" u2="&#x166;" k="6" />
    <hkern u1="&#x20;" u2="T" k="28" />
    <hkern u1="&#x20;" u2="A" k="31" />
    <hkern u1="&#x20;" u2="&#x141;" k="7" />
    <hkern u1="&#x20;" u2="J" k="29" />
    <hkern u1="&#x104;" u2="y" k="8" />
    <hkern u1="&#x104;" u2="&#x201a;" k="-9" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-9" />
    <hkern u1="&#x104;" u2="j" k="-118" />
    <hkern u1="&#x104;" u2="g" k="-7" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-9" />
    <hkern u1="B" u2="z" k="5" />
    <hkern u1="B" g2="y.alt" k="10" />
    <hkern u1="B" u2="y" k="10" />
    <hkern u1="B" u2="x" k="13" />
    <hkern u1="B" g2="w.alt" k="9" />
    <hkern u1="B" u2="w" k="11" />
    <hkern u1="B" u2="v" k="9" />
    <hkern u1="B" u2="u" k="3" />
    <hkern u1="B" u2="&#x33;" k="3" />
    <hkern u1="B" g2="t.alt" k="8" />
    <hkern u1="B" u2="t" k="11" />
    <hkern u1="B" u2="&#xc6;" k="11" />
    <hkern u1="B" u2="&#x37;" k="5" />
    <hkern u1="B" u2="&#x259;" k="3" />
    <hkern u1="B" u2="s" k="7" />
    <hkern u1="B" u2="o" k="3" />
    <hkern u1="B" u2="n" k="3" />
    <hkern u1="B" u2="&#x135;" k="-10" />
    <hkern u1="B" u2="&#x129;" k="-30" />
    <hkern u1="B" u2="&#xef;" k="-12" />
    <hkern u1="B" u2="&#xee;" k="-10" />
    <hkern u1="B" u2="f" k="12" />
    <hkern u1="B" u2="&#xf0;" k="4" />
    <hkern u1="B" u2="d" k="3" />
    <hkern u1="B" u2="&#x2bc;" k="12" />
    <hkern u1="B" u2="Z" k="2" />
    <hkern u1="B" u2="Y" k="29" />
    <hkern u1="B" u2="X" k="14" />
    <hkern u1="B" u2="W" k="21" />
    <hkern u1="B" u2="V" k="20" />
    <hkern u1="B" u2="T" k="21" />
    <hkern u1="B" u2="A" k="14" />
    <hkern u1="B" u2="J" k="7" />
    <hkern u1="C" u2="z" k="4" />
    <hkern u1="C" u2="y" k="4" />
    <hkern u1="C" u2="w" k="3" />
    <hkern u1="C" u2="u" k="3" />
    <hkern u1="C" u2="&#x2122;" k="-12" />
    <hkern u1="C" u2="t" k="5" />
    <hkern u1="C" u2="&#xc6;" k="8" />
    <hkern u1="C" u2="&#x259;" k="4" />
    <hkern u1="C" u2="s" k="6" />
    <hkern u1="C" u2="o" k="5" />
    <hkern u1="C" u2="n" k="3" />
    <hkern u1="C" u2="&#x135;" k="-17" />
    <hkern u1="C" u2="&#x129;" k="-52" />
    <hkern u1="C" u2="&#xef;" k="-31" />
    <hkern u1="C" u2="&#xee;" k="-18" />
    <hkern u1="C" u2="f" k="8" />
    <hkern u1="C" u2="&#xf0;" k="5" />
    <hkern u1="C" u2="d" k="5" />
    <hkern u1="C" u2="&#x2a;" k="-17" />
    <hkern u1="C" u2="&#x2bc;" k="24" />
    <hkern u1="C" u2="Y" k="17" />
    <hkern u1="C" u2="X" k="6" />
    <hkern u1="C" u2="W" k="11" />
    <hkern u1="C" u2="V" k="11" />
    <hkern u1="C" u2="&#x166;" k="8" />
    <hkern u1="C" u2="T" k="4" />
    <hkern u1="C" u2="S" k="2" />
    <hkern u1="C" u2="O" k="8" />
    <hkern u1="C" u2="A" k="10" />
    <hkern u1="D" u2="z" k="5" />
    <hkern u1="D" g2="y.alt" k="3" />
    <hkern u1="D" u2="y" k="5" />
    <hkern u1="D" u2="x" k="10" />
    <hkern u1="D" u2="w" k="3" />
    <hkern u1="D" u2="u" k="3" />
    <hkern u1="D" u2="&#x2122;" k="3" />
    <hkern u1="D" u2="&#xc6;" k="39" />
    <hkern u1="D" u2="s" k="5" />
    <hkern u1="D" u2="&#x27;" k="4" />
    <hkern u1="D" u2="&#x2018;" k="3" />
    <hkern u1="D" u2="&#x2e;" k="8" />
    <hkern u1="D" u2="&#x29;" k="12" />
    <hkern u1="D" u2="n" k="5" />
    <hkern u1="D" u2="&#x142;" k="-11" />
    <hkern u1="D" g2="l.alt" k="3" />
    <hkern u1="D" u2="&#x129;" k="-10" />
    <hkern u1="D" u2="i" k="3" />
    <hkern u1="D" u2="h" k="3" />
    <hkern u1="D" u2="&#x34;" k="3" />
    <hkern u1="D" u2="&#xf0;" k="5" />
    <hkern u1="D" u2="\" k="15" />
    <hkern u1="D" u2="&#x2bc;" k="40" />
    <hkern u1="D" u2="a" k="8" />
    <hkern u1="D" u2="Z" k="13" />
    <hkern u1="D" u2="Y" k="39" />
    <hkern u1="D" u2="X" k="28" />
    <hkern u1="D" u2="W" k="26" />
    <hkern u1="D" u2="V" k="24" />
    <hkern u1="D" u2="&#x166;" k="4" />
    <hkern u1="D" u2="T" k="22" />
    <hkern u1="D" u2="S" k="4" />
    <hkern u1="D" u2="&#x2f;" k="21" />
    <hkern u1="D" u2="A" k="25" />
    <hkern u1="D" u2="&#x141;" k="6" />
    <hkern u1="D" u2="J" k="29" />
    <hkern u1="D" u2="&#x126;" k="4" />
    <hkern u1="D" u2="&#xd0;" k="6" />
    <hkern u1="D" u2="&#x110;" k="6" />
    <hkern u1="E" g2="y.alt" k="15" />
    <hkern u1="E" u2="y" k="15" />
    <hkern u1="E" g2="w.alt" k="11" />
    <hkern u1="E" u2="w" k="14" />
    <hkern u1="E" u2="v" k="10" />
    <hkern u1="E" u2="u" k="9" />
    <hkern u1="E" g2="t.alt" k="4" />
    <hkern u1="E" u2="t" k="10" />
    <hkern u1="E" u2="&#x37;" k="7" />
    <hkern u1="E" u2="&#x259;" k="13" />
    <hkern u1="E" u2="s" k="2" />
    <hkern u1="E" u2="o" k="14" />
    <hkern u1="E" u2="n" k="2" />
    <hkern u1="E" u2="&#x135;" k="-18" />
    <hkern u1="E" u2="&#x129;" k="-46" />
    <hkern u1="E" u2="&#x12b;" k="-5" />
    <hkern u1="E" u2="&#xef;" k="-27" />
    <hkern u1="E" u2="&#xee;" k="-18" />
    <hkern u1="E" u2="&#x2d;" k="7" />
    <hkern u1="E" u2="&#x2039;" k="6" />
    <hkern u1="E" u2="f" k="14" />
    <hkern u1="E" u2="&#xf0;" k="14" />
    <hkern u1="E" u2="d" k="14" />
    <hkern u1="E" u2="&#x2bc;" k="19" />
    <hkern u1="E" u2="a" k="3" />
    <hkern u1="E" u2="S" k="9" />
    <hkern u1="E" u2="O" k="12" />
    <hkern u1="&#x14a;" u2="j" k="-27" />
    <hkern u1="&#x20ac;" u2="&#x33;" k="3" />
    <hkern u1="F" u2="z" k="21" />
    <hkern u1="F" g2="y.alt" k="12" />
    <hkern u1="F" u2="y" k="11" />
    <hkern u1="F" u2="x" k="13" />
    <hkern u1="F" g2="w.alt" k="9" />
    <hkern u1="F" u2="w" k="11" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="u" k="18" />
    <hkern u1="F" u2="&#x32;" k="11" />
    <hkern u1="F" u2="&#x2122;" k="-3" />
    <hkern u1="F" u2="&#x33;" k="9" />
    <hkern u1="F" g2="t.alt" k="-2" />
    <hkern u1="F" u2="t" k="4" />
    <hkern u1="F" u2="&#xc6;" k="93" />
    <hkern u1="F" u2="&#x37;" k="9" />
    <hkern u1="F" u2="&#x259;" k="35" />
    <hkern u1="F" u2="s" k="33" />
    <hkern u1="F" u2="&#x2e;" k="53" />
    <hkern u1="F" u2="&#x29;" k="-4" />
    <hkern u1="F" u2="&#x31;" k="6" />
    <hkern u1="F" u2="o" k="27" />
    <hkern u1="F" u2="n" k="21" />
    <hkern u1="F" u2="&#x135;" k="-37" />
    <hkern u1="F" u2="&#x129;" k="-71" />
    <hkern u1="F" u2="&#x12b;" k="-33" />
    <hkern u1="F" u2="&#xec;" k="-13" />
    <hkern u1="F" u2="&#xef;" k="-45" />
    <hkern u1="F" u2="&#xee;" k="-37" />
    <hkern u1="F" u2="&#x12d;" k="-22" />
    <hkern u1="F" u2="&#xed;" k="8" />
    <hkern u1="F" u2="&#x2d;" k="10" />
    <hkern u1="F" u2="&#x203a;" k="8" />
    <hkern u1="F" u2="&#x2039;" k="9" />
    <hkern u1="F" u2="&#xdf;" k="5" />
    <hkern u1="F" u2="&#x34;" k="34" />
    <hkern u1="F" u2="&#x35;" k="13" />
    <hkern u1="F" g2="f_f_l" k="10" />
    <hkern u1="F" g2="f_f_i" k="10" />
    <hkern u1="F" g2="f_f" k="10" />
    <hkern u1="F" u2="f" k="13" />
    <hkern u1="F" u2="&#xf0;" k="37" />
    <hkern u1="F" u2="&#x131;" k="21" />
    <hkern u1="F" u2="d" k="27" />
    <hkern u1="F" u2="&#x3a;" k="6" />
    <hkern u1="F" u2="&#x40;" k="3" />
    <hkern u1="F" u2="&#x2a;" k="-15" />
    <hkern u1="F" u2="&#x2bc;" k="4" />
    <hkern u1="F" u2="a" k="41" />
    <hkern u1="F" u2="S" k="17" />
    <hkern u1="F" u2="&#x2f;" k="53" />
    <hkern u1="F" u2="O" k="14" />
    <hkern u1="F" u2="A" k="57" />
    <hkern u1="F" u2="J" k="96" />
    <hkern u1="F" u2="&#x20;" k="23" />
    <hkern u1="G" g2="y.alt" k="13" />
    <hkern u1="G" u2="y" k="12" />
    <hkern u1="G" g2="w.alt" k="11" />
    <hkern u1="G" u2="w" k="14" />
    <hkern u1="G" u2="v" k="12" />
    <hkern u1="G" u2="&#x2122;" k="3" />
    <hkern u1="G" g2="t.alt" k="7" />
    <hkern u1="G" u2="t" k="7" />
    <hkern u1="G" u2="&#x27;" k="4" />
    <hkern u1="G" u2="&#x142;" k="-4" />
    <hkern u1="G" u2="&#x129;" k="-23" />
    <hkern u1="G" u2="f" k="6" />
    <hkern u1="G" u2="&#xf0;" k="3" />
    <hkern u1="G" u2="\" k="8" />
    <hkern u1="G" u2="&#x2bc;" k="36" />
    <hkern u1="G" u2="Y" k="32" />
    <hkern u1="G" u2="W" k="24" />
    <hkern u1="G" u2="V" k="23" />
    <hkern u1="G" u2="&#x166;" k="11" />
    <hkern u1="G" u2="T" k="18" />
    <hkern g1="G.alt" u2="z" k="7" />
    <hkern g1="G.alt" g2="y.alt" k="8" />
    <hkern g1="G.alt" u2="y" k="8" />
    <hkern g1="G.alt" u2="x" k="9" />
    <hkern g1="G.alt" g2="w.alt" k="6" />
    <hkern g1="G.alt" u2="w" k="7" />
    <hkern g1="G.alt" u2="v" k="7" />
    <hkern g1="G.alt" u2="&#x2122;" k="4" />
    <hkern g1="G.alt" g2="t.alt" k="3" />
    <hkern g1="G.alt" u2="t" k="2" />
    <hkern g1="G.alt" u2="&#x37;" k="-5" />
    <hkern g1="G.alt" u2="s" k="3" />
    <hkern g1="G.alt" u2="&#x27;" k="5" />
    <hkern g1="G.alt" u2="&#x2019;" k="3" />
    <hkern g1="G.alt" u2="&#x2018;" k="3" />
    <hkern g1="G.alt" u2="&#x2e;" k="6" />
    <hkern g1="G.alt" u2="&#x142;" k="-15" />
    <hkern g1="G.alt" u2="&#x129;" k="-25" />
    <hkern g1="G.alt" u2="&#xef;" k="-4" />
    <hkern g1="G.alt" u2="\" k="10" />
    <hkern g1="G.alt" u2="&#x2a;" k="-7" />
    <hkern g1="G.alt" u2="&#x2bc;" k="37" />
    <hkern g1="G.alt" u2="a" k="4" />
    <hkern g1="G.alt" u2="&#x2f;" k="12" />
    <hkern u1="&#x126;" g2="y.alt" k="2" />
    <hkern u1="&#x126;" u2="y" k="2" />
    <hkern u1="&#x126;" g2="w.alt" k="2" />
    <hkern u1="&#x126;" g2="t.alt" k="-3" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-12" />
    <hkern u1="&#x126;" u2="O" k="4" />
    <hkern u1="I" u2="z" k="4" />
    <hkern u1="I" u2="y" k="3" />
    <hkern u1="I" g2="t.alt" k="6" />
    <hkern u1="I" u2="t" k="8" />
    <hkern u1="I" u2="&#x259;" k="7" />
    <hkern u1="I" u2="s" k="6" />
    <hkern u1="I" u2="o" k="9" />
    <hkern u1="I" u2="&#x129;" k="-27" />
    <hkern u1="I" u2="&#xef;" k="-12" />
    <hkern u1="I" u2="f" k="8" />
    <hkern u1="I" u2="&#xf0;" k="11" />
    <hkern u1="I" u2="d" k="8" />
    <hkern u1="I" u2="&#x2bc;" k="21" />
    <hkern u1="I" u2="a" k="7" />
    <hkern u1="J" u2="z" k="6" />
    <hkern u1="J" u2="x" k="4" />
    <hkern u1="J" u2="u" k="5" />
    <hkern u1="J" g2="t.alt" k="2" />
    <hkern u1="J" u2="t" k="6" />
    <hkern u1="J" u2="&#xc6;" k="24" />
    <hkern u1="J" u2="&#x259;" k="7" />
    <hkern u1="J" u2="s" k="12" />
    <hkern u1="J" u2="&#x2e;" k="4" />
    <hkern u1="J" u2="o" k="9" />
    <hkern u1="J" u2="n" k="5" />
    <hkern u1="J" g2="l.alt" k="3" />
    <hkern u1="J" u2="l" k="3" />
    <hkern u1="J" u2="&#x135;" k="-9" />
    <hkern u1="J" u2="&#x129;" k="-34" />
    <hkern u1="J" u2="&#xef;" k="-17" />
    <hkern u1="J" u2="&#xee;" k="-9" />
    <hkern u1="J" u2="i" k="3" />
    <hkern u1="J" u2="h" k="3" />
    <hkern u1="J" u2="f" k="7" />
    <hkern u1="J" u2="&#xf0;" k="12" />
    <hkern u1="J" u2="d" k="9" />
    <hkern u1="J" u2="&#x2bc;" k="19" />
    <hkern u1="J" u2="a" k="10" />
    <hkern u1="J" u2="&#x2f;" k="10" />
    <hkern u1="J" u2="A" k="17" />
    <hkern u1="J" u2="J" k="15" />
    <hkern u1="&#xcf;" u2="&#x7c;" k="-7" />
    <hkern u1="&#x12e;" u2="j" k="-45" />
    <hkern u1="K" u2="&#x30;" k="3" />
    <hkern u1="K" g2="y.alt" k="39" />
    <hkern u1="K" u2="y" k="37" />
    <hkern u1="K" g2="w.alt" k="37" />
    <hkern u1="K" u2="w" k="41" />
    <hkern u1="K" u2="v" k="38" />
    <hkern u1="K" u2="u" k="16" />
    <hkern u1="K" u2="&#x32;" k="-15" />
    <hkern u1="K" u2="&#x2122;" k="-12" />
    <hkern u1="K" g2="t.alt" k="6" />
    <hkern u1="K" u2="t" k="18" />
    <hkern u1="K" u2="&#x3b;" k="-7" />
    <hkern u1="K" u2="&#x259;" k="20" />
    <hkern u1="K" u2="&#xae;" k="15" />
    <hkern u1="K" u2="&#x201a;" k="-13" />
    <hkern u1="K" u2="&#x201e;" k="-13" />
    <hkern u1="K" u2="&#x29;" k="-23" />
    <hkern u1="K" u2="&#x1ff;" k="15" />
    <hkern u1="K" u2="&#xf8;" k="15" />
    <hkern u1="K" u2="o" k="32" />
    <hkern u1="K" u2="&#x135;" k="-10" />
    <hkern u1="K" u2="&#x129;" k="-42" />
    <hkern u1="K" u2="&#x12b;" k="-44" />
    <hkern u1="K" u2="&#xec;" k="-21" />
    <hkern u1="K" u2="&#xef;" k="-54" />
    <hkern u1="K" u2="&#xee;" k="-10" />
    <hkern u1="K" u2="&#x12d;" k="-30" />
    <hkern u1="K" u2="&#x2d;" k="23" />
    <hkern u1="K" u2="&#x127;" k="-11" />
    <hkern u1="K" u2="&#x203a;" k="-4" />
    <hkern u1="K" u2="&#x2039;" k="12" />
    <hkern u1="K" u2="&#x34;" k="-23" />
    <hkern u1="K" u2="f" k="19" />
    <hkern u1="K" u2="&#xf0;" k="28" />
    <hkern u1="K" u2="d" k="31" />
    <hkern u1="K" u2="&#xa9;" k="15" />
    <hkern u1="K" u2="&#x2c;" k="-13" />
    <hkern u1="K" u2="]" k="-13" />
    <hkern u1="K" u2="&#x7d;" k="-12" />
    <hkern u1="K" u2="\" k="-10" />
    <hkern u1="K" u2="&#x2a;" k="-17" />
    <hkern u1="K" u2="&#x2bc;" k="13" />
    <hkern u1="K" u2="a" k="5" />
    <hkern u1="K" u2="S" k="10" />
    <hkern u1="K" u2="&#x1fe;" k="14" />
    <hkern u1="K" u2="&#x2f;" k="-22" />
    <hkern u1="K" u2="&#xd8;" k="14" />
    <hkern u1="K" u2="O" k="37" />
    <hkern u1="K" u2="J" k="-4" />
    <hkern u1="K" u2="&#x20;" k="13" />
    <hkern u1="L" g2="y.alt" k="63" />
    <hkern u1="L" u2="y" k="61" />
    <hkern u1="L" g2="w.alt" k="52" />
    <hkern u1="L" u2="w" k="69" />
    <hkern u1="L" u2="v" k="57" />
    <hkern u1="L" u2="u" k="5" />
    <hkern u1="L" u2="&#x2122;" k="101" />
    <hkern u1="L" g2="t.alt" k="19" />
    <hkern u1="L" u2="t" k="22" />
    <hkern u1="L" u2="&#xc6;" k="-10" />
    <hkern u1="L" u2="&#x37;" k="4" />
    <hkern u1="L" u2="&#x259;" k="7" />
    <hkern u1="L" u2="&#xae;" k="10" />
    <hkern u1="L" u2="&#x27;" k="98" />
    <hkern u1="L" u2="&#x2019;" k="99" />
    <hkern u1="L" u2="&#x2018;" k="100" />
    <hkern u1="L" u2="&#x3f;" k="8" />
    <hkern u1="L" u2="&#xba;" k="99" />
    <hkern u1="L" u2="&#xaa;" k="98" />
    <hkern u1="L" u2="o" k="10" />
    <hkern u1="L" u2="&#x2d;" k="30" />
    <hkern u1="L" u2="&#x2039;" k="21" />
    <hkern u1="L" u2="f" k="17" />
    <hkern u1="L" u2="&#xf0;" k="8" />
    <hkern u1="L" u2="d" k="11" />
    <hkern u1="L" u2="&#xa9;" k="10" />
    <hkern u1="L" u2="\" k="81" />
    <hkern u1="L" u2="&#x2a;" k="101" />
    <hkern u1="L" u2="&#x2bc;" k="118" />
    <hkern u1="L" u2="Y" k="104" />
    <hkern u1="L" u2="W" k="95" />
    <hkern u1="L" u2="V" k="90" />
    <hkern u1="L" u2="U" k="29" />
    <hkern u1="L" u2="&#x166;" k="46" />
    <hkern u1="L" u2="T" k="100" />
    <hkern u1="L" u2="S" k="2" />
    <hkern u1="L" u2="O" k="28" />
    <hkern u1="L" u2="&#xb7;" k="126" />
    <hkern u1="L" u2="&#x20;" k="27" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="32" />
    <hkern u1="&#x13d;" u2="&#x27;" k="45" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="35" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="35" />
    <hkern u1="&#x13d;" u2="&#xaa;" k="55" />
    <hkern u1="&#x13d;" u2="\" k="34" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="37" />
    <hkern u1="&#x13d;" u2="Y" k="9" />
    <hkern u1="&#x13d;" u2="W" k="10" />
    <hkern u1="&#x13d;" u2="V" k="11" />
    <hkern u1="&#x13d;" u2="&#x166;" k="42" />
    <hkern u1="&#x13d;" u2="T" k="23" />
    <hkern u1="&#xb7;" g2="two.lnum" k="5" />
    <hkern u1="&#xb7;" u2="&#x32;" k="5" />
    <hkern u1="&#xb7;" g2="three.lnum" k="9" />
    <hkern u1="&#xb7;" u2="&#x33;" k="20" />
    <hkern u1="&#xb7;" g2="seven.lnum" k="25" />
    <hkern u1="&#xb7;" u2="&#x37;" k="13" />
    <hkern u1="&#xb7;" g2="one.lnum" k="10" />
    <hkern u1="&#xb7;" u2="&#x31;" k="9" />
    <hkern u1="&#xb7;" u2="l" k="35" />
    <hkern u1="&#xb7;" u2="&#x34;" k="9" />
    <hkern u1="&#x141;" u2="&#x166;" k="43" />
    <hkern u1="A" u2="&#x30;" k="15" />
    <hkern u1="A" g2="y.alt" k="35" />
    <hkern u1="A" u2="y" k="33" />
    <hkern u1="A" g2="w.alt" k="29" />
    <hkern u1="A" u2="w" k="35" />
    <hkern u1="A" u2="v" k="31" />
    <hkern u1="A" u2="u" k="11" />
    <hkern u1="A" u2="&#x32;" k="-6" />
    <hkern u1="A" u2="&#x2122;" k="58" />
    <hkern u1="A" g2="t.alt" k="20" />
    <hkern u1="A" u2="t" k="27" />
    <hkern u1="A" u2="&#x36;" k="15" />
    <hkern u1="A" u2="&#x37;" k="11" />
    <hkern u1="A" u2="&#x259;" k="14" />
    <hkern u1="A" u2="s" k="3" />
    <hkern u1="A" u2="&#xae;" k="19" />
    <hkern u1="A" u2="&#x27;" k="59" />
    <hkern u1="A" u2="&#x2019;" k="59" />
    <hkern u1="A" u2="&#x2018;" k="60" />
    <hkern u1="A" u2="&#x201a;" k="-4" />
    <hkern u1="A" u2="&#x201e;" k="-4" />
    <hkern u1="A" u2="&#x3f;" k="12" />
    <hkern u1="A" u2="&#x29;" k="-14" />
    <hkern u1="A" u2="&#xba;" k="45" />
    <hkern u1="A" u2="&#xaa;" k="41" />
    <hkern u1="A" u2="o" k="17" />
    <hkern u1="A" u2="&#x142;" k="2" />
    <hkern u1="A" u2="l" k="5" />
    <hkern u1="A" u2="&#x2d;" k="12" />
    <hkern u1="A" u2="&#x2039;" k="12" />
    <hkern u1="A" u2="&#x34;" k="-13" />
    <hkern u1="A" u2="f" k="17" />
    <hkern u1="A" u2="&#xf0;" k="15" />
    <hkern u1="A" u2="&#x38;" k="13" />
    <hkern u1="A" u2="d" k="17" />
    <hkern u1="A" u2="&#xa9;" k="19" />
    <hkern u1="A" u2="&#x2c;" k="-4" />
    <hkern u1="A" u2="]" k="-3" />
    <hkern u1="A" u2="\" k="58" />
    <hkern u1="A" u2="&#x40;" k="4" />
    <hkern u1="A" u2="&#x2a;" k="57" />
    <hkern u1="A" u2="&#x2bc;" k="102" />
    <hkern u1="A" u2="&#x26;" k="13" />
    <hkern u1="A" u2="a" k="8" />
    <hkern u1="A" u2="Y" k="69" />
    <hkern u1="A" u2="W" k="56" />
    <hkern u1="A" u2="V" k="55" />
    <hkern u1="A" u2="U" k="28" />
    <hkern u1="A" u2="&#x166;" k="54" />
    <hkern u1="A" u2="T" k="66" />
    <hkern u1="A" u2="S" k="8" />
    <hkern u1="A" u2="&#x1fe;" k="9" />
    <hkern u1="A" u2="&#x2f;" k="-13" />
    <hkern u1="A" u2="&#xd8;" k="9" />
    <hkern u1="A" u2="O" k="26" />
    <hkern u1="A" u2="&#x141;" k="11" />
    <hkern u1="A" u2="&#xd0;" k="2" />
    <hkern u1="A" u2="&#x110;" k="2" />
    <hkern u1="A" u2="&#x20;" k="31" />
    <hkern u1="O" u2="z" k="5" />
    <hkern u1="O" g2="y.alt" k="3" />
    <hkern u1="O" u2="y" k="2" />
    <hkern u1="O" u2="x" k="12" />
    <hkern u1="O" u2="w" k="3" />
    <hkern u1="O" u2="u" k="3" />
    <hkern u1="O" u2="&#x2122;" k="3" />
    <hkern u1="O" u2="&#xc6;" k="41" />
    <hkern u1="O" u2="s" k="5" />
    <hkern u1="O" u2="&#x27;" k="6" />
    <hkern u1="O" u2="&#x2019;" k="3" />
    <hkern u1="O" u2="&#x2018;" k="3" />
    <hkern u1="O" u2="&#x2e;" k="9" />
    <hkern u1="O" u2="&#x29;" k="14" />
    <hkern u1="O" u2="n" k="5" />
    <hkern u1="O" u2="&#x142;" k="-12" />
    <hkern u1="O" g2="l.alt" k="3" />
    <hkern u1="O" u2="&#x129;" k="-7" />
    <hkern u1="O" u2="i" k="5" />
    <hkern u1="O" u2="h" k="3" />
    <hkern u1="O" u2="&#x34;" k="4" />
    <hkern u1="O" u2="&#xf0;" k="5" />
    <hkern u1="O" u2="\" k="17" />
    <hkern u1="O" u2="&#x2a;" k="3" />
    <hkern u1="O" u2="&#x2bc;" k="40" />
    <hkern u1="O" u2="a" k="8" />
    <hkern u1="O" u2="Z" k="15" />
    <hkern u1="O" u2="Y" k="40" />
    <hkern u1="O" u2="X" k="28" />
    <hkern u1="O" u2="W" k="27" />
    <hkern u1="O" u2="V" k="25" />
    <hkern u1="O" u2="&#x166;" k="4" />
    <hkern u1="O" u2="T" k="24" />
    <hkern u1="O" u2="S" k="4" />
    <hkern u1="O" u2="&#x2f;" k="21" />
    <hkern u1="O" u2="A" k="26" />
    <hkern u1="O" u2="&#x141;" k="6" />
    <hkern u1="O" u2="J" k="29" />
    <hkern u1="O" u2="&#x126;" k="4" />
    <hkern u1="O" u2="&#xd0;" k="7" />
    <hkern u1="O" u2="&#x110;" k="7" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="-10" />
    <hkern u1="&#xd8;" u2="&#x29;" k="-10" />
    <hkern u1="&#xd8;" u2="\" k="-7" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="-15" />
    <hkern u1="&#xd8;" u2="Y" k="16" />
    <hkern u1="&#xd8;" u2="X" k="12" />
    <hkern u1="&#xd8;" u2="W" k="10" />
    <hkern u1="&#xd8;" u2="V" k="9" />
    <hkern u1="&#x2f;" g2="zero.lnum" k="20" />
    <hkern u1="&#x2f;" u2="&#x30;" k="39" />
    <hkern u1="&#x2f;" u2="z" k="16" />
    <hkern u1="&#x2f;" g2="y.alt" k="16" />
    <hkern u1="&#x2f;" u2="y" k="15" />
    <hkern u1="&#x2f;" u2="x" k="10" />
    <hkern u1="&#x2f;" g2="w.alt" k="16" />
    <hkern u1="&#x2f;" u2="w" k="16" />
    <hkern u1="&#x2f;" u2="v" k="16" />
    <hkern u1="&#x2f;" u2="u" k="22" />
    <hkern u1="&#x2f;" u2="&#x32;" k="23" />
    <hkern u1="&#x2f;" u2="&#x33;" k="25" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="95" />
    <hkern u1="&#x2f;" g2="six.lnum" k="16" />
    <hkern u1="&#x2f;" u2="&#x36;" k="16" />
    <hkern u1="&#x2f;" u2="s" k="35" />
    <hkern u1="&#x2f;" u2="&#x31;" k="22" />
    <hkern u1="&#x2f;" u2="o" k="38" />
    <hkern u1="&#x2f;" u2="&#x39;" k="31" />
    <hkern u1="&#x2f;" u2="n" k="23" />
    <hkern u1="&#x2f;" u2="&#x129;" k="-23" />
    <hkern u1="&#x2f;" u2="&#x12b;" k="-28" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-5" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-38" />
    <hkern u1="&#x2f;" u2="&#x12d;" k="-15" />
    <hkern u1="&#x2f;" g2="four.lnum" k="56" />
    <hkern u1="&#x2f;" u2="&#x34;" k="76" />
    <hkern u1="&#x2f;" g2="five.lnum" k="8" />
    <hkern u1="&#x2f;" u2="&#x35;" k="36" />
    <hkern u1="&#x2f;" u2="f" k="12" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="36" />
    <hkern u1="&#x2f;" g2="eight.lnum" k="8" />
    <hkern u1="&#x2f;" u2="&#x38;" k="8" />
    <hkern u1="&#x2f;" u2="d" k="38" />
    <hkern u1="&#x2f;" u2="a" k="30" />
    <hkern u1="&#x2f;" u2="Y" k="-17" />
    <hkern u1="&#x2f;" u2="X" k="-16" />
    <hkern u1="&#x2f;" u2="W" k="-15" />
    <hkern u1="&#x2f;" u2="V" k="-14" />
    <hkern u1="&#x2f;" u2="S" k="9" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="264" />
    <hkern u1="&#x2f;" u2="O" k="21" />
    <hkern u1="&#x2f;" u2="A" k="61" />
    <hkern u1="&#x2f;" u2="J" k="74" />
    <hkern u1="&#x1fe;" u2="&#x2122;" k="-10" />
    <hkern u1="&#x1fe;" u2="&#x29;" k="-10" />
    <hkern u1="&#x1fe;" u2="\" k="-7" />
    <hkern u1="&#x1fe;" u2="&#x2a;" k="-15" />
    <hkern u1="&#x1fe;" u2="Y" k="16" />
    <hkern u1="&#x1fe;" u2="X" k="12" />
    <hkern u1="&#x1fe;" u2="W" k="10" />
    <hkern u1="&#x1fe;" u2="V" k="9" />
    <hkern u1="P" g2="y.alt" k="-7" />
    <hkern u1="P" u2="y" k="-12" />
    <hkern u1="P" u2="x" k="-5" />
    <hkern u1="P" g2="w.alt" k="-7" />
    <hkern u1="P" u2="w" k="-9" />
    <hkern u1="P" u2="v" k="-7" />
    <hkern u1="P" g2="t.alt" k="-3" />
    <hkern u1="P" u2="&#xc6;" k="87" />
    <hkern u1="P" u2="&#x259;" k="10" />
    <hkern u1="P" u2="s" k="9" />
    <hkern u1="P" u2="&#x2e;" k="65" />
    <hkern u1="P" u2="&#x29;" k="11" />
    <hkern u1="P" u2="o" k="13" />
    <hkern u1="P" u2="&#x142;" k="-9" />
    <hkern u1="P" u2="&#x135;" k="-11" />
    <hkern u1="P" u2="&#x129;" k="-42" />
    <hkern u1="P" u2="&#xef;" k="-22" />
    <hkern u1="P" u2="&#xee;" k="-11" />
    <hkern u1="P" u2="&#x2d;" k="12" />
    <hkern u1="P" u2="&#x2039;" k="12" />
    <hkern u1="P" u2="&#x34;" k="34" />
    <hkern u1="P" u2="&#x35;" k="9" />
    <hkern u1="P" u2="&#xf0;" k="24" />
    <hkern u1="P" u2="d" k="12" />
    <hkern u1="P" u2="&#x2bc;" k="26" />
    <hkern u1="P" u2="a" k="10" />
    <hkern u1="P" u2="Z" k="7" />
    <hkern u1="P" u2="Y" k="16" />
    <hkern u1="P" u2="X" k="20" />
    <hkern u1="P" u2="W" k="12" />
    <hkern u1="P" u2="V" k="12" />
    <hkern u1="P" u2="T" k="2" />
    <hkern u1="P" u2="&#x2f;" k="58" />
    <hkern u1="P" u2="A" k="51" />
    <hkern u1="P" u2="J" k="87" />
    <hkern u1="P" u2="&#x20;" k="27" />
    <hkern u1="Q" u2="y" k="5" />
    <hkern u1="Q" u2="&#x32;" k="-9" />
    <hkern u1="Q" u2="&#xc6;" k="13" />
    <hkern u1="Q" u2="&#x201a;" k="-7" />
    <hkern u1="Q" u2="&#x201e;" k="-7" />
    <hkern u1="Q" u2="&#x29;" k="-17" />
    <hkern u1="Q" u2="&#x142;" k="-10" />
    <hkern u1="Q" u2="&#x129;" k="-5" />
    <hkern u1="Q" u2="&#x34;" k="-17" />
    <hkern u1="Q" u2="&#x2c;" k="-7" />
    <hkern u1="Q" u2="]" k="-7" />
    <hkern u1="Q" u2="&#x7d;" k="-6" />
    <hkern u1="Q" u2="X" k="11" />
    <hkern u1="Q" u2="&#x2f;" k="-15" />
    <hkern u1="Q" u2="A" k="7" />
    <hkern u1="Q" u2="J" k="10" />
    <hkern u1="R" g2="y.alt" k="10" />
    <hkern u1="R" u2="y" k="9" />
    <hkern u1="R" g2="w.alt" k="8" />
    <hkern u1="R" u2="w" k="10" />
    <hkern u1="R" u2="v" k="8" />
    <hkern u1="R" u2="u" k="11" />
    <hkern u1="R" u2="&#x2122;" k="7" />
    <hkern u1="R" g2="t.alt" k="7" />
    <hkern u1="R" u2="t" k="10" />
    <hkern u1="R" u2="&#x259;" k="21" />
    <hkern u1="R" u2="s" k="2" />
    <hkern u1="R" u2="&#x2019;" k="5" />
    <hkern u1="R" u2="&#x29;" k="-7" />
    <hkern u1="R" u2="o" k="22" />
    <hkern u1="R" u2="n" k="7" />
    <hkern u1="R" g2="l.alt" k="5" />
    <hkern u1="R" u2="l" k="9" />
    <hkern u1="R" u2="&#x129;" k="-12" />
    <hkern u1="R" u2="i" k="7" />
    <hkern u1="R" u2="&#x2d;" k="3" />
    <hkern u1="R" u2="h" k="5" />
    <hkern u1="R" u2="&#x2039;" k="12" />
    <hkern u1="R" u2="&#x34;" k="-7" />
    <hkern u1="R" u2="f" k="9" />
    <hkern u1="R" u2="&#xf0;" k="27" />
    <hkern u1="R" u2="d" k="21" />
    <hkern u1="R" u2="\" k="14" />
    <hkern u1="R" u2="&#x2bc;" k="31" />
    <hkern u1="R" u2="a" k="12" />
    <hkern u1="R" u2="Y" k="35" />
    <hkern u1="R" u2="W" k="28" />
    <hkern u1="R" u2="V" k="27" />
    <hkern u1="R" u2="U" k="7" />
    <hkern u1="R" u2="&#x166;" k="1" />
    <hkern u1="R" u2="T" k="17" />
    <hkern u1="R" u2="&#x2f;" k="-6" />
    <hkern u1="R" u2="O" k="8" />
    <hkern u1="R" u2="A" k="4" />
    <hkern u1="R" u2="&#x141;" k="5" />
    <hkern u1="R" u2="&#x126;" k="4" />
    <hkern u1="R" u2="&#xd0;" k="5" />
    <hkern u1="R" u2="&#x110;" k="5" />
    <hkern u1="S" u2="z" k="6" />
    <hkern u1="S" g2="y.alt" k="21" />
    <hkern u1="S" u2="y" k="20" />
    <hkern u1="S" u2="x" k="11" />
    <hkern u1="S" g2="w.alt" k="18" />
    <hkern u1="S" u2="w" k="22" />
    <hkern u1="S" u2="v" k="20" />
    <hkern u1="S" u2="u" k="5" />
    <hkern u1="S" g2="t.alt" k="10" />
    <hkern u1="S" u2="t" k="14" />
    <hkern u1="S" u2="&#xc6;" k="7" />
    <hkern u1="S" u2="&#x37;" k="4" />
    <hkern u1="S" u2="s" k="7" />
    <hkern u1="S" u2="&#xaa;" k="4" />
    <hkern u1="S" u2="n" k="5" />
    <hkern u1="S" g2="l.alt" k="3" />
    <hkern u1="S" u2="l" k="3" />
    <hkern u1="S" u2="&#x135;" k="-16" />
    <hkern u1="S" u2="&#x129;" k="-31" />
    <hkern u1="S" u2="&#xef;" k="-23" />
    <hkern u1="S" u2="&#xee;" k="-16" />
    <hkern u1="S" u2="&#x12d;" k="-1" />
    <hkern u1="S" u2="i" k="3" />
    <hkern u1="S" u2="h" k="3" />
    <hkern u1="S" u2="f" k="13" />
    <hkern u1="S" u2="&#xf0;" k="3" />
    <hkern u1="S" u2="&#x2bc;" k="25" />
    <hkern u1="S" u2="a" k="3" />
    <hkern u1="S" u2="Y" k="19" />
    <hkern u1="S" u2="X" k="9" />
    <hkern u1="S" u2="W" k="15" />
    <hkern u1="S" u2="V" k="15" />
    <hkern u1="S" u2="&#x166;" k="6" />
    <hkern u1="S" u2="T" k="4" />
    <hkern u1="S" u2="A" k="14" />
    <hkern u1="T" u2="&#x30;" k="35" />
    <hkern u1="T" u2="z" k="60" />
    <hkern u1="T" g2="y.alt" k="48" />
    <hkern u1="T" u2="y" k="45" />
    <hkern u1="T" u2="x" k="44" />
    <hkern u1="T" g2="w.alt" k="48" />
    <hkern u1="T" u2="w" k="47" />
    <hkern u1="T" u2="v" k="47" />
    <hkern u1="T" u2="u" k="67" />
    <hkern u1="T" u2="&#x32;" k="21" />
    <hkern u1="T" u2="&#x2122;" k="-4" />
    <hkern u1="T" u2="&#x33;" k="23" />
    <hkern u1="T" g2="t.alt" k="-1" />
    <hkern u1="T" u2="t" k="4" />
    <hkern u1="T" u2="&#xc6;" k="85" />
    <hkern u1="T" u2="&#x36;" k="7" />
    <hkern u1="T" u2="&#x37;" k="16" />
    <hkern u1="T" u2="&#x15d;" k="40" />
    <hkern u1="T" u2="&#x259;" k="91" />
    <hkern u1="T" u2="&#x161;" k="39" />
    <hkern u1="T" u2="s" k="85" />
    <hkern u1="T" u2="&#xae;" k="11" />
    <hkern u1="T" u2="&#x159;" k="27" />
    <hkern u1="T" u2="&#x2e;" k="63" />
    <hkern u1="T" u2="&#x29;" k="-4" />
    <hkern u1="T" u2="&#x31;" k="31" />
    <hkern u1="T" u2="o" k="89" />
    <hkern u1="T" u2="&#x39;" k="38" />
    <hkern u1="T" u2="n" k="51" />
    <hkern u1="T" u2="&#x135;" k="-37" />
    <hkern u1="T" u2="&#x129;" k="-74" />
    <hkern u1="T" u2="&#x12b;" k="-38" />
    <hkern u1="T" u2="&#xec;" k="-13" />
    <hkern u1="T" u2="&#xef;" k="-46" />
    <hkern u1="T" u2="&#xee;" k="-37" />
    <hkern u1="T" u2="&#x12d;" k="-23" />
    <hkern u1="T" u2="&#xed;" k="13" />
    <hkern u1="T" u2="&#x2d;" k="61" />
    <hkern u1="T" u2="&#x203a;" k="24" />
    <hkern u1="T" u2="&#x2039;" k="52" />
    <hkern u1="T" u2="&#xdf;" k="9" />
    <hkern u1="T" u2="&#x34;" k="59" />
    <hkern u1="T" u2="&#x35;" k="36" />
    <hkern u1="T" g2="f_i" k="12" />
    <hkern u1="T" g2="f_f_l" k="42" />
    <hkern u1="T" g2="f_f_i" k="16" />
    <hkern u1="T" g2="f_f" k="16" />
    <hkern u1="T" u2="f" k="31" />
    <hkern u1="T" u2="&#xf0;" k="81" />
    <hkern u1="T" u2="&#x38;" k="3" />
    <hkern u1="T" u2="&#x131;" k="51" />
    <hkern u1="T" u2="d" k="87" />
    <hkern u1="T" u2="&#xa9;" k="11" />
    <hkern u1="T" u2="&#x3a;" k="21" />
    <hkern u1="T" u2="&#xe3;" k="63" />
    <hkern u1="T" u2="&#x40;" k="43" />
    <hkern u1="T" u2="&#x2a;" k="-19" />
    <hkern u1="T" u2="&#x26;" k="4" />
    <hkern u1="T" u2="a" k="80" />
    <hkern u1="T" u2="S" k="7" />
    <hkern u1="T" u2="&#x2f;" k="54" />
    <hkern u1="T" u2="O" k="23" />
    <hkern u1="T" u2="A" k="66" />
    <hkern u1="T" u2="J" k="94" />
    <hkern u1="T" u2="&#x20;" k="28" />
    <hkern g1="T.sc" u2="o" k="13" />
    <hkern u1="&#x166;" u2="z" k="27" />
    <hkern u1="&#x166;" g2="y.alt" k="11" />
    <hkern u1="&#x166;" u2="y" k="10" />
    <hkern u1="&#x166;" u2="x" k="8" />
    <hkern u1="&#x166;" g2="w.alt" k="9" />
    <hkern u1="&#x166;" u2="w" k="11" />
    <hkern u1="&#x166;" u2="v" k="10" />
    <hkern u1="&#x166;" u2="u" k="14" />
    <hkern u1="&#x166;" u2="&#xc6;" k="45" />
    <hkern u1="&#x166;" u2="&#x3b;" k="4" />
    <hkern u1="&#x166;" u2="&#xfb06;" k="3" />
    <hkern u1="&#x166;" u2="s" k="28" />
    <hkern u1="&#x166;" u2="&#xae;" k="1" />
    <hkern u1="&#x166;" g2="q.alt" k="10" />
    <hkern u1="&#x166;" u2="q" k="10" />
    <hkern u1="&#x166;" u2="o" k="28" />
    <hkern u1="&#x166;" u2="n" k="7" />
    <hkern u1="&#x166;" u2="&#x2d;" k="5" />
    <hkern u1="&#x166;" u2="&#x203a;" k="4" />
    <hkern u1="&#x166;" u2="&#x2039;" k="16" />
    <hkern u1="&#x166;" u2="&#xdf;" k="9" />
    <hkern u1="&#x166;" u2="f" k="16" />
    <hkern u1="&#x166;" u2="&#xf0;" k="35" />
    <hkern u1="&#x166;" u2="e" k="9" />
    <hkern u1="&#x166;" u2="d" k="28" />
    <hkern u1="&#x166;" u2="&#xa9;" k="1" />
    <hkern u1="&#x166;" u2="&#x3a;" k="3" />
    <hkern u1="&#x166;" u2="c" k="9" />
    <hkern u1="&#x166;" u2="&#x2a;" k="-8" />
    <hkern u1="&#x166;" u2="a" k="41" />
    <hkern u1="&#x166;" u2="&#x1fe;" k="1" />
    <hkern u1="&#x166;" u2="&#x2f;" k="19" />
    <hkern u1="&#x166;" u2="&#xd8;" k="1" />
    <hkern u1="&#x166;" u2="O" k="4" />
    <hkern u1="&#x166;" u2="A" k="54" />
    <hkern u1="&#x166;" u2="J" k="47" />
    <hkern u1="&#x166;" u2="&#x20;" k="6" />
    <hkern u1="&#xde;" g2="y.alt" k="2" />
    <hkern u1="&#xde;" u2="y" k="6" />
    <hkern u1="&#xde;" u2="x" k="8" />
    <hkern u1="&#xde;" u2="w" k="2" />
    <hkern u1="&#xde;" u2="&#x2122;" k="19" />
    <hkern u1="&#xde;" u2="&#xc6;" k="48" />
    <hkern u1="&#xde;" u2="&#x27;" k="30" />
    <hkern u1="&#xde;" u2="&#x2019;" k="22" />
    <hkern u1="&#xde;" u2="&#x2018;" k="25" />
    <hkern u1="&#xde;" u2="&#x3f;" k="4" />
    <hkern u1="&#xde;" u2="&#x2e;" k="21" />
    <hkern u1="&#xde;" u2="&#x29;" k="20" />
    <hkern u1="&#xde;" u2="&#x2d;" k="3" />
    <hkern u1="&#xde;" u2="]" k="10" />
    <hkern u1="&#xde;" u2="&#x7d;" k="9" />
    <hkern u1="&#xde;" u2="\" k="28" />
    <hkern u1="&#xde;" u2="&#x2a;" k="16" />
    <hkern u1="&#xde;" u2="a" k="4" />
    <hkern u1="&#xde;" u2="Z" k="18" />
    <hkern u1="&#xde;" u2="Y" k="54" />
    <hkern u1="&#xde;" u2="X" k="43" />
    <hkern u1="&#xde;" u2="W" k="32" />
    <hkern u1="&#xde;" u2="V" k="31" />
    <hkern u1="&#xde;" u2="T" k="36" />
    <hkern u1="&#xde;" u2="S" k="4" />
    <hkern u1="&#xde;" u2="&#x2f;" k="28" />
    <hkern u1="&#xde;" u2="A" k="27" />
    <hkern u1="&#xde;" u2="J" k="32" />
    <hkern u1="&#xde;" u2="&#xd0;" k="4" />
    <hkern u1="&#xde;" u2="&#x110;" k="4" />
    <hkern u1="U" u2="z" k="9" />
    <hkern u1="U" u2="x" k="4" />
    <hkern u1="U" u2="u" k="8" />
    <hkern u1="U" g2="t.alt" k="2" />
    <hkern u1="U" u2="t" k="5" />
    <hkern u1="U" u2="&#xc6;" k="44" />
    <hkern u1="U" u2="&#x259;" k="9" />
    <hkern u1="U" u2="s" k="14" />
    <hkern u1="U" u2="&#x2e;" k="14" />
    <hkern u1="U" u2="o" k="11" />
    <hkern u1="U" u2="n" k="9" />
    <hkern u1="U" g2="l.alt" k="6" />
    <hkern u1="U" u2="l" k="6" />
    <hkern u1="U" u2="&#x135;" k="-11" />
    <hkern u1="U" u2="&#x129;" k="-35" />
    <hkern u1="U" u2="&#xef;" k="-20" />
    <hkern u1="U" u2="&#xee;" k="-11" />
    <hkern u1="U" u2="i" k="6" />
    <hkern u1="U" u2="h" k="6" />
    <hkern u1="U" u2="&#x34;" k="4" />
    <hkern u1="U" u2="f" k="7" />
    <hkern u1="U" u2="&#xf0;" k="14" />
    <hkern u1="U" u2="d" k="11" />
    <hkern u1="U" u2="&#x2bc;" k="18" />
    <hkern u1="U" u2="a" k="13" />
    <hkern u1="U" u2="&#x2f;" k="21" />
    <hkern u1="U" u2="A" k="27" />
    <hkern u1="U" u2="J" k="28" />
    <hkern u1="V" u2="&#x30;" k="29" />
    <hkern u1="V" u2="z" k="27" />
    <hkern u1="V" g2="y.alt" k="13" />
    <hkern u1="V" u2="y" k="12" />
    <hkern u1="V" u2="x" k="13" />
    <hkern u1="V" g2="w.alt" k="14" />
    <hkern u1="V" u2="w" k="12" />
    <hkern u1="V" u2="v" k="14" />
    <hkern u1="V" u2="u" k="38" />
    <hkern u1="V" u2="&#x32;" k="18" />
    <hkern u1="V" u2="&#x2122;" k="-15" />
    <hkern u1="V" u2="&#x33;" k="18" />
    <hkern u1="V" g2="t.alt" k="2" />
    <hkern u1="V" u2="t" k="12" />
    <hkern u1="V" u2="&#xc6;" k="85" />
    <hkern u1="V" u2="&#x36;" k="17" />
    <hkern u1="V" u2="&#x259;" k="56" />
    <hkern u1="V" u2="s" k="52" />
    <hkern u1="V" u2="&#xae;" k="19" />
    <hkern u1="V" u2="&#x159;" k="9" />
    <hkern u1="V" u2="&#x2019;" k="-5" />
    <hkern u1="V" u2="&#x2018;" k="-4" />
    <hkern u1="V" u2="&#x2e;" k="63" />
    <hkern u1="V" u2="&#x29;" k="-15" />
    <hkern u1="V" u2="&#x31;" k="18" />
    <hkern u1="V" u2="o" k="60" />
    <hkern u1="V" u2="&#x39;" k="26" />
    <hkern u1="V" u2="n" k="41" />
    <hkern u1="V" u2="&#x142;" k="6" />
    <hkern u1="V" u2="&#x135;" k="-30" />
    <hkern u1="V" u2="&#x129;" k="-58" />
    <hkern u1="V" u2="&#x12b;" k="-49" />
    <hkern u1="V" u2="&#xec;" k="-25" />
    <hkern u1="V" u2="&#xef;" k="-57" />
    <hkern u1="V" u2="&#xee;" k="-30" />
    <hkern u1="V" u2="&#x12d;" k="-34" />
    <hkern u1="V" u2="&#xed;" k="19" />
    <hkern u1="V" u2="&#x2d;" k="37" />
    <hkern u1="V" u2="&#x127;" k="-7" />
    <hkern u1="V" u2="&#x203a;" k="22" />
    <hkern u1="V" u2="&#x2039;" k="39" />
    <hkern u1="V" u2="&#xdf;" k="22" />
    <hkern u1="V" u2="&#x34;" k="56" />
    <hkern u1="V" u2="&#x35;" k="32" />
    <hkern u1="V" u2="f" k="27" />
    <hkern u1="V" u2="&#xf0;" k="65" />
    <hkern u1="V" u2="&#x38;" k="15" />
    <hkern u1="V" u2="&#x131;" k="41" />
    <hkern u1="V" u2="d" k="58" />
    <hkern u1="V" u2="&#xa9;" k="19" />
    <hkern u1="V" u2="&#x3a;" k="17" />
    <hkern u1="V" u2="]" k="-3" />
    <hkern u1="V" u2="\" k="-13" />
    <hkern u1="V" u2="&#xe3;" k="32" />
    <hkern u1="V" u2="&#x40;" k="35" />
    <hkern u1="V" u2="&#x2a;" k="-24" />
    <hkern u1="V" u2="&#x2bc;" k="14" />
    <hkern u1="V" u2="&#x26;" k="17" />
    <hkern u1="V" u2="a" k="53" />
    <hkern u1="V" u2="S" k="19" />
    <hkern u1="V" u2="&#x2f;" k="60" />
    <hkern u1="V" u2="O" k="26" />
    <hkern u1="V" u2="A" k="55" />
    <hkern u1="V" u2="&#x141;" k="6" />
    <hkern u1="V" u2="J" k="82" />
    <hkern u1="V" u2="&#xd0;" k="2" />
    <hkern u1="V" u2="&#x110;" k="2" />
    <hkern u1="V" u2="&#x20;" k="35" />
    <hkern u1="W" u2="&#x30;" k="29" />
    <hkern u1="W" u2="&#x17e;" k="10" />
    <hkern u1="W" u2="z" k="28" />
    <hkern u1="W" g2="y.alt" k="14" />
    <hkern u1="W" u2="y" k="12" />
    <hkern u1="W" u2="x" k="14" />
    <hkern u1="W" g2="w.alt" k="14" />
    <hkern u1="W" u2="w" k="13" />
    <hkern u1="W" u2="v" k="14" />
    <hkern u1="W" u2="u" k="40" />
    <hkern u1="W" u2="&#x32;" k="18" />
    <hkern u1="W" u2="&#x2122;" k="-17" />
    <hkern u1="W" u2="&#x33;" k="18" />
    <hkern u1="W" g2="t.alt" k="2" />
    <hkern u1="W" u2="t" k="12" />
    <hkern u1="W" u2="&#xc6;" k="87" />
    <hkern u1="W" u2="&#x36;" k="17" />
    <hkern u1="W" u2="&#x259;" k="59" />
    <hkern u1="W" u2="&#x161;" k="22" />
    <hkern u1="W" u2="s" k="55" />
    <hkern u1="W" u2="&#xae;" k="19" />
    <hkern u1="W" u2="&#x159;" k="29" />
    <hkern u1="W" u2="&#x2019;" k="-6" />
    <hkern u1="W" u2="&#x2018;" k="-6" />
    <hkern u1="W" u2="&#x2e;" k="66" />
    <hkern u1="W" u2="&#x29;" k="-17" />
    <hkern u1="W" u2="&#x31;" k="19" />
    <hkern u1="W" u2="o" k="63" />
    <hkern u1="W" u2="&#x39;" k="28" />
    <hkern u1="W" u2="n" k="42" />
    <hkern u1="W" u2="&#x142;" k="6" />
    <hkern u1="W" u2="&#x135;" k="-30" />
    <hkern u1="W" u2="&#x129;" k="-59" />
    <hkern u1="W" u2="&#x12b;" k="-51" />
    <hkern u1="W" u2="&#xec;" k="-27" />
    <hkern u1="W" u2="&#xef;" k="-60" />
    <hkern u1="W" u2="&#xee;" k="-30" />
    <hkern u1="W" u2="&#x12d;" k="-36" />
    <hkern u1="W" u2="&#xed;" k="19" />
    <hkern u1="W" u2="&#x2d;" k="39" />
    <hkern u1="W" u2="&#x127;" k="-15" />
    <hkern u1="W" u2="&#x203a;" k="23" />
    <hkern u1="W" u2="&#x2039;" k="40" />
    <hkern u1="W" u2="&#xdf;" k="22" />
    <hkern u1="W" u2="&#x34;" k="57" />
    <hkern u1="W" u2="&#x35;" k="33" />
    <hkern u1="W" u2="f" k="29" />
    <hkern u1="W" u2="&#xf0;" k="68" />
    <hkern u1="W" u2="&#x38;" k="15" />
    <hkern u1="W" u2="&#x131;" k="42" />
    <hkern u1="W" u2="d" k="62" />
    <hkern u1="W" u2="&#xa9;" k="19" />
    <hkern u1="W" u2="&#x3a;" k="18" />
    <hkern u1="W" u2="]" k="-5" />
    <hkern u1="W" u2="&#x7d;" k="-4" />
    <hkern u1="W" u2="\" k="-15" />
    <hkern u1="W" u2="&#xe3;" k="32" />
    <hkern u1="W" u2="&#x40;" k="36" />
    <hkern u1="W" u2="&#x2a;" k="-22" />
    <hkern u1="W" u2="&#x2bc;" k="13" />
    <hkern u1="W" u2="&#x26;" k="17" />
    <hkern u1="W" u2="&#xe4;" k="32" />
    <hkern u1="W" u2="a" k="53" />
    <hkern u1="W" u2="S" k="20" />
    <hkern u1="W" u2="&#x2f;" k="63" />
    <hkern u1="W" u2="O" k="27" />
    <hkern u1="W" u2="A" k="56" />
    <hkern u1="W" u2="&#x141;" k="6" />
    <hkern u1="W" u2="J" k="82" />
    <hkern u1="W" u2="&#xd0;" k="2" />
    <hkern u1="W" u2="&#x110;" k="2" />
    <hkern u1="W" u2="&#x20;" k="35" />
    <hkern g1="W.alt" u2="&#x30;" k="17" />
    <hkern g1="W.alt" u2="z" k="16" />
    <hkern g1="W.alt" u2="x" k="4" />
    <hkern g1="W.alt" g2="w.alt" k="2" />
    <hkern g1="W.alt" u2="w" k="2" />
    <hkern g1="W.alt" u2="v" k="2" />
    <hkern g1="W.alt" u2="u" k="28" />
    <hkern g1="W.alt" u2="&#x32;" k="4" />
    <hkern g1="W.alt" u2="&#x2122;" k="-14" />
    <hkern g1="W.alt" u2="&#x33;" k="4" />
    <hkern g1="W.alt" u2="t" k="5" />
    <hkern g1="W.alt" u2="&#x36;" k="4" />
    <hkern g1="W.alt" u2="&#x259;" k="46" />
    <hkern g1="W.alt" u2="s" k="46" />
    <hkern g1="W.alt" u2="&#xae;" k="11" />
    <hkern g1="W.alt" u2="&#x159;" k="8" />
    <hkern g1="W.alt" u2="&#x2e;" k="45" />
    <hkern g1="W.alt" u2="&#x29;" k="-14" />
    <hkern g1="W.alt" u2="&#x31;" k="3" />
    <hkern g1="W.alt" u2="o" k="46" />
    <hkern g1="W.alt" u2="&#x39;" k="15" />
    <hkern g1="W.alt" u2="n" k="30" />
    <hkern g1="W.alt" u2="&#x135;" k="-29" />
    <hkern g1="W.alt" u2="&#x129;" k="-58" />
    <hkern g1="W.alt" u2="&#x12b;" k="-46" />
    <hkern g1="W.alt" u2="&#xec;" k="-23" />
    <hkern g1="W.alt" u2="&#xef;" k="-57" />
    <hkern g1="W.alt" u2="&#xee;" k="-29" />
    <hkern g1="W.alt" u2="&#x12d;" k="-33" />
    <hkern g1="W.alt" u2="&#xed;" k="13" />
    <hkern g1="W.alt" u2="&#x2d;" k="25" />
    <hkern g1="W.alt" u2="&#x127;" k="-7" />
    <hkern g1="W.alt" u2="&#x203a;" k="13" />
    <hkern g1="W.alt" u2="&#x2039;" k="27" />
    <hkern g1="W.alt" u2="&#xdf;" k="14" />
    <hkern g1="W.alt" u2="&#x34;" k="39" />
    <hkern g1="W.alt" u2="&#x35;" k="23" />
    <hkern g1="W.alt" u2="f" k="17" />
    <hkern g1="W.alt" u2="&#xf0;" k="48" />
    <hkern g1="W.alt" u2="&#x38;" k="4" />
    <hkern g1="W.alt" u2="&#x131;" k="30" />
    <hkern g1="W.alt" u2="d" k="44" />
    <hkern g1="W.alt" u2="&#xa9;" k="11" />
    <hkern g1="W.alt" u2="&#x3a;" k="7" />
    <hkern g1="W.alt" u2="\" k="-12" />
    <hkern g1="W.alt" u2="&#x40;" k="23" />
    <hkern g1="W.alt" u2="&#x2a;" k="-24" />
    <hkern g1="W.alt" u2="&#x2bc;" k="10" />
    <hkern g1="W.alt" u2="&#x26;" k="4" />
    <hkern g1="W.alt" u2="a" k="43" />
    <hkern g1="W.alt" u2="&#x2f;" k="42" />
    <hkern g1="W.alt" u2="&#x20;" k="31" />
    <hkern u1="X" u2="&#x30;" k="12" />
    <hkern u1="X" g2="y.alt" k="32" />
    <hkern u1="X" u2="y" k="29" />
    <hkern u1="X" g2="w.alt" k="30" />
    <hkern u1="X" u2="w" k="32" />
    <hkern u1="X" u2="v" k="30" />
    <hkern u1="X" u2="u" k="22" />
    <hkern u1="X" u2="&#x32;" k="-3" />
    <hkern u1="X" u2="&#x2122;" k="-20" />
    <hkern u1="X" g2="t.alt" k="5" />
    <hkern u1="X" u2="t" k="14" />
    <hkern u1="X" u2="&#x259;" k="27" />
    <hkern u1="X" u2="&#xae;" k="14" />
    <hkern u1="X" u2="&#x2019;" k="-7" />
    <hkern u1="X" u2="&#x2018;" k="-7" />
    <hkern u1="X" u2="&#x29;" k="-18" />
    <hkern u1="X" u2="&#x1ff;" k="22" />
    <hkern u1="X" u2="&#xf8;" k="22" />
    <hkern u1="X" u2="o" k="39" />
    <hkern u1="X" u2="n" k="9" />
    <hkern u1="X" u2="&#x135;" k="-20" />
    <hkern u1="X" u2="&#x129;" k="-50" />
    <hkern u1="X" u2="&#x12b;" k="-54" />
    <hkern u1="X" u2="&#xec;" k="-27" />
    <hkern u1="X" u2="&#xef;" k="-60" />
    <hkern u1="X" u2="&#xee;" k="-20" />
    <hkern u1="X" u2="&#x12d;" k="-37" />
    <hkern u1="X" u2="&#xed;" k="7" />
    <hkern u1="X" u2="&#x2d;" k="25" />
    <hkern u1="X" u2="&#x127;" k="-14" />
    <hkern u1="X" u2="&#x2039;" k="16" />
    <hkern u1="X" u2="&#xdf;" k="6" />
    <hkern u1="X" u2="&#x34;" k="-12" />
    <hkern u1="X" u2="f" k="21" />
    <hkern u1="X" u2="&#xf0;" k="36" />
    <hkern u1="X" u2="&#x131;" k="7" />
    <hkern u1="X" u2="d" k="37" />
    <hkern u1="X" u2="&#xa9;" k="14" />
    <hkern u1="X" u2="]" k="-6" />
    <hkern u1="X" u2="&#x7d;" k="-5" />
    <hkern u1="X" u2="\" k="-16" />
    <hkern u1="X" u2="&#x2a;" k="-23" />
    <hkern u1="X" u2="&#x2bc;" k="4" />
    <hkern u1="X" u2="a" k="10" />
    <hkern u1="X" u2="S" k="7" />
    <hkern u1="X" u2="&#x1fe;" k="13" />
    <hkern u1="X" u2="&#x2f;" k="-10" />
    <hkern u1="X" u2="&#xd8;" k="13" />
    <hkern u1="X" u2="O" k="29" />
    <hkern u1="X" u2="&#x20;" k="4" />
    <hkern u1="Y" u2="&#x30;" k="42" />
    <hkern u1="Y" u2="&#x17e;" k="11" />
    <hkern u1="Y" u2="z" k="46" />
    <hkern u1="Y" g2="y.alt" k="33" />
    <hkern u1="Y" u2="y" k="29" />
    <hkern u1="Y" u2="x" k="31" />
    <hkern u1="Y" g2="w.alt" k="34" />
    <hkern u1="Y" u2="w" k="32" />
    <hkern u1="Y" u2="v" k="33" />
    <hkern u1="Y" u2="u" k="56" />
    <hkern u1="Y" u2="&#x32;" k="28" />
    <hkern u1="Y" u2="&#x2122;" k="-20" />
    <hkern u1="Y" u2="&#x33;" k="28" />
    <hkern u1="Y" g2="t.alt" k="8" />
    <hkern u1="Y" u2="t" k="19" />
    <hkern u1="Y" u2="&#xc6;" k="100" />
    <hkern u1="Y" u2="&#x36;" k="24" />
    <hkern u1="Y" u2="&#x15d;" k="40" />
    <hkern u1="Y" u2="&#x259;" k="92" />
    <hkern u1="Y" u2="&#x161;" k="23" />
    <hkern u1="Y" u2="s" k="82" />
    <hkern u1="Y" u2="&#xae;" k="28" />
    <hkern u1="Y" u2="&#x159;" k="38" />
    <hkern u1="Y" u2="&#x2019;" k="-10" />
    <hkern u1="Y" u2="&#x2018;" k="-9" />
    <hkern u1="Y" u2="&#x2e;" k="73" />
    <hkern u1="Y" u2="&#x29;" k="-20" />
    <hkern u1="Y" u2="&#x31;" k="31" />
    <hkern u1="Y" u2="&#xf6;" k="47" />
    <hkern u1="Y" u2="o" k="81" />
    <hkern u1="Y" u2="&#x39;" k="40" />
    <hkern u1="Y" u2="n" k="58" />
    <hkern u1="Y" u2="&#x135;" k="-26" />
    <hkern u1="Y" u2="&#x129;" k="-56" />
    <hkern u1="Y" u2="&#x12b;" k="-55" />
    <hkern u1="Y" u2="&#xec;" k="-30" />
    <hkern u1="Y" u2="&#xef;" k="-62" />
    <hkern u1="Y" u2="&#xee;" k="-26" />
    <hkern u1="Y" u2="&#x12d;" k="-40" />
    <hkern u1="Y" u2="&#xed;" k="24" />
    <hkern u1="Y" u2="&#x2d;" k="58" />
    <hkern u1="Y" u2="&#x127;" k="-17" />
    <hkern u1="Y" u2="&#x203a;" k="37" />
    <hkern u1="Y" u2="&#x2039;" k="58" />
    <hkern u1="Y" u2="&#xdf;" k="31" />
    <hkern u1="Y" u2="&#x34;" k="67" />
    <hkern u1="Y" u2="&#x35;" k="40" />
    <hkern u1="Y" u2="f" k="39" />
    <hkern u1="Y" u2="&#xf0;" k="82" />
    <hkern u1="Y" u2="&#x38;" k="21" />
    <hkern u1="Y" u2="&#xeb;" k="47" />
    <hkern u1="Y" u2="&#x11b;" k="45" />
    <hkern u1="Y" u2="&#x131;" k="58" />
    <hkern u1="Y" u2="d" k="79" />
    <hkern u1="Y" u2="&#xa9;" k="28" />
    <hkern u1="Y" u2="&#x3a;" k="28" />
    <hkern u1="Y" u2="&#x10d;" k="47" />
    <hkern u1="Y" u2="]" k="-7" />
    <hkern u1="Y" u2="&#x7d;" k="-7" />
    <hkern u1="Y" u2="\" k="-17" />
    <hkern u1="Y" u2="&#xe3;" k="34" />
    <hkern u1="Y" u2="&#x40;" k="49" />
    <hkern u1="Y" u2="&#x2a;" k="-24" />
    <hkern u1="Y" u2="&#x2bc;" k="14" />
    <hkern u1="Y" u2="&#x26;" k="22" />
    <hkern u1="Y" u2="&#xe4;" k="29" />
    <hkern u1="Y" u2="a" k="77" />
    <hkern u1="Y" u2="S" k="26" />
    <hkern u1="Y" u2="&#x2f;" k="63" />
    <hkern u1="Y" u2="O" k="39" />
    <hkern u1="Y" u2="A" k="69" />
    <hkern u1="Y" u2="&#x141;" k="3" />
    <hkern u1="Y" u2="J" k="89" />
    <hkern u1="Y" u2="&#xd0;" k="2" />
    <hkern u1="Y" u2="&#x110;" k="2" />
    <hkern u1="Y" u2="&#x20;" k="38" />
    <hkern u1="Z" u2="&#x30;" k="3" />
    <hkern u1="Z" g2="y.alt" k="15" />
    <hkern u1="Z" u2="y" k="14" />
    <hkern u1="Z" g2="w.alt" k="14" />
    <hkern u1="Z" u2="w" k="14" />
    <hkern u1="Z" u2="v" k="15" />
    <hkern u1="Z" u2="u" k="12" />
    <hkern u1="Z" g2="t.alt" k="6" />
    <hkern u1="Z" u2="t" k="12" />
    <hkern u1="Z" u2="&#x259;" k="12" />
    <hkern u1="Z" u2="s" k="3" />
    <hkern u1="Z" u2="&#xae;" k="5" />
    <hkern u1="Z" u2="o" k="15" />
    <hkern u1="Z" u2="n" k="8" />
    <hkern u1="Z" u2="&#x135;" k="-19" />
    <hkern u1="Z" u2="&#x129;" k="-55" />
    <hkern u1="Z" u2="&#x12b;" k="-16" />
    <hkern u1="Z" u2="&#xef;" k="-27" />
    <hkern u1="Z" u2="&#xee;" k="-19" />
    <hkern u1="Z" u2="&#x12d;" k="-4" />
    <hkern u1="Z" u2="&#x2d;" k="7" />
    <hkern u1="Z" u2="&#x2039;" k="4" />
    <hkern u1="Z" u2="f" k="16" />
    <hkern u1="Z" u2="&#xf0;" k="13" />
    <hkern u1="Z" u2="&#x131;" k="3" />
    <hkern u1="Z" u2="d" k="15" />
    <hkern u1="Z" u2="&#xa9;" k="5" />
    <hkern u1="Z" u2="&#x2bc;" k="16" />
    <hkern u1="Z" u2="a" k="5" />
    <hkern u1="Z" u2="O" k="14" />
    <hkern u1="a" g2="y.alt" k="23" />
    <hkern u1="a" u2="y" k="20" />
    <hkern u1="a" g2="w.alt" k="19" />
    <hkern u1="a" u2="w" k="26" />
    <hkern u1="a" u2="v" k="21" />
    <hkern u1="a" u2="&#x2122;" k="34" />
    <hkern u1="a" g2="t.alt" k="9" />
    <hkern u1="a" u2="t" k="9" />
    <hkern u1="a" u2="&#x27;" k="25" />
    <hkern u1="a" u2="&#x2019;" k="27" />
    <hkern u1="a" u2="&#x2018;" k="31" />
    <hkern u1="a" u2="&#x3f;" k="5" />
    <hkern u1="a" u2="&#xba;" k="15" />
    <hkern u1="a" u2="&#xaa;" k="9" />
    <hkern u1="a" u2="f" k="3" />
    <hkern u1="a" u2="\" k="47" />
    <hkern u1="a" u2="&#x2a;" k="28" />
    <hkern u1="a" u2="&#x2bc;" k="63" />
    <hkern u1="a" u2="Z" k="2" />
    <hkern u1="a" u2="Y" k="81" />
    <hkern u1="a" u2="W" k="57" />
    <hkern u1="a" u2="V" k="55" />
    <hkern u1="a" u2="U" k="15" />
    <hkern u1="a" u2="T" k="86" />
    <hkern u1="a" u2="S" k="2" />
    <hkern u1="a" u2="O" k="9" />
    <hkern u1="a" u2="I" k="3" />
    <hkern u1="a" u2="&#xd0;" k="3" />
    <hkern g1="a.alt" u2="&#x2122;" k="10" />
    <hkern g1="a.alt" u2="\" k="22" />
    <hkern g1="a.alt" u2="&#x2bc;" k="23" />
    <hkern g1="a.alt2" g2="y.alt" k="15" />
    <hkern g1="a.alt2" u2="y" k="12" />
    <hkern g1="a.alt2" g2="w.alt" k="10" />
    <hkern g1="a.alt2" u2="w" k="16" />
    <hkern g1="a.alt2" u2="v" k="12" />
    <hkern g1="a.alt2" u2="&#x2122;" k="25" />
    <hkern g1="a.alt2" g2="t.alt" k="7" />
    <hkern g1="a.alt2" u2="t" k="5" />
    <hkern g1="a.alt2" u2="&#x27;" k="16" />
    <hkern g1="a.alt2" u2="&#x2019;" k="18" />
    <hkern g1="a.alt2" u2="&#x2018;" k="22" />
    <hkern g1="a.alt2" u2="&#x3f;" k="5" />
    <hkern g1="a.alt2" u2="&#xba;" k="3" />
    <hkern g1="a.alt2" u2="\" k="37" />
    <hkern g1="a.alt2" u2="&#x2a;" k="20" />
    <hkern g1="a.alt2" u2="&#x2bc;" k="55" />
    <hkern u1="&#x26;" u2="z" k="-9" />
    <hkern u1="&#x26;" g2="y.alt" k="18" />
    <hkern u1="&#x26;" u2="y" k="17" />
    <hkern u1="&#x26;" u2="x" k="-28" />
    <hkern u1="&#x26;" g2="w.alt" k="14" />
    <hkern u1="&#x26;" u2="w" k="19" />
    <hkern u1="&#x26;" u2="v" k="16" />
    <hkern u1="&#x26;" u2="&#xc6;" k="-48" />
    <hkern u1="&#x26;" u2="s" k="-8" />
    <hkern u1="&#x26;" u2="&#x27;" k="37" />
    <hkern u1="&#x26;" u2="&#x2019;" k="35" />
    <hkern u1="&#x26;" u2="&#x1ff;" k="-11" />
    <hkern u1="&#x26;" u2="&#xf8;" k="-11" />
    <hkern u1="&#x26;" u2="Z" k="-8" />
    <hkern u1="&#x26;" u2="Y" k="51" />
    <hkern u1="&#x26;" u2="X" k="-31" />
    <hkern u1="&#x26;" u2="W" k="43" />
    <hkern u1="&#x26;" u2="V" k="41" />
    <hkern u1="&#x26;" u2="U" k="9" />
    <hkern u1="&#x26;" u2="&#x166;" k="27" />
    <hkern u1="&#x26;" u2="T" k="42" />
    <hkern u1="&#x26;" u2="&#x1fe;" k="-26" />
    <hkern u1="&#x26;" u2="&#xd8;" k="-26" />
    <hkern u1="&#x26;" u2="A" k="-32" />
    <hkern u1="&#x26;" u2="J" k="-15" />
    <hkern u1="&#x105;" u2="j" k="-86" />
    <hkern u1="&#x2bc;" u2="z" k="25" />
    <hkern u1="&#x2bc;" g2="y.alt" k="12" />
    <hkern u1="&#x2bc;" u2="y" k="12" />
    <hkern u1="&#x2bc;" u2="x" k="16" />
    <hkern u1="&#x2bc;" g2="w.alt" k="12" />
    <hkern u1="&#x2bc;" u2="w" k="11" />
    <hkern u1="&#x2bc;" u2="v" k="12" />
    <hkern u1="&#x2bc;" u2="u" k="28" />
    <hkern u1="&#x2bc;" g2="t.alt" k="25" />
    <hkern u1="&#x2bc;" u2="t" k="32" />
    <hkern u1="&#x2bc;" u2="s" k="59" />
    <hkern u1="&#x2bc;" u2="o" k="67" />
    <hkern u1="&#x2bc;" u2="n" k="29" />
    <hkern u1="&#x2bc;" g2="l.alt" k="21" />
    <hkern u1="&#x2bc;" u2="l" k="21" />
    <hkern u1="&#x2bc;" u2="i" k="20" />
    <hkern u1="&#x2bc;" u2="h" k="21" />
    <hkern u1="&#x2bc;" u2="f" k="44" />
    <hkern u1="&#x2bc;" u2="d" k="92" />
    <hkern u1="&#x2bc;" u2="a" k="54" />
    <hkern u1="&#x2a;" u2="x" k="-17" />
    <hkern u1="&#x2a;" g2="w.alt" k="-13" />
    <hkern u1="&#x2a;" u2="w" k="-16" />
    <hkern u1="&#x2a;" u2="v" k="-13" />
    <hkern u1="&#x2a;" g2="t.alt" k="-28" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="89" />
    <hkern u1="&#x2a;" u2="s" k="18" />
    <hkern u1="&#x2a;" u2="o" k="27" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-41" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-73" />
    <hkern u1="&#x2a;" u2="&#x12b;" k="-24" />
    <hkern u1="&#x2a;" u2="&#xec;" k="-11" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-49" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-41" />
    <hkern u1="&#x2a;" u2="&#x12d;" k="-5" />
    <hkern u1="&#x2a;" u2="f" k="3" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="42" />
    <hkern u1="&#x2a;" u2="d" k="34" />
    <hkern u1="&#x2a;" u2="a" k="7" />
    <hkern u1="&#x2a;" u2="Y" k="-23" />
    <hkern u1="&#x2a;" u2="X" k="-23" />
    <hkern u1="&#x2a;" u2="W" k="-22" />
    <hkern u1="&#x2a;" u2="V" k="-4" />
    <hkern u1="&#x2a;" u2="&#x166;" k="-8" />
    <hkern u1="&#x2a;" u2="T" k="-19" />
    <hkern u1="&#x2a;" u2="O" k="3" />
    <hkern u1="&#x2a;" u2="A" k="57" />
    <hkern u1="&#x2a;" u2="J" k="81" />
    <hkern u1="&#x2a;" u2="&#x126;" k="-12" />
    <hkern u1="&#x40;" u2="&#x27;" k="26" />
    <hkern u1="&#x40;" u2="&#x2019;" k="27" />
    <hkern u1="&#x40;" u2="Y" k="48" />
    <hkern u1="&#x40;" u2="W" k="35" />
    <hkern u1="&#x40;" u2="V" k="34" />
    <hkern u1="&#x40;" u2="T" k="42" />
    <hkern u1="&#x40;" u2="A" k="11" />
    <hkern u1="b" u2="z" k="9" />
    <hkern u1="b" g2="y.alt" k="18" />
    <hkern u1="b" u2="y" k="16" />
    <hkern u1="b" u2="x" k="19" />
    <hkern u1="b" g2="w.alt" k="15" />
    <hkern u1="b" u2="w" k="19" />
    <hkern u1="b" u2="v" k="16" />
    <hkern u1="b" u2="&#x2122;" k="32" />
    <hkern u1="b" g2="t.alt" k="9" />
    <hkern u1="b" u2="t" k="7" />
    <hkern u1="b" u2="&#xc6;" k="21" />
    <hkern u1="b" u2="s" k="3" />
    <hkern u1="b" u2="&#x27;" k="47" />
    <hkern u1="b" u2="&#x2019;" k="40" />
    <hkern u1="b" u2="&#x2018;" k="45" />
    <hkern u1="b" u2="&#x3f;" k="7" />
    <hkern u1="b" u2="&#x2e;" k="3" />
    <hkern u1="b" u2="&#x29;" k="17" />
    <hkern u1="b" u2="&#xba;" k="12" />
    <hkern u1="b" u2="&#xaa;" k="3" />
    <hkern u1="b" u2="&#x142;" k="-10" />
    <hkern u1="b" u2="f" k="1" />
    <hkern u1="b" u2="\" k="38" />
    <hkern u1="b" u2="&#x2a;" k="34" />
    <hkern u1="b" u2="&#x2bc;" k="90" />
    <hkern u1="b" u2="Z" k="18" />
    <hkern u1="b" u2="Y" k="78" />
    <hkern u1="b" u2="X" k="37" />
    <hkern u1="b" u2="W" k="62" />
    <hkern u1="b" u2="V" k="59" />
    <hkern u1="b" u2="U" k="11" />
    <hkern u1="b" u2="T" k="87" />
    <hkern u1="b" u2="S" k="10" />
    <hkern u1="b" u2="A" k="16" />
    <hkern u1="b" u2="J" k="15" />
    <hkern u1="b" u2="I" k="8" />
    <hkern u1="b" u2="&#xd0;" k="5" />
    <hkern u1="\" g2="zero.lnum" k="17" />
    <hkern u1="\" u2="&#x30;" k="7" />
    <hkern u1="\" g2="y.alt" k="32" />
    <hkern u1="\" u2="y" k="31" />
    <hkern u1="\" u2="x" k="-9" />
    <hkern u1="\" g2="w.alt" k="26" />
    <hkern u1="\" u2="w" k="35" />
    <hkern u1="\" u2="v" k="28" />
    <hkern u1="\" u2="t" k="9" />
    <hkern u1="\" u2="&#xc6;" k="-23" />
    <hkern u1="\" g2="six.lnum" k="7" />
    <hkern u1="\" u2="&#x36;" k="7" />
    <hkern u1="\" u2="&#x37;" k="7" />
    <hkern u1="\" u2="&#x27;" k="82" />
    <hkern u1="\" u2="&#x2019;" k="81" />
    <hkern u1="\" u2="Y" k="61" />
    <hkern u1="\" u2="X" k="-10" />
    <hkern u1="\" u2="W" k="61" />
    <hkern u1="\" u2="V" k="58" />
    <hkern u1="\" u2="U" k="19" />
    <hkern u1="\" u2="&#x166;" k="45" />
    <hkern u1="\" u2="T" k="53" />
    <hkern u1="\" u2="&#x1fe;" k="-7" />
    <hkern u1="\" u2="&#xd8;" k="-7" />
    <hkern u1="\" u2="O" k="18" />
    <hkern u1="\" u2="A" k="-12" />
    <hkern u1="\" u2="J" k="-5" />
    <hkern u1="&#x7c;" u2="&#x135;" k="-68" />
    <hkern u1="&#x7c;" u2="&#x129;" k="-30" />
    <hkern u1="&#x7c;" u2="j" k="-68" />
    <hkern u1="&#x7c;" u2="&#xef;" k="-14" />
    <hkern u1="&#x7c;" u2="&#xee;" k="-5" />
    <hkern u1="&#x7c;" u2="&#x128;" k="-23" />
    <hkern u1="&#x7c;" u2="&#xcf;" k="-7" />
    <hkern u1="&#x7b;" u2="&#xc6;" k="-13" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-22" />
    <hkern u1="&#x7b;" u2="&#x129;" k="-42" />
    <hkern u1="&#x7b;" u2="&#x12b;" k="-5" />
    <hkern u1="&#x7b;" u2="j" k="-22" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-28" />
    <hkern u1="&#x7b;" u2="&#xee;" k="-20" />
    <hkern u1="&#x7b;" u2="&#x12d;" k="-5" />
    <hkern u1="&#x7b;" u2="Y" k="-6" />
    <hkern u1="&#x7b;" u2="X" k="-5" />
    <hkern u1="&#x7b;" u2="W" k="-4" />
    <hkern u1="[" u2="&#xc6;" k="-14" />
    <hkern u1="[" u2="&#x135;" k="-26" />
    <hkern u1="[" u2="&#x129;" k="-42" />
    <hkern u1="[" u2="&#x12b;" k="-5" />
    <hkern u1="[" u2="j" k="-26" />
    <hkern u1="[" u2="&#xef;" k="-28" />
    <hkern u1="[" u2="&#xee;" k="-20" />
    <hkern u1="[" u2="&#x12d;" k="-5" />
    <hkern u1="[" u2="Y" k="-6" />
    <hkern u1="[" u2="X" k="-6" />
    <hkern u1="[" u2="W" k="-5" />
    <hkern u1="[" u2="V" k="-4" />
    <hkern u1="[" u2="A" k="-3" />
    <hkern u1="c" g2="y.alt" k="9" />
    <hkern u1="c" u2="y" k="10" />
    <hkern u1="c" u2="x" k="4" />
    <hkern u1="c" g2="w.alt" k="6" />
    <hkern u1="c" u2="w" k="9" />
    <hkern u1="c" u2="v" k="7" />
    <hkern u1="c" u2="&#x2122;" k="19" />
    <hkern u1="c" u2="&#xc6;" k="3" />
    <hkern u1="c" u2="&#x259;" k="4" />
    <hkern u1="c" u2="s" k="5" />
    <hkern u1="c" u2="&#x27;" k="4" />
    <hkern u1="c" u2="&#x2019;" k="12" />
    <hkern u1="c" u2="&#x2018;" k="14" />
    <hkern u1="c" u2="&#x3f;" k="3" />
    <hkern u1="c" u2="o" k="6" />
    <hkern u1="c" u2="&#x2d;" k="8" />
    <hkern u1="c" u2="&#x2039;" k="5" />
    <hkern u1="c" u2="&#xf0;" k="7" />
    <hkern u1="c" u2="d" k="6" />
    <hkern u1="c" u2="\" k="29" />
    <hkern u1="c" u2="&#x2a;" k="14" />
    <hkern u1="c" u2="&#x2bc;" k="45" />
    <hkern u1="c" u2="Z" k="3" />
    <hkern u1="c" u2="Y" k="69" />
    <hkern u1="c" u2="X" k="9" />
    <hkern u1="c" u2="W" k="53" />
    <hkern u1="c" u2="V" k="53" />
    <hkern u1="c" u2="U" k="8" />
    <hkern u1="c" u2="T" k="80" />
    <hkern u1="c" u2="S" k="8" />
    <hkern u1="c" u2="O" k="5" />
    <hkern u1="c" u2="A" k="6" />
    <hkern u1="c" u2="&#xd0;" k="3" />
    <hkern u1="&#xe7;" u2="j" k="-14" />
    <hkern u1="&#x3a;" u2="Y" k="28" />
    <hkern u1="&#x3a;" u2="W" k="18" />
    <hkern u1="&#x3a;" u2="V" k="17" />
    <hkern u1="&#x3a;" u2="&#x166;" k="3" />
    <hkern u1="&#x3a;" u2="T" k="21" />
    <hkern u1="&#x2c;" u2="&#x135;" k="-23" />
    <hkern u1="&#x2c;" u2="j" k="-23" />
    <hkern u1="&#x2c;" u2="&#x166;" k="26" />
    <hkern u1="d" g2="y.alt" k="6" />
    <hkern u1="d" u2="y" k="4" />
    <hkern u1="d" g2="w.alt" k="5" />
    <hkern u1="d" u2="w" k="7" />
    <hkern u1="d" u2="v" k="5" />
    <hkern u1="d" g2="t.alt" k="3" />
    <hkern u1="d" u2="t" k="4" />
    <hkern u1="d" u2="&#x129;" k="-20" />
    <hkern u1="d" u2="&#x2bc;" k="29" />
    <hkern u1="d" u2="Y" k="10" />
    <hkern u1="d" u2="W" k="9" />
    <hkern u1="d" u2="V" k="9" />
    <hkern u1="d" u2="U" k="11" />
    <hkern u1="d" u2="T" k="10" />
    <hkern u1="d" u2="O" k="9" />
    <hkern u1="d" u2="&#xd0;" k="5" />
    <hkern g1="d.alt" u2="&#x135;" k="-12" />
    <hkern g1="d.alt" u2="&#x129;" k="-36" />
    <hkern g1="d.alt" u2="&#xef;" k="-20" />
    <hkern g1="d.alt" u2="&#xee;" k="-12" />
    <hkern g1="d.alt" u2="&#x2bc;" k="22" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-72" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-121" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-89" />
    <hkern u1="&#x10f;" g2="t.alt" k="-70" />
    <hkern u1="&#x10f;" u2="t" k="-58" />
    <hkern u1="&#x10f;" u2="&#xae;" k="-11" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-101" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-116" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-114" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-57" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-125" />
    <hkern u1="&#x10f;" u2="&#xba;" k="-57" />
    <hkern u1="&#x10f;" u2="&#xaa;" k="-77" />
    <hkern u1="&#x10f;" g2="l.alt" k="-97" />
    <hkern u1="&#x10f;" u2="l" k="-100" />
    <hkern u1="&#x10f;" u2="i" k="-97" />
    <hkern u1="&#x10f;" u2="h" k="-97" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-34" />
    <hkern u1="&#x10f;" g2="f_i" k="-19" />
    <hkern u1="&#x10f;" g2="f_f_l" k="-13" />
    <hkern u1="&#x10f;" g2="f_f_i" k="-13" />
    <hkern u1="&#x10f;" g2="f_f" k="-13" />
    <hkern u1="&#x10f;" u2="f" k="-22" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-81" />
    <hkern u1="&#x10f;" u2="&#xf0;" k="-54" />
    <hkern u1="&#x10f;" u2="&#xa9;" k="-11" />
    <hkern u1="&#x10f;" u2="]" k="-118" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-119" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-92" />
    <hkern u1="&#x10f;" u2="\" k="-121" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-94" />
    <hkern u1="&#x10f;" u2="&#x26;" k="-29" />
    <hkern u1="&#x10f;" u2="&#x20;" k="-34" />
    <hkern u1="&#x111;" u2="&#x2a;" k="-5" />
    <hkern u1="&#xb0;" g2="zero.lnum" k="3" />
    <hkern u1="&#xb0;" u2="&#x30;" k="14" />
    <hkern u1="&#xb0;" u2="&#x33;" k="7" />
    <hkern u1="&#xb0;" u2="&#x31;" k="5" />
    <hkern u1="&#xb0;" u2="&#x39;" k="22" />
    <hkern u1="&#xb0;" g2="four.lnum" k="52" />
    <hkern u1="&#xb0;" u2="&#x34;" k="82" />
    <hkern u1="&#xb0;" u2="&#x35;" k="19" />
    <hkern u1="&#x24;" u2="&#x33;" k="11" />
    <hkern u1="&#x24;" u2="&#x37;" k="4" />
    <hkern u1="e" u2="z" k="7" />
    <hkern u1="e" g2="y.alt" k="17" />
    <hkern u1="e" u2="y" k="16" />
    <hkern u1="e" u2="x" k="20" />
    <hkern u1="e" g2="w.alt" k="12" />
    <hkern u1="e" u2="w" k="18" />
    <hkern u1="e" u2="v" k="14" />
    <hkern u1="e" u2="&#x2122;" k="26" />
    <hkern u1="e" g2="t.alt" k="8" />
    <hkern u1="e" u2="t" k="5" />
    <hkern u1="e" u2="&#xc6;" k="20" />
    <hkern u1="e" u2="&#x27;" k="19" />
    <hkern u1="e" u2="&#x2019;" k="21" />
    <hkern u1="e" u2="&#x2018;" k="24" />
    <hkern u1="e" u2="&#x3f;" k="6" />
    <hkern u1="e" u2="&#x29;" k="14" />
    <hkern u1="e" u2="&#xba;" k="3" />
    <hkern u1="e" u2="&#x142;" k="-8" />
    <hkern u1="e" u2="\" k="37" />
    <hkern u1="e" u2="&#x2a;" k="24" />
    <hkern u1="e" u2="&#x2bc;" k="56" />
    <hkern u1="e" u2="Z" k="17" />
    <hkern u1="e" u2="Y" k="93" />
    <hkern u1="e" u2="X" k="27" />
    <hkern u1="e" u2="W" k="58" />
    <hkern u1="e" u2="V" k="56" />
    <hkern u1="e" u2="U" k="8" />
    <hkern u1="e" u2="T" k="92" />
    <hkern u1="e" u2="S" k="9" />
    <hkern u1="e" u2="A" k="14" />
    <hkern u1="e" u2="J" k="13" />
    <hkern u1="e" u2="I" k="7" />
    <hkern u1="e" u2="&#xd0;" k="4" />
    <hkern u1="&#x38;" u2="&#xc6;" k="15" />
    <hkern u1="&#x38;" u2="&#x2b;" k="3" />
    <hkern u1="&#x38;" u2="Y" k="21" />
    <hkern u1="&#x38;" u2="W" k="15" />
    <hkern u1="&#x38;" u2="V" k="15" />
    <hkern u1="&#x38;" u2="T" k="4" />
    <hkern u1="&#x38;" u2="A" k="13" />
    <hkern g1="eight.lnum" u2="&#x2b;" k="3" />
    <hkern u1="&#x14b;" u2="j" k="-40" />
    <hkern u1="&#x3d;" u2="&#x33;" k="10" />
    <hkern u1="&#x3d;" g2="seven.lnum" k="9" />
    <hkern u1="&#x3d;" u2="&#x37;" k="9" />
    <hkern u1="&#x3d;" g2="one.lnum" k="4" />
    <hkern u1="&#x3d;" u2="&#x31;" k="6" />
    <hkern u1="&#x3d;" u2="&#x34;" k="4" />
    <hkern u1="&#xf0;" u2="z" k="6" />
    <hkern u1="&#xf0;" g2="y.alt" k="12" />
    <hkern u1="&#xf0;" u2="y" k="12" />
    <hkern u1="&#xf0;" u2="x" k="15" />
    <hkern u1="&#xf0;" g2="w.alt" k="10" />
    <hkern u1="&#xf0;" u2="w" k="13" />
    <hkern u1="&#xf0;" u2="v" k="10" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="4" />
    <hkern u1="&#xf0;" g2="t.alt" k="4" />
    <hkern u1="&#xf0;" u2="t" k="1" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="26" />
    <hkern u1="&#xf0;" u2="&#x27;" k="5" />
    <hkern u1="&#xf0;" u2="&#x2019;" k="5" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="5" />
    <hkern u1="&#xf0;" u2="&#x29;" k="4" />
    <hkern u1="&#xf0;" u2="&#x142;" k="-8" />
    <hkern u1="&#xf0;" u2="&#x129;" k="-7" />
    <hkern u1="&#xf0;" u2="\" k="3" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="6" />
    <hkern u1="&#xf0;" u2="Z" k="20" />
    <hkern u1="&#xf0;" u2="Y" k="33" />
    <hkern u1="&#xf0;" u2="X" k="29" />
    <hkern u1="&#xf0;" u2="W" k="27" />
    <hkern u1="&#xf0;" u2="V" k="26" />
    <hkern u1="&#xf0;" u2="U" k="9" />
    <hkern u1="&#xf0;" u2="T" k="25" />
    <hkern u1="&#xf0;" u2="S" k="9" />
    <hkern u1="&#xf0;" u2="&#x2f;" k="9" />
    <hkern u1="&#xf0;" u2="A" k="18" />
    <hkern u1="&#xf0;" u2="J" k="18" />
    <hkern u1="&#xf0;" u2="I" k="8" />
    <hkern u1="&#xf0;" u2="&#xd0;" k="4" />
    <hkern u1="&#xa1;" u2="&#x129;" k="-24" />
    <hkern u1="&#xa1;" u2="&#xef;" k="-8" />
    <hkern u1="f" u2="z" k="2" />
    <hkern u1="f" u2="&#x2122;" k="-22" />
    <hkern u1="f" u2="&#xc6;" k="46" />
    <hkern u1="f" u2="&#x259;" k="2" />
    <hkern u1="f" u2="&#x2019;" k="-7" />
    <hkern u1="f" u2="&#x2018;" k="-7" />
    <hkern u1="f" u2="&#x2e;" k="29" />
    <hkern u1="f" u2="&#x29;" k="-29" />
    <hkern u1="f" u2="o" k="3" />
    <hkern u1="f" u2="&#x142;" k="-14" />
    <hkern u1="f" u2="&#x135;" k="-40" />
    <hkern u1="f" u2="&#x129;" k="-80" />
    <hkern u1="f" u2="&#x12b;" k="-67" />
    <hkern u1="f" u2="&#xec;" k="-32" />
    <hkern u1="f" u2="&#xef;" k="-70" />
    <hkern u1="f" u2="&#xee;" k="-40" />
    <hkern u1="f" u2="&#x12d;" k="-47" />
    <hkern u1="f" u2="&#x2d;" k="13" />
    <hkern u1="f" u2="&#x2039;" k="11" />
    <hkern u1="f" u2="&#xf0;" k="14" />
    <hkern u1="f" u2="d" k="3" />
    <hkern u1="f" u2="]" k="-9" />
    <hkern u1="f" u2="&#x7d;" k="-8" />
    <hkern u1="f" u2="\" k="-22" />
    <hkern u1="f" u2="&#x2a;" k="-13" />
    <hkern u1="f" u2="a" k="8" />
    <hkern u1="f" u2="Z" k="-4" />
    <hkern u1="f" u2="Y" k="-48" />
    <hkern u1="f" u2="X" k="-49" />
    <hkern u1="f" u2="W" k="-45" />
    <hkern u1="f" u2="V" k="-44" />
    <hkern u1="f" u2="T" k="-31" />
    <hkern u1="f" u2="S" k="3" />
    <hkern u1="f" u2="&#x2f;" k="23" />
    <hkern u1="f" u2="A" k="37" />
    <hkern u1="f" u2="J" k="55" />
    <hkern u1="f" u2="&#x20;" k="17" />
    <hkern g1="f_f" u2="&#x149;" k="-41" />
    <hkern g1="f_f" u2="&#x129;" k="-16" />
    <hkern g1="f_f" u2="&#x12b;" k="-26" />
    <hkern g1="f_f" u2="&#xef;" k="-3" />
    <hkern g1="f_f" u2="&#x12d;" k="-4" />
    <hkern g1="f_f_i" u2="&#x129;" k="-12" />
    <hkern g1="f_f_i" u2="&#x12b;" k="-2" />
    <hkern g1="f_f_i" u2="&#xef;" k="-6" />
    <hkern g1="f_i" u2="&#x129;" k="-12" />
    <hkern g1="f_i" u2="&#x12b;" k="-2" />
    <hkern g1="f_i" u2="&#xef;" k="-7" />
    <hkern u1="&#x35;" u2="&#xc6;" k="-9" />
    <hkern u1="&#x35;" u2="&#x2b;" k="12" />
    <hkern u1="&#x35;" u2="&#x2212;" k="4" />
    <hkern u1="&#x35;" u2="&#x2d;" k="5" />
    <hkern u1="&#x35;" u2="\" k="14" />
    <hkern u1="&#x35;" u2="Y" k="22" />
    <hkern u1="&#x35;" u2="W" k="15" />
    <hkern u1="&#x35;" u2="V" k="15" />
    <hkern u1="&#x35;" u2="T" k="20" />
    <hkern u1="&#x35;" u2="A" k="-10" />
    <hkern u1="&#x35;" u2="&#xb7;" k="5" />
    <hkern u1="&#x2044;" u2="&#x2084;" k="17" />
    <hkern u1="&#x2044;" u2="&#x34;" k="28" />
    <hkern u1="&#x34;" u2="&#xc6;" k="-19" />
    <hkern u1="&#x34;" u2="&#x37;" k="5" />
    <hkern u1="&#x34;" u2="&#x27;" k="11" />
    <hkern u1="&#x34;" u2="&#x2b;" k="15" />
    <hkern u1="&#x34;" u2="&#x2212;" k="12" />
    <hkern u1="&#x34;" u2="&#x2d;" k="4" />
    <hkern u1="&#x34;" u2="&#xb0;" k="14" />
    <hkern u1="&#x34;" u2="&#x2c;" k="-7" />
    <hkern u1="&#x34;" u2="\" k="32" />
    <hkern u1="&#x34;" u2="Y" k="39" />
    <hkern u1="&#x34;" u2="X" k="-5" />
    <hkern u1="&#x34;" u2="W" k="32" />
    <hkern u1="&#x34;" u2="V" k="31" />
    <hkern u1="&#x34;" u2="T" k="35" />
    <hkern u1="&#x34;" u2="A" k="-8" />
    <hkern u1="&#x34;" u2="&#xb7;" k="13" />
    <hkern g1="four.lnum" u2="&#x27;" k="10" />
    <hkern g1="four.lnum" u2="&#xb0;" k="10" />
    <hkern g1="four.lnum" u2="\" k="7" />
    <hkern u1="g" u2="j" k="-33" />
    <hkern u1="g" u2="Z" k="7" />
    <hkern u1="g" u2="Y" k="58" />
    <hkern u1="g" u2="X" k="9" />
    <hkern u1="g" u2="W" k="44" />
    <hkern u1="g" u2="V" k="41" />
    <hkern u1="g" u2="U" k="9" />
    <hkern u1="g" u2="T" k="55" />
    <hkern u1="g" u2="S" k="3" />
    <hkern u1="g" u2="O" k="5" />
    <hkern u1="&#x11f;" u2="j" k="-33" />
    <hkern u1="&#x11d;" u2="j" k="-33" />
    <hkern u1="&#x123;" u2="j" k="-33" />
    <hkern u1="&#x121;" u2="j" k="-33" />
    <hkern u1="&#xdf;" u2="z" k="6" />
    <hkern u1="&#xdf;" g2="y.alt" k="18" />
    <hkern u1="&#xdf;" u2="y" k="17" />
    <hkern u1="&#xdf;" u2="x" k="16" />
    <hkern u1="&#xdf;" g2="w.alt" k="16" />
    <hkern u1="&#xdf;" u2="w" k="18" />
    <hkern u1="&#xdf;" u2="v" k="16" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="12" />
    <hkern u1="&#xdf;" g2="t.alt" k="10" />
    <hkern u1="&#xdf;" u2="t" k="9" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="13" />
    <hkern u1="&#xdf;" u2="s" k="3" />
    <hkern u1="&#xdf;" u2="&#x27;" k="10" />
    <hkern u1="&#xdf;" u2="&#x2019;" k="10" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="9" />
    <hkern u1="&#xdf;" u2="&#xaa;" k="3" />
    <hkern u1="&#xdf;" u2="&#x129;" k="-19" />
    <hkern u1="&#xdf;" u2="f" k="6" />
    <hkern u1="&#xdf;" u2="\" k="12" />
    <hkern u1="&#xdf;" u2="&#x2a;" k="9" />
    <hkern u1="&#xdf;" u2="Z" k="10" />
    <hkern u1="&#xdf;" u2="Y" k="44" />
    <hkern u1="&#xdf;" u2="X" k="25" />
    <hkern u1="&#xdf;" u2="W" k="35" />
    <hkern u1="&#xdf;" u2="V" k="35" />
    <hkern u1="&#xdf;" u2="U" k="13" />
    <hkern u1="&#xdf;" u2="T" k="27" />
    <hkern u1="&#xdf;" u2="S" k="7" />
    <hkern u1="&#xdf;" u2="O" k="6" />
    <hkern u1="&#xdf;" u2="A" k="12" />
    <hkern u1="&#xdf;" u2="J" k="4" />
    <hkern u1="&#xdf;" u2="I" k="9" />
    <hkern u1="&#xdf;" u2="&#xd0;" k="5" />
    <hkern u1="&#x2039;" u2="Y" k="38" />
    <hkern u1="&#x2039;" u2="W" k="23" />
    <hkern u1="&#x2039;" u2="V" k="22" />
    <hkern u1="&#x2039;" u2="&#x166;" k="4" />
    <hkern u1="&#x2039;" u2="T" k="24" />
    <hkern u1="&#x203a;" u2="z" k="10" />
    <hkern u1="&#x203a;" g2="y.alt" k="22" />
    <hkern u1="&#x203a;" u2="y" k="19" />
    <hkern u1="&#x203a;" u2="x" k="24" />
    <hkern u1="&#x203a;" g2="w.alt" k="17" />
    <hkern u1="&#x203a;" u2="w" k="24" />
    <hkern u1="&#x203a;" u2="v" k="19" />
    <hkern u1="&#x203a;" g2="t.alt" k="12" />
    <hkern u1="&#x203a;" u2="t" k="4" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="19" />
    <hkern u1="&#x203a;" u2="s" k="4" />
    <hkern u1="&#x203a;" u2="&#x27;" k="13" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="22" />
    <hkern u1="&#x203a;" u2="f" k="4" />
    <hkern u1="&#x203a;" u2="Z" k="5" />
    <hkern u1="&#x203a;" u2="Y" k="58" />
    <hkern u1="&#x203a;" u2="X" k="16" />
    <hkern u1="&#x203a;" u2="W" k="40" />
    <hkern u1="&#x203a;" u2="V" k="40" />
    <hkern u1="&#x203a;" u2="&#x166;" k="16" />
    <hkern u1="&#x203a;" u2="T" k="52" />
    <hkern u1="&#x203a;" u2="A" k="12" />
    <hkern u1="&#x203a;" u2="&#x141;" k="3" />
    <hkern u1="&#x203a;" u2="J" k="12" />
    <hkern u1="&#x203a;" u2="&#xd0;" k="3" />
    <hkern u1="&#x203a;" u2="&#x110;" k="3" />
    <hkern u1="h" g2="y.alt" k="10" />
    <hkern u1="h" u2="y" k="8" />
    <hkern u1="h" g2="w.alt" k="7" />
    <hkern u1="h" u2="w" k="10" />
    <hkern u1="h" u2="v" k="8" />
    <hkern u1="h" u2="&#x2122;" k="27" />
    <hkern u1="h" g2="t.alt" k="4" />
    <hkern u1="h" u2="t" k="2" />
    <hkern u1="h" u2="&#x27;" k="40" />
    <hkern u1="h" u2="&#x2019;" k="31" />
    <hkern u1="h" u2="&#x2018;" k="33" />
    <hkern u1="h" u2="&#x3f;" k="5" />
    <hkern u1="h" u2="&#xba;" k="4" />
    <hkern u1="h" u2="\" k="33" />
    <hkern u1="h" u2="&#x2a;" k="25" />
    <hkern u1="h" u2="&#x2bc;" k="81" />
    <hkern u1="h" u2="Z" k="8" />
    <hkern u1="h" u2="Y" k="70" />
    <hkern u1="h" u2="X" k="7" />
    <hkern u1="h" u2="W" k="53" />
    <hkern u1="h" u2="V" k="52" />
    <hkern u1="h" u2="U" k="12" />
    <hkern u1="h" u2="T" k="81" />
    <hkern u1="h" u2="S" k="5" />
    <hkern u1="h" u2="O" k="5" />
    <hkern u1="&#x2d;" u2="z" k="14" />
    <hkern u1="&#x2d;" g2="y.alt" k="18" />
    <hkern u1="&#x2d;" u2="y" k="16" />
    <hkern u1="&#x2d;" u2="x" k="24" />
    <hkern u1="&#x2d;" g2="w.alt" k="13" />
    <hkern u1="&#x2d;" u2="w" k="19" />
    <hkern u1="&#x2d;" u2="v" k="15" />
    <hkern u1="&#x2d;" g2="two.lnum" k="4" />
    <hkern u1="&#x2d;" u2="&#x32;" k="3" />
    <hkern u1="&#x2d;" u2="&#x33;" k="33" />
    <hkern u1="&#x2d;" g2="t.alt" k="3" />
    <hkern u1="&#x2d;" u2="t" k="3" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="16" />
    <hkern u1="&#x2d;" g2="seven.lnum" k="27" />
    <hkern u1="&#x2d;" u2="&#x37;" k="14" />
    <hkern u1="&#x2d;" u2="s" k="3" />
    <hkern u1="&#x2d;" g2="one.lnum" k="9" />
    <hkern u1="&#x2d;" u2="&#x31;" k="10" />
    <hkern u1="&#x2d;" u2="&#x34;" k="8" />
    <hkern u1="&#x2d;" u2="f" k="3" />
    <hkern u1="&#x2d;" u2="Z" k="7" />
    <hkern u1="&#x2d;" u2="Y" k="58" />
    <hkern u1="&#x2d;" u2="X" k="25" />
    <hkern u1="&#x2d;" u2="W" k="39" />
    <hkern u1="&#x2d;" u2="V" k="37" />
    <hkern u1="&#x2d;" u2="&#x166;" k="5" />
    <hkern u1="&#x2d;" u2="T" k="62" />
    <hkern u1="&#x2d;" u2="A" k="12" />
    <hkern u1="&#x2d;" u2="J" k="27" />
    <hkern u1="i" u2="&#x135;" k="-12" />
    <hkern u1="i" u2="&#x129;" k="-36" />
    <hkern u1="i" u2="&#xef;" k="-20" />
    <hkern u1="i" u2="&#xee;" k="-12" />
    <hkern u1="i" u2="&#x2bc;" k="23" />
    <hkern u1="i" u2="Z" k="6" />
    <hkern u1="i" u2="U" k="6" />
    <hkern u1="i" u2="S" k="3" />
    <hkern u1="i" u2="O" k="4" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-5" />
    <hkern u1="&#xed;" u2="&#x2019;" k="-5" />
    <hkern u1="&#xed;" u2="&#x2018;" k="-5" />
    <hkern u1="&#xed;" u2="&#x29;" k="-17" />
    <hkern u1="&#xed;" u2="\" k="-6" />
    <hkern u1="&#xed;" u2="&#x2a;" k="-6" />
    <hkern u1="&#x12d;" u2="&#x2122;" k="-14" />
    <hkern u1="&#x12d;" u2="&#x2019;" k="-7" />
    <hkern u1="&#x12d;" u2="&#x2018;" k="-7" />
    <hkern u1="&#x12d;" u2="&#x29;" k="-20" />
    <hkern u1="&#x12d;" u2="]" k="-5" />
    <hkern u1="&#x12d;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x12d;" u2="\" k="-15" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-5" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-33" />
    <hkern u1="&#xee;" g2="t.alt" k="-13" />
    <hkern u1="&#xee;" u2="&#x27;" k="-10" />
    <hkern u1="&#xee;" u2="&#x2019;" k="-21" />
    <hkern u1="&#xee;" u2="&#x2018;" k="-21" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-17" />
    <hkern u1="&#xee;" u2="&#x29;" k="-9" />
    <hkern u1="&#xee;" u2="&#xba;" k="-17" />
    <hkern u1="&#xee;" u2="&#xaa;" k="-6" />
    <hkern u1="&#xee;" g2="l.alt" k="-12" />
    <hkern u1="&#xee;" u2="l" k="-13" />
    <hkern u1="&#xee;" u2="i" k="-12" />
    <hkern u1="&#xee;" u2="h" k="-12" />
    <hkern u1="&#xee;" u2="&#xdf;" k="-5" />
    <hkern u1="&#xee;" u2="]" k="-20" />
    <hkern u1="&#xee;" u2="&#x7d;" k="-20" />
    <hkern u1="&#xee;" u2="&#x7c;" k="-5" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-41" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-37" />
    <hkern u1="&#xef;" g2="t.alt" k="-45" />
    <hkern u1="&#xef;" u2="&#x27;" k="-19" />
    <hkern u1="&#xef;" u2="&#x2019;" k="-30" />
    <hkern u1="&#xef;" u2="&#x2018;" k="-30" />
    <hkern u1="&#xef;" u2="&#x3f;" k="-31" />
    <hkern u1="&#xef;" u2="&#x29;" k="-43" />
    <hkern u1="&#xef;" u2="&#xba;" k="-30" />
    <hkern u1="&#xef;" u2="&#xaa;" k="-22" />
    <hkern u1="&#xef;" g2="l.alt" k="-20" />
    <hkern u1="&#xef;" u2="l" k="-22" />
    <hkern u1="&#xef;" u2="i" k="-20" />
    <hkern u1="&#xef;" u2="h" k="-20" />
    <hkern u1="&#xef;" u2="&#xdf;" k="-13" />
    <hkern u1="&#xef;" u2="&#x21;" k="-8" />
    <hkern u1="&#xef;" u2="]" k="-28" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-28" />
    <hkern u1="&#xef;" u2="&#x7c;" k="-14" />
    <hkern u1="&#xef;" u2="\" k="-39" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-49" />
    <hkern u1="&#x133;" u2="j" k="-36" />
    <hkern u1="j" u2="&#x135;" k="-36" />
    <hkern u1="j" u2="j" k="-36" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x12b;" u2="&#x27;" k="-7" />
    <hkern u1="&#x12b;" u2="&#x2019;" k="-9" />
    <hkern u1="&#x12b;" u2="&#x2018;" k="-9" />
    <hkern u1="&#x12b;" u2="&#x22;" k="-4" />
    <hkern u1="&#x12b;" u2="&#x29;" k="-29" />
    <hkern u1="&#x12b;" u2="]" k="-5" />
    <hkern u1="&#x12b;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x12b;" u2="\" k="-30" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-26" />
    <hkern u1="&#x12f;" u2="j" k="-58" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-61" />
    <hkern u1="&#x129;" u2="&#xfe;" k="-27" />
    <hkern u1="&#x129;" g2="t.alt" k="-13" />
    <hkern u1="&#x129;" u2="&#x27;" k="-34" />
    <hkern u1="&#x129;" u2="&#x2019;" k="-46" />
    <hkern u1="&#x129;" u2="&#x2018;" k="-44" />
    <hkern u1="&#x129;" u2="&#x22;" k="-3" />
    <hkern u1="&#x129;" u2="&#x3f;" k="-8" />
    <hkern u1="&#x129;" u2="&#x29;" k="-63" />
    <hkern u1="&#x129;" u2="&#xba;" k="-10" />
    <hkern u1="&#x129;" u2="&#xaa;" k="-22" />
    <hkern u1="&#x129;" g2="l.alt" k="-30" />
    <hkern u1="&#x129;" u2="l" k="-32" />
    <hkern u1="&#x129;" u2="i" k="-30" />
    <hkern u1="&#x129;" u2="h" k="-30" />
    <hkern u1="&#x129;" u2="&#x21;" k="-16" />
    <hkern u1="&#x129;" u2="]" k="-48" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-49" />
    <hkern u1="&#x129;" u2="&#x7c;" k="-23" />
    <hkern u1="&#x129;" u2="\" k="-61" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-44" />
    <hkern g1="j.alt" u2="j" k="-89" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x135;" g2="t.alt" k="-13" />
    <hkern u1="&#x135;" u2="&#x27;" k="-10" />
    <hkern u1="&#x135;" u2="&#x2019;" k="-21" />
    <hkern u1="&#x135;" u2="&#x2018;" k="-21" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-17" />
    <hkern u1="&#x135;" u2="&#x29;" k="-9" />
    <hkern u1="&#x135;" u2="&#xba;" k="-17" />
    <hkern u1="&#x135;" u2="&#xaa;" k="-6" />
    <hkern u1="&#x135;" g2="l.alt" k="-12" />
    <hkern u1="&#x135;" u2="l" k="-13" />
    <hkern u1="&#x135;" u2="j" k="-36" />
    <hkern u1="&#x135;" u2="i" k="-12" />
    <hkern u1="&#x135;" u2="h" k="-12" />
    <hkern u1="&#x135;" u2="&#xdf;" k="-5" />
    <hkern u1="&#x135;" u2="]" k="-20" />
    <hkern u1="&#x135;" u2="&#x7d;" k="-20" />
    <hkern u1="&#x135;" u2="&#x7c;" k="-5" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-41" />
    <hkern u1="k" u2="&#x259;" k="22" />
    <hkern u1="k" u2="s" k="5" />
    <hkern u1="k" u2="&#x27;" k="19" />
    <hkern u1="k" u2="&#x2019;" k="4" />
    <hkern u1="k" u2="&#x2018;" k="5" />
    <hkern u1="k" u2="&#x201a;" k="-7" />
    <hkern u1="k" u2="&#x201e;" k="-7" />
    <hkern u1="k" u2="&#x29;" k="-15" />
    <hkern u1="k" u2="&#x1ff;" k="13" />
    <hkern u1="k" u2="&#xf8;" k="13" />
    <hkern u1="k" u2="o" k="25" />
    <hkern u1="k" u2="&#x2d;" k="27" />
    <hkern u1="k" u2="&#x2039;" k="24" />
    <hkern u1="k" u2="f" k="3" />
    <hkern u1="k" u2="&#xf0;" k="29" />
    <hkern u1="k" u2="d" k="24" />
    <hkern u1="k" u2="&#x2c;" k="-7" />
    <hkern u1="k" u2="]" k="-4" />
    <hkern u1="k" u2="&#x7d;" k="-3" />
    <hkern u1="k" u2="&#x2a;" k="-13" />
    <hkern u1="k" u2="&#x2bc;" k="48" />
    <hkern u1="k" u2="a" k="10" />
    <hkern u1="k" u2="Y" k="34" />
    <hkern u1="k" u2="W" k="17" />
    <hkern u1="k" u2="V" k="16" />
    <hkern u1="k" u2="U" k="5" />
    <hkern u1="k" u2="T" k="47" />
    <hkern u1="k" u2="&#x2f;" k="-13" />
    <hkern u1="k" u2="O" k="15" />
    <hkern u1="l" g2="y.alt" k="28" />
    <hkern u1="l" u2="y" k="26" />
    <hkern u1="l" g2="w.alt" k="25" />
    <hkern u1="l" u2="w" k="31" />
    <hkern u1="l" u2="v" k="25" />
    <hkern u1="l" u2="&#x2122;" k="22" />
    <hkern u1="l" g2="t.alt" k="12" />
    <hkern u1="l" u2="t" k="13" />
    <hkern u1="l" u2="&#xc6;" k="-3" />
    <hkern u1="l" u2="&#x259;" k="3" />
    <hkern u1="l" u2="&#xae;" k="3" />
    <hkern u1="l" u2="&#x27;" k="22" />
    <hkern u1="l" u2="&#x2019;" k="22" />
    <hkern u1="l" u2="&#x2018;" k="22" />
    <hkern u1="l" u2="&#x29;" k="-8" />
    <hkern u1="l" u2="&#xba;" k="21" />
    <hkern u1="l" u2="&#xaa;" k="21" />
    <hkern u1="l" u2="o" k="4" />
    <hkern u1="l" u2="&#x2d;" k="7" />
    <hkern u1="l" u2="&#x2039;" k="7" />
    <hkern u1="l" u2="f" k="10" />
    <hkern u1="l" u2="&#xf0;" k="3" />
    <hkern u1="l" u2="d" k="4" />
    <hkern u1="l" u2="&#xa9;" k="3" />
    <hkern u1="l" u2="\" k="17" />
    <hkern u1="l" u2="&#x2a;" k="23" />
    <hkern u1="l" u2="&#x2bc;" k="52" />
    <hkern u1="l" u2="Y" k="45" />
    <hkern u1="l" u2="W" k="42" />
    <hkern u1="l" u2="V" k="41" />
    <hkern u1="l" u2="U" k="26" />
    <hkern u1="l" u2="T" k="36" />
    <hkern u1="l" u2="&#x2f;" k="-5" />
    <hkern u1="l" u2="O" k="21" />
    <hkern u1="l" u2="&#xb7;" k="68" />
    <hkern u1="l" u2="I" k="5" />
    <hkern u1="l" u2="&#x20;" k="24" />
    <hkern g1="l.alt" u2="&#x135;" k="-12" />
    <hkern g1="l.alt" u2="&#x129;" k="-36" />
    <hkern g1="l.alt" u2="&#xef;" k="-20" />
    <hkern g1="l.alt" u2="&#xee;" k="-12" />
    <hkern g1="l.alt" u2="&#x2bc;" k="22" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-46" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-85" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-63" />
    <hkern u1="&#x13e;" g2="t.alt" k="-44" />
    <hkern u1="&#x13e;" u2="t" k="-32" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-34" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-69" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-80" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-78" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-93" />
    <hkern u1="&#x13e;" u2="&#xba;" k="-30" />
    <hkern u1="&#x13e;" u2="&#xaa;" k="-49" />
    <hkern u1="&#x13e;" g2="l.alt" k="-70" />
    <hkern u1="&#x13e;" u2="l" k="-72" />
    <hkern u1="&#x13e;" u2="i" k="-70" />
    <hkern u1="&#x13e;" u2="h" k="-70" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-16" />
    <hkern u1="&#x13e;" g2="f_l" k="-2" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-54" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-27" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-10" />
    <hkern u1="&#x13e;" u2="]" k="-83" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-83" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-64" />
    <hkern u1="&#x13e;" u2="\" k="-86" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-61" />
    <hkern u1="&#x13e;" u2="&#x26;" k="-10" />
    <hkern u1="&#x13e;" u2="&#x20;" k="-7" />
    <hkern u1="&#x13c;" u2="j" k="-67" />
    <hkern u1="&#x142;" u2="z" k="-2" />
    <hkern u1="&#x142;" g2="y.alt" k="5" />
    <hkern u1="&#x142;" u2="y" k="2" />
    <hkern u1="&#x142;" u2="x" k="-21" />
    <hkern u1="&#x142;" g2="w.alt" k="7" />
    <hkern u1="&#x142;" u2="w" k="7" />
    <hkern u1="&#x142;" u2="v" k="7" />
    <hkern u1="&#x142;" g2="t.alt" k="-6" />
    <hkern u1="&#x142;" u2="s" k="-5" />
    <hkern u1="&#x142;" u2="&#xaa;" k="9" />
    <hkern u1="&#x142;" u2="f" k="2" />
    <hkern u1="&#x142;" u2="&#x2a;" k="8" />
    <hkern u1="&#x2212;" g2="two.lnum" k="7" />
    <hkern u1="&#x2212;" g2="three.lnum" k="10" />
    <hkern u1="&#x2212;" u2="&#x33;" k="15" />
    <hkern u1="&#x2212;" g2="seven.lnum" k="20" />
    <hkern u1="&#x2212;" u2="&#x37;" k="12" />
    <hkern u1="&#x2212;" g2="one.lnum" k="8" />
    <hkern u1="&#x2212;" u2="&#x31;" k="9" />
    <hkern u1="&#x2212;" u2="&#x34;" k="18" />
    <hkern u1="n" g2="y.alt" k="10" />
    <hkern u1="n" u2="y" k="8" />
    <hkern u1="n" g2="w.alt" k="7" />
    <hkern u1="n" u2="w" k="10" />
    <hkern u1="n" u2="v" k="8" />
    <hkern u1="n" u2="&#x2122;" k="22" />
    <hkern u1="n" g2="t.alt" k="4" />
    <hkern u1="n" u2="t" k="2" />
    <hkern u1="n" u2="&#x27;" k="10" />
    <hkern u1="n" u2="&#x2019;" k="14" />
    <hkern u1="n" u2="&#x2018;" k="15" />
    <hkern u1="n" u2="&#x3f;" k="4" />
    <hkern u1="n" u2="\" k="33" />
    <hkern u1="n" u2="&#x2a;" k="15" />
    <hkern u1="n" u2="&#x2bc;" k="46" />
    <hkern u1="n" u2="Z" k="8" />
    <hkern u1="n" u2="Y" k="68" />
    <hkern u1="n" u2="X" k="7" />
    <hkern u1="n" u2="W" k="53" />
    <hkern u1="n" u2="V" k="52" />
    <hkern u1="n" u2="U" k="12" />
    <hkern u1="n" u2="T" k="81" />
    <hkern u1="n" u2="S" k="5" />
    <hkern u1="n" u2="O" k="5" />
    <hkern u1="&#x39;" u2="&#x37;" k="6" />
    <hkern u1="&#x39;" u2="&#x27;" k="19" />
    <hkern u1="&#x39;" u2="&#xb0;" k="26" />
    <hkern u1="&#x39;" u2="\" k="35" />
    <hkern u1="&#x39;" u2="Y" k="44" />
    <hkern u1="&#x39;" u2="W" k="31" />
    <hkern u1="&#x39;" u2="V" k="31" />
    <hkern u1="&#x39;" u2="T" k="40" />
    <hkern g1="nine.lnum" g2="seven.lnum" k="4" />
    <hkern g1="nine.lnum" u2="&#x2e;" k="5" />
    <hkern g1="nine.lnum" u2="&#x29;" k="7" />
    <hkern g1="nine.lnum" u2="\" k="7" />
    <hkern g1="nine.lnum" u2="&#x2f;" k="16" />
    <hkern u1="&#x23;" u2="&#x34;" k="4" />
    <hkern u1="o" u2="z" k="10" />
    <hkern u1="o" g2="y.alt" k="19" />
    <hkern u1="o" u2="y" k="17" />
    <hkern u1="o" u2="x" k="21" />
    <hkern u1="o" g2="w.alt" k="15" />
    <hkern u1="o" u2="w" k="20" />
    <hkern u1="o" u2="v" k="16" />
    <hkern u1="o" u2="&#x2122;" k="28" />
    <hkern u1="o" g2="t.alt" k="10" />
    <hkern u1="o" u2="t" k="8" />
    <hkern u1="o" u2="&#xc6;" k="23" />
    <hkern u1="o" u2="s" k="3" />
    <hkern u1="o" u2="&#x27;" k="22" />
    <hkern u1="o" u2="&#x2019;" k="24" />
    <hkern u1="o" u2="&#x2018;" k="28" />
    <hkern u1="o" u2="&#x3f;" k="6" />
    <hkern u1="o" u2="&#x29;" k="17" />
    <hkern u1="o" u2="&#xba;" k="4" />
    <hkern u1="o" u2="&#x142;" k="-5" />
    <hkern u1="o" u2="f" k="4" />
    <hkern u1="o" u2="\" k="38" />
    <hkern u1="o" u2="&#x2a;" k="27" />
    <hkern u1="o" u2="&#x2bc;" k="61" />
    <hkern u1="o" u2="Z" k="18" />
    <hkern u1="o" u2="Y" k="81" />
    <hkern u1="o" u2="X" k="39" />
    <hkern u1="o" u2="W" k="63" />
    <hkern u1="o" u2="V" k="61" />
    <hkern u1="o" u2="U" k="11" />
    <hkern u1="o" u2="T" k="90" />
    <hkern u1="o" u2="S" k="11" />
    <hkern u1="o" u2="A" k="17" />
    <hkern u1="o" u2="J" k="15" />
    <hkern u1="o" u2="I" k="9" />
    <hkern u1="o" u2="&#xd0;" k="5" />
    <hkern u1="&#x31;" u2="&#xc6;" k="-22" />
    <hkern u1="&#x31;" u2="&#x37;" k="4" />
    <hkern u1="&#x31;" u2="&#x27;" k="20" />
    <hkern u1="&#x31;" u2="&#x2b;" k="18" />
    <hkern u1="&#x31;" u2="&#x23;" k="-6" />
    <hkern u1="&#x31;" u2="&#x2212;" k="18" />
    <hkern u1="&#x31;" u2="&#x2d;" k="7" />
    <hkern u1="&#x31;" u2="&#x3d;" k="4" />
    <hkern u1="&#x31;" u2="&#xb0;" k="22" />
    <hkern u1="&#x31;" u2="\" k="40" />
    <hkern u1="&#x31;" u2="Y" k="46" />
    <hkern u1="&#x31;" u2="X" k="-8" />
    <hkern u1="&#x31;" u2="W" k="39" />
    <hkern u1="&#x31;" u2="V" k="38" />
    <hkern u1="&#x31;" u2="U" k="4" />
    <hkern u1="&#x31;" u2="T" k="42" />
    <hkern u1="&#x31;" u2="A" k="-11" />
    <hkern u1="&#x31;" u2="&#xb7;" k="14" />
    <hkern g1="one.lnum" g2="zero.lnum" k="3" />
    <hkern g1="one.lnum" u2="&#x27;" k="20" />
    <hkern g1="one.lnum" u2="&#x2b;" k="7" />
    <hkern g1="one.lnum" u2="&#x23;" k="-8" />
    <hkern g1="one.lnum" u2="&#x2212;" k="3" />
    <hkern g1="one.lnum" u2="&#x2d;" k="5" />
    <hkern g1="one.lnum" g2="four.lnum" k="4" />
    <hkern g1="one.lnum" u2="&#xb0;" k="20" />
    <hkern g1="one.lnum" u2="\" k="18" />
    <hkern g1="one.lnum" u2="&#xb7;" k="7" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="5" />
    <hkern u1="&#x1ff;" u2="&#x2a;" k="5" />
    <hkern u1="p" u2="&#x142;" k="-6" />
    <hkern u1="&#x28;" g2="zero.lnum" k="14" />
    <hkern u1="&#x28;" u2="&#x30;" k="15" />
    <hkern u1="&#x28;" g2="y.alt" k="13" />
    <hkern u1="&#x28;" u2="y" k="5" />
    <hkern u1="&#x28;" u2="x" k="-10" />
    <hkern u1="&#x28;" g2="w.alt" k="13" />
    <hkern u1="&#x28;" u2="w" k="14" />
    <hkern u1="&#x28;" u2="v" k="13" />
    <hkern u1="&#x28;" u2="u" k="4" />
    <hkern u1="&#x28;" u2="&#xc6;" k="-25" />
    <hkern u1="&#x28;" g2="six.lnum" k="7" />
    <hkern u1="&#x28;" u2="&#x36;" k="7" />
    <hkern u1="&#x28;" u2="o" k="16" />
    <hkern u1="&#x28;" u2="&#x135;" k="-20" />
    <hkern u1="&#x28;" u2="&#x129;" k="-25" />
    <hkern u1="&#x28;" u2="&#x12b;" k="-28" />
    <hkern u1="&#x28;" u2="j" k="-20" />
    <hkern u1="&#x28;" u2="&#xec;" k="-17" />
    <hkern u1="&#x28;" u2="&#xef;" k="-43" />
    <hkern u1="&#x28;" u2="&#xee;" k="-9" />
    <hkern u1="&#x28;" u2="&#x12d;" k="-20" />
    <hkern u1="&#x28;" g2="four.lnum" k="5" />
    <hkern u1="&#x28;" u2="f" k="7" />
    <hkern u1="&#x28;" u2="&#xf0;" k="14" />
    <hkern u1="&#x28;" u2="d" k="17" />
    <hkern u1="&#x28;" u2="Y" k="-19" />
    <hkern u1="&#x28;" u2="X" k="-18" />
    <hkern u1="&#x28;" u2="W" k="-17" />
    <hkern u1="&#x28;" u2="V" k="-17" />
    <hkern u1="&#x28;" u2="T" k="-4" />
    <hkern u1="&#x28;" u2="&#x1fe;" k="-8" />
    <hkern u1="&#x28;" u2="&#xd8;" k="-9" />
    <hkern u1="&#x28;" u2="O" k="15" />
    <hkern u1="&#x28;" u2="A" k="-15" />
    <hkern u1="&#x28;" u2="&#x128;" k="-23" />
    <hkern u1="&#x28;" u2="J" k="-6" />
    <hkern u1="&#x2e;" g2="zero.lnum" k="6" />
    <hkern u1="&#x2e;" g2="y.alt" k="41" />
    <hkern u1="&#x2e;" u2="y" k="38" />
    <hkern u1="&#x2e;" g2="w.alt" k="31" />
    <hkern u1="&#x2e;" u2="w" k="42" />
    <hkern u1="&#x2e;" u2="v" k="36" />
    <hkern u1="&#x2e;" g2="t.alt" k="7" />
    <hkern u1="&#x2e;" u2="t" k="7" />
    <hkern u1="&#x2e;" u2="&#xc6;" k="-3" />
    <hkern u1="&#x2e;" u2="&#x37;" k="25" />
    <hkern u1="&#x2e;" u2="&#x27;" k="119" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="123" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="128" />
    <hkern u1="&#x2e;" u2="f" k="5" />
    <hkern u1="&#x2e;" u2="d" k="3" />
    <hkern u1="&#x2e;" u2="Y" k="72" />
    <hkern u1="&#x2e;" u2="W" k="66" />
    <hkern u1="&#x2e;" u2="V" k="64" />
    <hkern u1="&#x2e;" u2="U" k="14" />
    <hkern u1="&#x2e;" u2="T" k="63" />
    <hkern u1="&#x2e;" u2="O" k="9" />
    <hkern u1="&#x2b;" g2="two.lnum" k="15" />
    <hkern u1="&#x2b;" u2="&#x32;" k="4" />
    <hkern u1="&#x2b;" g2="three.lnum" k="17" />
    <hkern u1="&#x2b;" u2="&#x33;" k="8" />
    <hkern u1="&#x2b;" g2="seven.lnum" k="21" />
    <hkern u1="&#x2b;" u2="&#x37;" k="9" />
    <hkern u1="&#x2b;" g2="one.lnum" k="9" />
    <hkern u1="&#x2b;" u2="&#x31;" k="4" />
    <hkern u1="&#x2b;" u2="&#x34;" k="24" />
    <hkern u1="&#x2b;" g2="eight.lnum" k="3" />
    <hkern u1="&#x2b;" u2="&#x38;" k="3" />
    <hkern u1="q" u2="&#x3b;" k="-5" />
    <hkern u1="q" u2="&#x201a;" k="-10" />
    <hkern u1="q" u2="&#x201e;" k="-10" />
    <hkern u1="q" u2="j" k="-120" />
    <hkern u1="q" u2="&#x2c;" k="-10" />
    <hkern u1="q" u2="Z" k="7" />
    <hkern u1="q" u2="Y" k="58" />
    <hkern u1="q" u2="X" k="9" />
    <hkern u1="q" u2="W" k="44" />
    <hkern u1="q" u2="V" k="41" />
    <hkern u1="q" u2="U" k="9" />
    <hkern u1="q" u2="T" k="55" />
    <hkern u1="q" u2="S" k="3" />
    <hkern u1="q" u2="O" k="5" />
    <hkern g1="q.alt" u2="j" k="-89" />
    <hkern u1="&#xbf;" u2="z" k="8" />
    <hkern u1="&#xbf;" g2="y.alt" k="19" />
    <hkern u1="&#xbf;" u2="y" k="11" />
    <hkern u1="&#xbf;" u2="x" k="7" />
    <hkern u1="&#xbf;" g2="w.alt" k="20" />
    <hkern u1="&#xbf;" u2="w" k="23" />
    <hkern u1="&#xbf;" u2="v" k="20" />
    <hkern u1="&#xbf;" u2="u" k="9" />
    <hkern u1="&#xbf;" g2="t.alt" k="9" />
    <hkern u1="&#xbf;" u2="t" k="9" />
    <hkern u1="&#xbf;" u2="&#xc6;" k="8" />
    <hkern u1="&#xbf;" u2="s" k="11" />
    <hkern u1="&#xbf;" u2="o" k="10" />
    <hkern u1="&#xbf;" u2="n" k="8" />
    <hkern u1="&#xbf;" g2="l.alt" k="8" />
    <hkern u1="&#xbf;" u2="l" k="9" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-82" />
    <hkern u1="&#xbf;" u2="&#x12f;" k="2" />
    <hkern u1="&#xbf;" u2="j" k="-82" />
    <hkern u1="&#xbf;" u2="i" k="8" />
    <hkern u1="&#xbf;" u2="h" k="8" />
    <hkern u1="&#xbf;" u2="f" k="9" />
    <hkern u1="&#xbf;" u2="&#xf0;" k="11" />
    <hkern u1="&#xbf;" u2="d" k="10" />
    <hkern u1="&#xbf;" u2="a" k="11" />
    <hkern u1="&#xbf;" u2="Z" k="9" />
    <hkern u1="&#xbf;" u2="Y" k="48" />
    <hkern u1="&#xbf;" u2="X" k="8" />
    <hkern u1="&#xbf;" u2="W" k="38" />
    <hkern u1="&#xbf;" u2="V" k="38" />
    <hkern u1="&#xbf;" u2="U" k="9" />
    <hkern u1="&#xbf;" u2="&#x166;" k="16" />
    <hkern u1="&#xbf;" u2="T" k="41" />
    <hkern u1="&#xbf;" u2="S" k="9" />
    <hkern u1="&#xbf;" u2="O" k="9" />
    <hkern u1="&#xbf;" u2="A" k="9" />
    <hkern u1="&#xbf;" u2="&#x141;" k="9" />
    <hkern u1="&#xbf;" u2="J" k="21" />
    <hkern u1="&#xbf;" u2="I" k="6" />
    <hkern u1="&#xbf;" u2="&#x126;" k="8" />
    <hkern u1="&#xbf;" u2="&#xd0;" k="9" />
    <hkern u1="&#xbf;" u2="&#x110;" k="9" />
    <hkern u1="&#x22;" u2="&#x129;" k="-2" />
    <hkern u1="&#x22;" u2="&#x12b;" k="-4" />
    <hkern u1="&#x22;" u2="&#x20;" k="14" />
    <hkern u1="&#x201e;" u2="&#x135;" k="-23" />
    <hkern u1="&#x201e;" u2="j" k="-23" />
    <hkern u1="&#x201e;" u2="&#x166;" k="26" />
    <hkern u1="&#x201a;" u2="&#x135;" k="-23" />
    <hkern u1="&#x201a;" u2="j" k="-23" />
    <hkern u1="&#x201a;" u2="&#x166;" k="26" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="86" />
    <hkern u1="&#x2018;" u2="s" k="4" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="117" />
    <hkern u1="&#x2018;" u2="o" k="16" />
    <hkern u1="&#x2018;" u2="&#x135;" k="-10" />
    <hkern u1="&#x2018;" u2="&#x129;" k="-40" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-24" />
    <hkern u1="&#x2018;" u2="&#xee;" k="-10" />
    <hkern u1="&#x2018;" u2="f" k="3" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="37" />
    <hkern u1="&#x2018;" u2="d" k="34" />
    <hkern u1="&#x2018;" u2="a" k="5" />
    <hkern u1="&#x2018;" u2="O" k="3" />
    <hkern u1="&#x2018;" u2="A" k="53" />
    <hkern u1="&#x2018;" u2="J" k="81" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="87" />
    <hkern u1="&#x2019;" u2="s" k="4" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="118" />
    <hkern u1="&#x2019;" u2="o" k="17" />
    <hkern u1="&#x2019;" u2="&#x135;" k="-11" />
    <hkern u1="&#x2019;" u2="&#x129;" k="-37" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="-2" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-21" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-11" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="15" />
    <hkern u1="&#x2019;" u2="f" k="3" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="38" />
    <hkern u1="&#x2019;" u2="d" k="35" />
    <hkern u1="&#x2019;" u2="&#x40;" k="22" />
    <hkern u1="&#x2019;" u2="a" k="5" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="86" />
    <hkern u1="&#x2019;" u2="O" k="3" />
    <hkern u1="&#x2019;" u2="A" k="55" />
    <hkern u1="&#x2019;" u2="J" k="81" />
    <hkern u1="&#x2019;" u2="&#x20;" k="21" />
    <hkern u1="&#x27;" g2="zero.lnum" k="4" />
    <hkern u1="&#x27;" u2="&#x30;" k="5" />
    <hkern u1="&#x27;" u2="&#x33;" k="4" />
    <hkern u1="&#x27;" u2="&#xc6;" k="90" />
    <hkern u1="&#x27;" u2="s" k="11" />
    <hkern u1="&#x27;" u2="&#x2e;" k="119" />
    <hkern u1="&#x27;" u2="o" k="22" />
    <hkern u1="&#x27;" u2="&#x39;" k="13" />
    <hkern u1="&#x27;" u2="&#x135;" k="-10" />
    <hkern u1="&#x27;" u2="&#x129;" k="-40" />
    <hkern u1="&#x27;" u2="&#x12b;" k="-7" />
    <hkern u1="&#x27;" u2="&#xef;" k="-19" />
    <hkern u1="&#x27;" u2="&#xee;" k="-10" />
    <hkern u1="&#x27;" u2="&#x2039;" k="13" />
    <hkern u1="&#x27;" g2="four.lnum" k="48" />
    <hkern u1="&#x27;" u2="&#x34;" k="77" />
    <hkern u1="&#x27;" u2="&#x35;" k="16" />
    <hkern u1="&#x27;" u2="f" k="3" />
    <hkern u1="&#x27;" u2="&#xf0;" k="43" />
    <hkern u1="&#x27;" u2="d" k="47" />
    <hkern u1="&#x27;" u2="&#x40;" k="27" />
    <hkern u1="&#x27;" u2="a" k="4" />
    <hkern u1="&#x27;" u2="&#x2f;" k="92" />
    <hkern u1="&#x27;" u2="O" k="6" />
    <hkern u1="&#x27;" u2="A" k="59" />
    <hkern u1="&#x27;" u2="J" k="80" />
    <hkern u1="&#x27;" u2="&#x20;" k="23" />
    <hkern u1="r" u2="&#xc6;" k="55" />
    <hkern u1="r" u2="&#x259;" k="12" />
    <hkern u1="r" u2="s" k="2" />
    <hkern u1="r" u2="&#x2e;" k="46" />
    <hkern u1="r" u2="&#x29;" k="10" />
    <hkern u1="r" u2="o" k="14" />
    <hkern u1="r" u2="&#x142;" k="-12" />
    <hkern u1="r" u2="&#x2d;" k="17" />
    <hkern u1="r" u2="&#x203a;" k="7" />
    <hkern u1="r" u2="&#x2039;" k="25" />
    <hkern u1="r" u2="&#xf0;" k="23" />
    <hkern u1="r" u2="d" k="13" />
    <hkern u1="r" u2="&#x40;" k="5" />
    <hkern u1="r" u2="&#x2bc;" k="5" />
    <hkern u1="r" u2="a" k="8" />
    <hkern u1="r" u2="Z" k="20" />
    <hkern u1="r" u2="Y" k="29" />
    <hkern u1="r" u2="X" k="36" />
    <hkern u1="r" u2="W" k="13" />
    <hkern u1="r" u2="V" k="12" />
    <hkern u1="r" u2="T" k="45" />
    <hkern u1="r" u2="S" k="4" />
    <hkern u1="r" u2="&#x2f;" k="36" />
    <hkern u1="r" u2="A" k="45" />
    <hkern u1="r" u2="J" k="74" />
    <hkern u1="r" u2="I" k="4" />
    <hkern u1="r" u2="&#x20;" k="20" />
    <hkern u1="&#x155;" u2="&#x29;" k="-7" />
    <hkern u1="&#x159;" u2="&#x2122;" k="-19" />
    <hkern u1="&#x159;" u2="&#x2019;" k="-12" />
    <hkern u1="&#x159;" u2="&#x2018;" k="-12" />
    <hkern u1="&#x159;" u2="&#x29;" k="-15" />
    <hkern u1="&#x159;" u2="&#xaa;" k="-11" />
    <hkern u1="&#x159;" u2="l" k="-4" />
    <hkern u1="&#x159;" u2="]" k="-10" />
    <hkern u1="&#x159;" u2="&#x7d;" k="-10" />
    <hkern u1="&#x159;" u2="\" k="-12" />
    <hkern u1="&#x159;" u2="&#x2a;" k="-29" />
    <hkern u1="s" g2="y.alt" k="11" />
    <hkern u1="s" u2="y" k="12" />
    <hkern u1="s" u2="x" k="6" />
    <hkern u1="s" g2="w.alt" k="8" />
    <hkern u1="s" u2="w" k="12" />
    <hkern u1="s" u2="v" k="8" />
    <hkern u1="s" u2="&#x2122;" k="16" />
    <hkern u1="s" u2="t" k="3" />
    <hkern u1="s" u2="&#x2019;" k="3" />
    <hkern u1="s" u2="&#x2018;" k="3" />
    <hkern u1="s" u2="&#x2d;" k="4" />
    <hkern u1="s" u2="f" k="4" />
    <hkern u1="s" u2="\" k="26" />
    <hkern u1="s" u2="&#x2a;" k="4" />
    <hkern u1="s" u2="&#x2bc;" k="37" />
    <hkern u1="s" u2="Z" k="2" />
    <hkern u1="s" u2="Y" k="64" />
    <hkern u1="s" u2="X" k="9" />
    <hkern u1="s" u2="W" k="50" />
    <hkern u1="s" u2="V" k="47" />
    <hkern u1="s" u2="U" k="11" />
    <hkern u1="s" u2="T" k="71" />
    <hkern u1="s" u2="O" k="5" />
    <hkern u1="s" u2="A" k="9" />
    <hkern u1="s" u2="I" k="6" />
    <hkern u1="s" u2="&#xd0;" k="4" />
    <hkern u1="&#x3b;" u2="&#x135;" k="-19" />
    <hkern u1="&#x3b;" u2="j" k="-19" />
    <hkern u1="&#x3b;" u2="&#x166;" k="4" />
    <hkern u1="&#x37;" u2="&#xc6;" k="50" />
    <hkern u1="&#x37;" u2="&#x37;" k="-7" />
    <hkern u1="&#x37;" u2="&#x2b;" k="5" />
    <hkern u1="&#x37;" u2="&#x2e;" k="58" />
    <hkern u1="&#x37;" u2="&#x2212;" k="15" />
    <hkern u1="&#x37;" u2="&#x2d;" k="24" />
    <hkern u1="&#x37;" u2="&#x34;" k="45" />
    <hkern u1="&#x37;" u2="&#x35;" k="15" />
    <hkern u1="&#x37;" u2="&#x3d;" k="5" />
    <hkern u1="&#x37;" u2="&#xb0;" k="-3" />
    <hkern u1="&#x37;" u2="&#xa2;" k="16" />
    <hkern u1="&#x37;" u2="Z" k="5" />
    <hkern u1="&#x37;" u2="T" k="-5" />
    <hkern u1="&#x37;" u2="&#x2f;" k="50" />
    <hkern u1="&#x37;" u2="A" k="36" />
    <hkern u1="&#x37;" u2="&#xb7;" k="17" />
    <hkern u1="&#x37;" u2="J" k="49" />
    <hkern g1="seven.lnum" g2="zero.lnum" k="13" />
    <hkern g1="seven.lnum" g2="three.lnum" k="-3" />
    <hkern g1="seven.lnum" g2="six.lnum" k="7" />
    <hkern g1="seven.lnum" g2="seven.lnum" k="-7" />
    <hkern g1="seven.lnum" u2="&#x2b;" k="25" />
    <hkern g1="seven.lnum" u2="&#x2e;" k="71" />
    <hkern g1="seven.lnum" u2="&#x29;" k="-17" />
    <hkern g1="seven.lnum" u2="&#x23;" k="19" />
    <hkern g1="seven.lnum" u2="&#x2212;" k="29" />
    <hkern g1="seven.lnum" u2="&#x2d;" k="39" />
    <hkern g1="seven.lnum" g2="four.lnum" k="37" />
    <hkern g1="seven.lnum" u2="&#x2044;" k="15" />
    <hkern g1="seven.lnum" g2="five.lnum" k="7" />
    <hkern g1="seven.lnum" u2="&#x3d;" k="24" />
    <hkern g1="seven.lnum" u2="&#xb0;" k="-3" />
    <hkern g1="seven.lnum" u2="&#xa2;" k="32" />
    <hkern g1="seven.lnum" u2="]" k="-4" />
    <hkern g1="seven.lnum" u2="&#x7d;" k="-3" />
    <hkern g1="seven.lnum" u2="\" k="-17" />
    <hkern g1="seven.lnum" u2="&#x2f;" k="73" />
    <hkern g1="seven.lnum" u2="&#xb7;" k="31" />
    <hkern u1="&#x2077;" u2="&#x2044;" k="44" />
    <hkern u1="&#x36;" u2="&#x33;" k="8" />
    <hkern u1="&#x36;" u2="&#xc6;" k="17" />
    <hkern u1="&#x36;" u2="Y" k="19" />
    <hkern u1="&#x36;" u2="W" k="14" />
    <hkern u1="&#x36;" u2="V" k="14" />
    <hkern u1="&#x36;" u2="T" k="4" />
    <hkern u1="&#x36;" u2="A" k="14" />
    <hkern u1="&#xa3;" g2="four.lnum" k="7" />
    <hkern u1="t" g2="y.alt" k="3" />
    <hkern u1="t" u2="y" k="3" />
    <hkern u1="t" g2="w.alt" k="3" />
    <hkern u1="t" u2="w" k="3" />
    <hkern u1="t" u2="v" k="3" />
    <hkern u1="t" u2="&#x2122;" k="10" />
    <hkern u1="t" g2="t.alt" k="3" />
    <hkern u1="t" u2="t" k="3" />
    <hkern u1="t" u2="&#x259;" k="6" />
    <hkern u1="t" u2="&#x27;" k="7" />
    <hkern u1="t" u2="&#x2019;" k="3" />
    <hkern u1="t" u2="&#x2018;" k="3" />
    <hkern u1="t" u2="o" k="8" />
    <hkern u1="t" u2="&#x2d;" k="7" />
    <hkern u1="t" u2="&#x2039;" k="9" />
    <hkern u1="t" u2="f" k="3" />
    <hkern u1="t" u2="&#xf0;" k="9" />
    <hkern u1="t" u2="d" k="8" />
    <hkern u1="t" u2="\" k="18" />
    <hkern u1="t" u2="&#x2bc;" k="44" />
    <hkern u1="t" u2="Y" k="53" />
    <hkern u1="t" u2="W" k="36" />
    <hkern u1="t" u2="V" k="34" />
    <hkern u1="t" u2="U" k="12" />
    <hkern u1="t" u2="T" k="37" />
    <hkern u1="t" u2="O" k="9" />
    <hkern u1="t" u2="I" k="3" />
    <hkern g1="t.alt" u2="&#x259;" k="13" />
    <hkern g1="t.alt" u2="s" k="7" />
    <hkern g1="t.alt" u2="&#x27;" k="5" />
    <hkern g1="t.alt" u2="&#x2019;" k="-9" />
    <hkern g1="t.alt" u2="&#x2e;" k="25" />
    <hkern g1="t.alt" u2="o" k="16" />
    <hkern g1="t.alt" u2="&#x135;" k="-15" />
    <hkern g1="t.alt" u2="&#x129;" k="-64" />
    <hkern g1="t.alt" u2="&#xef;" k="-48" />
    <hkern g1="t.alt" u2="&#xee;" k="-15" />
    <hkern g1="t.alt" u2="&#x2d;" k="17" />
    <hkern g1="t.alt" u2="&#x203a;" k="5" />
    <hkern g1="t.alt" u2="&#x2039;" k="23" />
    <hkern g1="t.alt" u2="&#xf0;" k="28" />
    <hkern g1="t.alt" u2="d" k="15" />
    <hkern g1="t.alt" u2="&#x40;" k="4" />
    <hkern g1="t.alt" u2="&#x2a;" k="-28" />
    <hkern g1="t.alt" u2="&#x2bc;" k="18" />
    <hkern g1="t.alt" u2="a" k="7" />
    <hkern g1="t.alt" u2="&#x2f;" k="18" />
    <hkern g1="t.alt" u2="&#x20;" k="21" />
    <hkern u1="&#x167;" g2="w.alt" k="2" />
    <hkern u1="&#x167;" u2="v" k="2" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-61" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-44" />
    <hkern u1="&#x165;" u2="&#x165;" k="-17" />
    <hkern u1="&#x165;" g2="t.alt" k="-25" />
    <hkern u1="&#x165;" u2="t" k="-13" />
    <hkern u1="&#x165;" u2="&#x161;" k="-21" />
    <hkern u1="&#x165;" u2="&#x27;" k="-51" />
    <hkern u1="&#x165;" u2="&#x2019;" k="-62" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-62" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-15" />
    <hkern u1="&#x165;" u2="&#x29;" k="-70" />
    <hkern u1="&#x165;" u2="&#xba;" k="-17" />
    <hkern u1="&#x165;" u2="&#xaa;" k="-30" />
    <hkern u1="&#x165;" g2="l.alt" k="-52" />
    <hkern u1="&#x165;" u2="l" k="-55" />
    <hkern u1="&#x165;" u2="i" k="-52" />
    <hkern u1="&#x165;" u2="h" k="-52" />
    <hkern u1="&#x165;" u2="&#xdf;" k="-6" />
    <hkern u1="&#x165;" u2="&#x21;" k="-36" />
    <hkern u1="&#x165;" u2="&#xf0;" k="-10" />
    <hkern u1="&#x165;" u2="]" k="-60" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-60" />
    <hkern u1="&#x165;" u2="&#x7c;" k="-47" />
    <hkern u1="&#x165;" u2="\" k="-62" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-45" />
    <hkern u1="&#x165;" u2="&#xe4;" k="-23" />
    <hkern u1="&#xfe;" u2="&#x142;" k="-4" />
    <hkern u1="&#x33;" u2="&#xc6;" k="-20" />
    <hkern u1="&#x33;" u2="&#x37;" k="4" />
    <hkern u1="&#x33;" u2="&#x27;" k="5" />
    <hkern u1="&#x33;" u2="&#xb0;" k="15" />
    <hkern u1="&#x33;" u2="\" k="30" />
    <hkern u1="&#x33;" u2="Y" k="37" />
    <hkern u1="&#x33;" u2="X" k="-11" />
    <hkern u1="&#x33;" u2="W" k="25" />
    <hkern u1="&#x33;" u2="V" k="24" />
    <hkern u1="&#x33;" u2="T" k="31" />
    <hkern g1="three.lnum" u2="&#x2b;" k="3" />
    <hkern u1="&#x32;" u2="&#xc6;" k="-11" />
    <hkern u1="&#x32;" u2="\" k="27" />
    <hkern u1="&#x32;" u2="Y" k="35" />
    <hkern u1="&#x32;" u2="W" k="24" />
    <hkern u1="&#x32;" u2="V" k="23" />
    <hkern u1="&#x32;" u2="T" k="30" />
    <hkern g1="two.lnum" u2="&#x2b;" k="4" />
    <hkern g1="two.lnum" u2="&#x2212;" k="4" />
    <hkern g1="two.lnum" u2="&#x2d;" k="6" />
    <hkern g1="two.lnum" g2="four.lnum" k="8" />
    <hkern g1="two.lnum" u2="&#xb7;" k="6" />
    <hkern u1="u" g2="y.alt" k="6" />
    <hkern u1="u" u2="y" k="4" />
    <hkern u1="u" g2="w.alt" k="5" />
    <hkern u1="u" u2="w" k="7" />
    <hkern u1="u" u2="v" k="5" />
    <hkern u1="u" u2="&#x2122;" k="16" />
    <hkern u1="u" g2="t.alt" k="3" />
    <hkern u1="u" u2="t" k="4" />
    <hkern u1="u" u2="&#x2019;" k="8" />
    <hkern u1="u" u2="&#x2018;" k="7" />
    <hkern u1="u" u2="\" k="28" />
    <hkern u1="u" u2="&#x2a;" k="7" />
    <hkern u1="u" u2="&#x2bc;" k="31" />
    <hkern u1="u" u2="Z" k="2" />
    <hkern u1="u" u2="Y" k="68" />
    <hkern u1="u" u2="W" k="45" />
    <hkern u1="u" u2="V" k="43" />
    <hkern u1="u" u2="U" k="11" />
    <hkern u1="u" u2="T" k="70" />
    <hkern u1="u" u2="O" k="9" />
    <hkern u1="u" u2="&#xd0;" k="5" />
    <hkern u1="&#x15f;" u2="j" k="-24" />
    <hkern u1="&#x163;" u2="j" k="-111" />
    <hkern u1="&#x21b;" u2="j" k="-40" />
    <hkern u1="&#x173;" u2="j" k="-77" />
    <hkern u1="v" u2="&#xc6;" k="51" />
    <hkern u1="v" u2="&#x259;" k="15" />
    <hkern u1="v" u2="s" k="13" />
    <hkern u1="v" u2="&#x2e;" k="36" />
    <hkern u1="v" u2="&#x29;" k="13" />
    <hkern u1="v" u2="&#xba;" k="-4" />
    <hkern u1="v" u2="o" k="16" />
    <hkern u1="v" u2="&#x142;" k="3" />
    <hkern u1="v" u2="&#x2d;" k="15" />
    <hkern u1="v" u2="&#x2039;" k="19" />
    <hkern u1="v" u2="&#xf0;" k="25" />
    <hkern u1="v" u2="d" k="15" />
    <hkern u1="v" u2="\" k="11" />
    <hkern u1="v" u2="&#x40;" k="3" />
    <hkern u1="v" u2="&#x2a;" k="-13" />
    <hkern u1="v" u2="&#x2bc;" k="4" />
    <hkern u1="v" u2="a" k="12" />
    <hkern u1="v" u2="Z" k="16" />
    <hkern u1="v" u2="Y" k="34" />
    <hkern u1="v" u2="X" k="30" />
    <hkern u1="v" u2="W" k="14" />
    <hkern u1="v" u2="V" k="13" />
    <hkern u1="v" u2="T" k="48" />
    <hkern u1="v" u2="&#x2f;" k="30" />
    <hkern u1="v" u2="A" k="31" />
    <hkern u1="v" u2="J" k="62" />
    <hkern u1="v" u2="&#xd0;" k="4" />
    <hkern u1="v" u2="&#x20;" k="26" />
    <hkern u1="w" u2="&#xc6;" k="54" />
    <hkern u1="w" u2="&#x259;" k="18" />
    <hkern u1="w" u2="s" k="16" />
    <hkern u1="w" u2="&#x2e;" k="42" />
    <hkern u1="w" u2="&#x29;" k="14" />
    <hkern u1="w" u2="&#xba;" k="-5" />
    <hkern u1="w" u2="o" k="20" />
    <hkern u1="w" u2="&#x142;" k="9" />
    <hkern u1="w" u2="&#x2d;" k="19" />
    <hkern u1="w" u2="&#x2039;" k="24" />
    <hkern u1="w" u2="&#xf0;" k="30" />
    <hkern u1="w" u2="d" k="19" />
    <hkern u1="w" u2="\" k="12" />
    <hkern u1="w" u2="&#x40;" k="3" />
    <hkern u1="w" u2="&#x2a;" k="-16" />
    <hkern u1="w" u2="&#x2bc;" k="4" />
    <hkern u1="w" u2="a" k="14" />
    <hkern u1="w" u2="Z" k="16" />
    <hkern u1="w" u2="Y" k="32" />
    <hkern u1="w" u2="X" k="32" />
    <hkern u1="w" u2="W" k="13" />
    <hkern u1="w" u2="V" k="12" />
    <hkern u1="w" u2="T" k="47" />
    <hkern u1="w" u2="S" k="4" />
    <hkern u1="w" u2="&#x2f;" k="36" />
    <hkern u1="w" u2="O" k="3" />
    <hkern u1="w" u2="A" k="35" />
    <hkern u1="w" u2="J" k="73" />
    <hkern u1="w" u2="&#xd0;" k="4" />
    <hkern u1="w" u2="&#x20;" k="28" />
    <hkern g1="w.alt" u2="&#x259;" k="13" />
    <hkern g1="w.alt" u2="s" k="12" />
    <hkern g1="w.alt" u2="&#x2e;" k="31" />
    <hkern g1="w.alt" u2="&#x29;" k="13" />
    <hkern g1="w.alt" u2="&#xba;" k="-4" />
    <hkern g1="w.alt" u2="o" k="15" />
    <hkern g1="w.alt" u2="&#x142;" k="3" />
    <hkern g1="w.alt" u2="&#x2d;" k="13" />
    <hkern g1="w.alt" u2="&#x2039;" k="17" />
    <hkern g1="w.alt" u2="&#xf0;" k="23" />
    <hkern g1="w.alt" u2="d" k="15" />
    <hkern g1="w.alt" u2="\" k="11" />
    <hkern g1="w.alt" u2="&#x2a;" k="-13" />
    <hkern g1="w.alt" u2="&#x2bc;" k="4" />
    <hkern g1="w.alt" u2="a" k="12" />
    <hkern g1="w.alt" u2="&#x2f;" k="28" />
    <hkern g1="w.alt" u2="&#x20;" k="26" />
    <hkern u1="x" u2="&#x259;" k="20" />
    <hkern u1="x" u2="s" k="5" />
    <hkern u1="x" u2="&#x29;" k="-10" />
    <hkern u1="x" u2="o" k="21" />
    <hkern u1="x" u2="&#x2d;" k="24" />
    <hkern u1="x" u2="&#x2039;" k="24" />
    <hkern u1="x" u2="f" k="3" />
    <hkern u1="x" u2="&#xf0;" k="26" />
    <hkern u1="x" u2="d" k="19" />
    <hkern u1="x" u2="&#x2a;" k="-17" />
    <hkern u1="x" u2="&#x2bc;" k="11" />
    <hkern u1="x" u2="a" k="9" />
    <hkern u1="x" u2="Y" k="31" />
    <hkern u1="x" u2="W" k="14" />
    <hkern u1="x" u2="V" k="13" />
    <hkern u1="x" u2="U" k="4" />
    <hkern u1="x" u2="T" k="44" />
    <hkern u1="x" u2="&#x2f;" k="-9" />
    <hkern u1="x" u2="O" k="12" />
    <hkern u1="y" u2="&#xc6;" k="50" />
    <hkern u1="y" u2="&#x259;" k="15" />
    <hkern u1="y" u2="s" k="14" />
    <hkern u1="y" u2="&#x2e;" k="35" />
    <hkern u1="y" u2="&#x29;" k="13" />
    <hkern u1="y" u2="o" k="17" />
    <hkern u1="y" u2="&#x142;" k="3" />
    <hkern u1="y" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2039;" k="19" />
    <hkern u1="y" u2="&#xf0;" k="25" />
    <hkern u1="y" u2="d" k="16" />
    <hkern u1="y" u2="\" k="13" />
    <hkern u1="y" u2="&#x40;" k="4" />
    <hkern u1="y" u2="&#x2bc;" k="4" />
    <hkern u1="y" u2="&#x26;" k="3" />
    <hkern u1="y" u2="a" k="12" />
    <hkern u1="y" u2="Z" k="17" />
    <hkern u1="y" u2="Y" k="38" />
    <hkern u1="y" u2="X" k="33" />
    <hkern u1="y" u2="W" k="18" />
    <hkern u1="y" u2="V" k="17" />
    <hkern u1="y" u2="T" k="51" />
    <hkern u1="y" u2="S" k="3" />
    <hkern u1="y" u2="&#x2f;" k="27" />
    <hkern u1="y" u2="A" k="31" />
    <hkern u1="y" u2="J" k="60" />
    <hkern u1="y" u2="I" k="2" />
    <hkern u1="y" u2="&#xd0;" k="4" />
    <hkern u1="y" u2="&#x20;" k="25" />
    <hkern g1="y.alt" u2="&#x259;" k="12" />
    <hkern g1="y.alt" u2="s" k="10" />
    <hkern g1="y.alt" u2="&#x2e;" k="31" />
    <hkern g1="y.alt" u2="&#x29;" k="4" />
    <hkern g1="y.alt" u2="&#xba;" k="-3" />
    <hkern g1="y.alt" u2="o" k="14" />
    <hkern g1="y.alt" u2="&#x142;" k="3" />
    <hkern g1="y.alt" u2="&#x2d;" k="9" />
    <hkern g1="y.alt" u2="&#x2039;" k="16" />
    <hkern g1="y.alt" u2="&#xf0;" k="21" />
    <hkern g1="y.alt" u2="d" k="13" />
    <hkern g1="y.alt" u2="\" k="5" />
    <hkern g1="y.alt" u2="&#x40;" k="4" />
    <hkern g1="y.alt" u2="&#x2bc;" k="4" />
    <hkern g1="y.alt" u2="a" k="10" />
    <hkern g1="y.alt" u2="&#x2f;" k="25" />
    <hkern g1="y.alt" u2="&#x20;" k="25" />
    <hkern u1="z" u2="&#x259;" k="7" />
    <hkern u1="z" u2="o" k="9" />
    <hkern u1="z" u2="&#x2d;" k="13" />
    <hkern u1="z" u2="&#x2039;" k="10" />
    <hkern u1="z" u2="&#xf0;" k="13" />
    <hkern u1="z" u2="d" k="9" />
    <hkern u1="z" u2="\" k="13" />
    <hkern u1="z" u2="&#x2bc;" k="19" />
    <hkern u1="z" u2="Y" k="49" />
    <hkern u1="z" u2="W" k="30" />
    <hkern u1="z" u2="V" k="29" />
    <hkern u1="z" u2="U" k="9" />
    <hkern u1="z" u2="T" k="58" />
    <hkern u1="z" u2="O" k="5" />
    <hkern u1="z" u2="I" k="4" />
    <hkern u1="z" u2="&#xd0;" k="3" />
    <hkern u1="&#x30;" u2="&#xc6;" k="20" />
    <hkern u1="&#x30;" u2="&#x37;" k="7" />
    <hkern u1="&#x30;" u2="&#x27;" k="5" />
    <hkern u1="&#x30;" u2="&#x29;" k="15" />
    <hkern u1="&#x30;" u2="&#xb0;" k="14" />
    <hkern u1="&#x30;" u2="\" k="34" />
    <hkern u1="&#x30;" u2="Z" k="5" />
    <hkern u1="&#x30;" u2="Y" k="42" />
    <hkern u1="&#x30;" u2="X" k="13" />
    <hkern u1="&#x30;" u2="W" k="31" />
    <hkern u1="&#x30;" u2="V" k="29" />
    <hkern u1="&#x30;" u2="T" k="36" />
    <hkern u1="&#x30;" u2="&#x2f;" k="8" />
    <hkern u1="&#x30;" u2="A" k="15" />
    <hkern u1="&#x30;" u2="J" k="10" />
    <hkern g1="zero.lnum" g2="seven.lnum" k="7" />
    <hkern g1="zero.lnum" u2="&#x27;" k="4" />
    <hkern g1="zero.lnum" u2="&#x2e;" k="6" />
    <hkern g1="zero.lnum" u2="&#x29;" k="14" />
    <hkern g1="zero.lnum" u2="&#xb0;" k="3" />
    <hkern g1="zero.lnum" u2="\" k="18" />
    <hkern g1="zero.lnum" u2="&#x2f;" k="20" />
  </font>
</defs></svg>
