/************************************************
*                                               *
*                    VUE HOME                   *
*                                               *
*************************************************/

.pageClient .open{
	position: fixed;
	top:10px;
	left:0.325em;
	font-size:2em;
	text-shadow: none;
	opacity:1;
	font-weight:normal;
	padding: 0.375em 0.5em;
	border-radius: 5px;
	text-align:center;
	z-index:99;
}

/****************************
*                           *
*            Menu           *
*                           *
*****************************/

.menu .btn-deconnexion{
	width:200px;
	margin-top:15px;
}
.menu p:first-child{
	line-height: 26px;
}
.menu p{
	line-height: 18px;
}
.menu ul li{
	padding:5px;
	font-weight: bold;
}
.menu .close{
	position: absolute;
	top:0;
	right:0.325em;
	font-size:2em;
	margin:10px;
	text-shadow: none;
	opacity:1;
	font-weight:normal;
	border-radius: 5px;
	padding: 0.375em 0.5em;
}
@media (min-width: 767px) and (max-width: 881px){
	#menu .btn-deconnexion {
	  margin-left:-18%;
	}
}
.listeMenu{
	margin-top: 5em;
}
.listeMenu .highlight{
	margin-top:10px;
	width:100%;
	padding:10px;
}
.listeMenu .highlight a{
	margin-top:0px;
}
@media only screen and (min-width: 40em) {
	.listeMenu{
		margin-top: 0;
	}
}

#menuWrap{
	position:fixed;
	width:100%;
	top:0;
	left:-100%;
	text-align:center;
	z-index:1000;
	-webkit-transition: left 0.5s linear;
	-moz-transition: left 0.5s linear;
	-ms-transition: left 0.5s linear;
	-o-transition: left 0.5s linear;
	transition: left 0.5s linear;
}

#menuWrap:target{
	left:0px;
}

.menuWrapper{
	position:relative;
}
.menu{
	position:absolute;
	top:0px;
	left:0;
	padding-bottom: 9999px;
	margin-bottom: -9999px;
	overflow: hidden;
}
@media (min-width: 64em) {
	#menuWrap{
		left:0;
		z-index:0;
		-webkit-transition: none;
		-moz-transition: none;
		-ms-transition: none;
		-o-transition: none;
		transition: none;
	}
}

/****************************
*                           *
*        Mon magasin        *
*                           *
*****************************/

/****************************
*                           *
*         Ma carte          *
*                           *
*****************************/

.maCarte,.passbook{
    .bubble{
        display: flex;
        flex-direction: column;
    }
    .firstBlock{
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 5;
    }
    .secondBlock{
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex: 2;
    }
}

// a revoir
#maCarte .carte .bloc, #maCarte .points .bloc{
	height:280px;
}
#maCarte .carte .codeCarte{
	margin:10px 0;
}
.code-barre {
	margin: 0 auto;
    padding: 10px 3px 0px 3px;
	max-height: 80px;
	padding	:0 1em;
}
#maCarte #donuts{
	margin:10px 0;
}
#maCarte .carte .bloc, #maCarte .points .bloc{
	height: 290px;
}
#maCarte .bloc img{
	margin-top:10px;
}
#maCarte #donuts {
	margin: 0 0 10px 0;
}

/****************************
*                           *
*        Mon compte         *
*                           *
*****************************/


#blocPoint {
	margin: 5px 0px;
}

#adresse label {
	font-weight: normal;
}

.mentions-legales{
	text-align: justify;
	margin: 35px 0px 20px 0px;
}
#monCompte{
	padding-bottom: 40px;
}

/****************************
*                           *
*        Historique         *
*                           *
*****************************/
.historique{
	flex: 1;
	margin-top: 1em;
	margin-bottom: 1em;
}

.historique-list {
	padding: 0px 0.5rem ;
	li {
		padding: 0.25rem 0;
		div:nth-child(1) {
			flex: 30;
			text-align: right;
		}
		div:nth-child(2) {
			text-align: left;
			flex: 15;
			padding-left: 0.125rem;
			justify-content: bottom;
		}
		div:nth-child(3) {
			flex: 40;
			text-align: left;
		}
	}
}

.separator {
	$width: 11rem;
	@include separator(solid, #E5E5E5, 2px, $width);
}

.monCompte {
	padding-bottom:50px;
	.submit {
		text-align: right;
	}
	.cnilMessageInfoRecueillies {
		margin: 2rem 0em;
		font-size: 0.8rem;
	}
	.form-group--margin {
		margin-left: 1.4rem;
	}
}

/****************************
*                           *
*        Mon programme      *
*                           *
*****************************/

.monProgramme__bloc{
	font-size: 0.9em;
	line-height: 1.5;
	min-height: 110px;
	display: flex;
	.monProgramme__content{
		padding: 0px 10px;
	}
	.monProgramme__titre{
		font-size: 1.3em;
	}
	.bloc-icon{
		min-width: 80px;
	}
}