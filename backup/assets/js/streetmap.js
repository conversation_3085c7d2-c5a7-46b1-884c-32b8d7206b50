var map;
var ajaxRequest;
var plotlist;
var plotlayers=[];

function initmap() {
    // set up the map
    map = new L.Map('map');

    // create the tile layer with correct attribution
    var osmUrl='http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
    var osmAttrib='Map data © <a href="http://openstreetmap.org">OpenStreetMap</a> contributors';
    var osm = new <PERSON><PERSON>Tile<PERSON>ayer(osmUrl, {minZoom: 8, maxZoom: 12, attribution: osmAttrib});

    // start the map in South-East England
    map.setView(new L.LatLng($("#map").data("lat"), $("#map").data("long")),20);
    L.marker([$("#map").data("lat"), $("#map").data("long")]).addTo(map)
    .bindPopup($("#map").data("nom-mag"));
    map.addLayer(osm);
}

$(document).ready(function() {
    if ($('#map').length) {
        initmap();
    }
});