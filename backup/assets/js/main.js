$(document).ready(function(){
	//effet scroll du menu
	$('.menu ul a').on('click', function(e){
		e.preventDefault();
		var scroll = '#' + $(this).attr("href").split('/vue/#')[1];
		var time = Math.abs($(scroll).offset().top - $(document).scrollTop());
		if(time > 1000)
			time = 1000;
		$('html, body').animate({
			scrollTop: $(scroll).offset().top+'px'
		}, time);
		return false;
	});
	$('.radioSelect .radio input:not([checked])').parent().hide();
	$('.radioSelect .radio label, .radioSelect .arrow').mouseup(function(){
		var obj= $(this).parent();
		if(obj.attr('class').indexOf('radioSelect')<0){
			obj = obj.parent();
		}
		if(obj.children('div.radio').attr('class').indexOf('grand') < 0){
			$('.radioSelect .radio').removeClass('grand');
			obj.children('div.radio').addClass('grand');
			obj.children('div.radio').children('label').show();
			obj.children('span.arrow').hide();
			
		}else{
			obj.children('div.radio').removeClass('grand');
			obj.children('div.radio').children('label').hide();
			$(this).show();
			obj.children('span.arrow').show();
		}
	});

	// setup an "add a tag" link
	var $addTagLink = $('#addChild');
	// Get the ul that holds the collection of tags
	$collectionHolder = $('#prototypeEnfants');
	// count the current form inputs we have (e.g. 2), use that as the new
	// index when inserting a new item (e.g. 2)
	$collectionHolder.data('index', $collectionHolder.find(':input').length);
	$addTagLink.on('click', function(e) {
	// prevent the link from creating a "#" on the URL
	e.preventDefault();
		// add a new tag form (see next code block)
		addTagForm($collectionHolder);
	});
	$collectionHolder.on('click', '.delete', function(e) {
	e.preventDefault();
		$(this).closest('fieldset').remove();
		return false;
	});
	function addTagForm($collectionHolder) {
	// Get the data-prototype explained earlier
	var prototype = $collectionHolder.data('prototype');
		// get the new index
		var index = $collectionHolder.data('index');
		// Replace '__name__' in the prototype's HTML to
		// instead be a number based on how many items we have
		var newForm = prototype.replace(/__name__/g, index);
		// increase the index with one for the next item
		$collectionHolder.data('index', index + 1);
		var $newFormLi = $('<fieldset class="no_fieldset enfant-field"><div class="column large-12"></div></fieldset>');
		$newForm = $(newForm).append($('<div><a href="#" class="btn btn_cancel delete"><span>delete</span></a></div>'));
		$newFormLi.find('.large-12').append($newForm);
		// Display the form in the page in an li, before the "Add a tag" link li
		$collectionHolder.append($newFormLi);
	}
	$('#client_telephoneMobile').on('blur', function(){
		$('#client_envoieSmsInterne').prop('checked', true);
	});
	$('#client_email').on('blur', function(){
		$('#client_envoieEmailInterne').prop('checked', true);
	});
});
