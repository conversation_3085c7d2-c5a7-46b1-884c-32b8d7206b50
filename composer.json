{"type": "project", "license": "proprietary", "require": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*", "alienor/logger-webservice-bundle": "^7.1", "alienor/pdf-bundle": "dev-master", "alienor/requeteur-adresse-bundle": "^7.1", "composer/package-versions-deprecated": "1.11.99.1", "doctrine/collections": "^2.3", "meteo-concept/hcaptcha-bundle": "^4.2.2", "nyholm/psr7": "^1.8", "phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.32", "picqer/php-barcode-generator": "^2.2", "runtime/frankenphp-symfony": "^0.2.0", "setasign/fpdf": "1.8.*", "setasign/fpdi": "^2.0", "symfony/apache-pack": "^1.0", "symfony/asset": "^7.2", "symfony/asset-mapper": "^7.2", "symfony/console": "^7.2", "symfony/dotenv": "^7.2", "symfony/expression-language": "^7.2", "symfony/flex": "^1.3.1", "symfony/form": "^7.2", "symfony/framework-bundle": "^7.2", "symfony/http-client": "^7.2", "symfony/intl": "^7.2", "symfony/mailer": "^7.2", "symfony/monolog-bundle": "^3.1", "symfony/process": "^7.2", "symfony/property-access": "^7.2", "symfony/property-info": "^7.2", "symfony/runtime": "^7.2", "symfony/security-bundle": "^7.2", "symfony/serializer": "^7.2", "symfony/translation": "^7.2", "symfony/twig-bundle": "^7.2", "symfony/validator": "^7.2", "symfony/web-link": "^7.2", "symfony/yaml": "^7.2", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0", "yellowskies/qr-code-bundle": "^1.2"}, "require-dev": {"alienor/**********************-git": "6.4.*", "chqthomas/approval-tests": "dev-main", "cweagans/composer-patches": "^1.7", "friendsofphp/php-cs-fixer": "^3.69", "gajus/dindent": "^2.0", "phpunit/phpunit": "^9.5", "rector/rector": "^2.0", "symfony/browser-kit": "^7.2", "symfony/css-selector": "^7.2", "symfony/debug-bundle": "^7.2", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^5.2", "symfony/stopwatch": "^7.2", "symfony/var-dumper": "^7.2", "symfony/web-profiler-bundle": "^7.2", "symplify/config-transformer": "^12.4", "symplify/vendor-patches": "^11.3", "wiremock-php/wiremock-php": "^2.35"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/", "Boticinal\\": "enseigne/boticinal/src/", "Giropharm\\": "enseigne/giropharm/src/", "Normandiepharma\\": "enseigne/normandiepharma/src/", "Alienor\\PDFBundle\\": "vendor/alienor/pdf-bundle/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "Giropharm\\Tests\\": "enseigne/giropharm/tests/", "Normandiepharma\\Tests\\": "enseigne/normandiepharma/tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"patches": {"chqthomas/approval-tests": ["patches/chqthomas-approval-tests-src-scrubber-jsonscrubber-php.patch", "patches/chqthomas-approval-tests-src-fileapprover-fileapproverbase-php.patch", "patches/chqthomas-approval-tests-src-scrubber-htmlscrubber-php.patch"], "meteo-concept/hcaptcha-bundle": ["patches/meteo-concept-hcaptcha-bundle-form-datatransformer-hcaptchavaluefetcher-php.patch"], "symfony/serializer": ["patches/symfony-serializer-normalizer-abstractobjectnormalizer-php.patch"]}, "symfony": {"allow-contrib": false, "require": "^7.2"}}, "repositories": [{"type": "git", "url": "**********************:dev-symfony2/AquitemWebServiceParserBundle.git"}, {"type": "git", "url": "**********************:dev-symfony2/ApiBundle.git"}, {"type": "git", "url": "**********************:dev-symfony2/LoggerWebserviceBundle.git"}, {"type": "git", "url": "**********************:dev-symfony2/AlienorRequeteurAdresseBundle.git"}, {"type": "git", "url": "**********************:dev-symfony2/pdfbundle.git"}, {"type": "git", "url": "**********************:dev-projets-alienor/**********************-git.git"}], "minimum-stability": "dev", "prefer-stable": true}