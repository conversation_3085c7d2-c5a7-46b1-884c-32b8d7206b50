/app/config/parameters.yml
/cartes/
/echeques/
/code_barre/
/composer.phar
.idea/
.sass-cache/

/build/
/node_modules/
npm-debug.log
yarn-error.log
/var/*
!/var/cache
/var/cache/*
!var/cache/.gitkeep
!/var/data
!/var/log
/var/log/*
!var/log/.gitkeep
!/var/sessions
/var/sessions/*
!var/sessions/.gitkeep
!var/SymfonyRequirements.php
/public/build/fonts/glyphicons-*
/public/build/images/glyphicons-*
tests/**/*.received.txt

###> CI/CD ###
.gitlab/generated/
###< CI/CD ###


###> symfony/framework-bundle ###
/.env.local
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> symfony/phpunit-bridge ###
.phpunit
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> symfony/web-server-bundle ###
/.web-server-pid
###< symfony/web-server-bundle ###

###> friendsofphp/php-cs-fixer ###
/.php_cs
/.php_cs.cache
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###
###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

.task/

###> Dotenv racine (variantes locales ou tiers) ###
/.env.*.local
###< Dotenv racine (variantes locales ou tiers) ###

###> Dotenv par enseigne ###
/enseigne/*/.env.local
/enseigne/*/.env.*.local
###< Dotenv par enseigne ###
.enseigne.local

###> symfony/asset-mapper ###
/public/assets/
/enseigne/*/assets/vendor/
###< symfony/asset-mapper ###
