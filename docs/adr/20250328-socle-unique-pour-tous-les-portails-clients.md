# Socle unique pour tous les portails clients

- Status: accepted
- Deciders: everyone
- Date: 2024-12-01
- Tags: crasfmanship, zefid, montéeDeVersion

## Context and Problem Statement

Trop de portails clients à montée en version. Pas de suivi coté aquitem.
Comment accélérer le travail de maintien à jour des socles symfony ?

## Decision Drivers <!-- optional -->

- une année de travail de montées de version qui n'ont pas été validées par les CP, aquitem et les clients
- plusieurs hacks sur les vieux portails clients (aquiwebl3, aquiwebl4)

## Considered Options

- maintenir tous les portails clients indépendamment
- socle unique pour tous les portails clients

## Decision Outcome

Chosen option: "socle unique pour tous les portails clients", because il est necessaire d'accélérer le travail de maintien à jour des socles symfony.

### Positive Consequences <!-- optional -->

- mise en place de tests
- montée de version plus rapide
- mise en place de CI avec .gitlab-ci.yml
- vers déploiement en production ?

### Negative Consequences <!-- optional -->

- nécessité de migrer tous les portails un par un vers le socle unique

