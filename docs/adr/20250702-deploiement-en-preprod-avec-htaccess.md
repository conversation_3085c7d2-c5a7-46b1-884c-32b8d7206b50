# Déploiement en preprod avec htaccess

- Status: accepted
- Deciders: Paul
- Date: 2025-07-02
- Tags: 

Technical Story: Déploiement avec basic auth

## Context and Problem Statement

Sans apache mais avec franckenphp, comment déployer avec une basic auth

## Decision Outcome

Utilisation de variable d'environnement dans le .env.docker :   
BASIC_AUTH_ENABLED=true    
BASIC_AUTH_LOGIN_1=devadmin $2y$10$xxxxxxxxxxxxxxxxx    
BASIC_AUTH_LOGIN_2=web-4663 $2y$10$xxxxxxxxxxxxxxxxx   

Utiliser htpasswd -bnBC 10 "devadmin" "password" pour générer le password crypté

Attention, passer à false le BASIC_AUTH_ENABLED ne fonctionnera pas. Il faudra supprimer la ligne.
