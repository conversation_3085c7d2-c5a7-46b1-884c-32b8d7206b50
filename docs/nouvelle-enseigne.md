# Procédure nouvelle enseigne

## Prérequis

- modifier le fichier `.env` pour définir la variable `ENSEIGNE` avec le nom de la nouvelle enseigne (et non dans le `.env.local` qui n'est pas pris en compte dans node et caddy)
- modifier le fichier Taskfile.yml pour définir la variable `ENSEIGNE` avec le nom de la nouvelle enseigne

- créer un dossier du nom de l'enseigne dans le dossier `enseigne`
- créer un dossier `assets` dans le dossier `enseigne` du projet
- créer un fichier `config.php` dans le dossier `enseigne` du projet
- créer un fichier `routes.php` dans le dossier `enseigne` du projet
- créer un fichier `services.yaml` dans le dossier `enseigne` du projet
- créer un fichier `security.yaml` dans le dossier `enseigne` du projet
- créer un fichier `templates.yaml` dans le dossier `enseigne` du projet
- créer un fichier `webpack.config.js` dans le dossier `enseigne` du projet

- modifier le fichier `composer.json` pour ajouter la dépendance du projet dans la section `autoload-dev`
- modifier le fichier `composer.json` pour ajouter la dépendance du projet dans la section `autoload`
 
## Déploiement

Déployer la configuration des variables d'environnement
```bash
export ENSEIGNE=toto
export HCAPTCHA_SECRET= (voir variable sur http://alienor-confluence-dev.alienor.net:8090/display/CC/Externes)
```
