TODO:
- Corriger le test Vue de Giropharm
- mettre en lien symbolique dans public les assets
- mettre à jour l'authenticator générique pour utiliser le hcaptcha
- traductions : impossible d'utiliser une variable d'environnement dans le path du dossier de traduction
(config/packages/translation.yaml -> actuellement un giropharm en dur)
- .env et .env.local par enseigne
[14:57]t.choquet: https://gitlab.alienor.net/dev-interne/myvoicesurvey/-/blob/base/src/Twig/Extension/CampaignTransExtension.php?ref_type=heads
[14:58]Paul <PERSON>: https://github.com/symfony/symfony/issues/40794#issuecomment-818611947
- utilisation par WireMock des fichiers de tests de l'enseigne
- plusieurs appels des données statiques ? (cache ?)
- update ?
- compliler path récupération de la variable d'environnement ENSEIGNE
- CustomFormRegistry extends FormRegistry
- transformer les configuration en php fluent
    - https://github.com/symplify/config-transformer
    - https://symfony.com/doc/current/configuration.html#using-php-configbuilders
- typer correctement les strings dans les classes pour que dans les approvales les retours ne soient pas changés :
    - id: "CALCULPTSARRONDI",
      libelle: null,
      valeur: "1", => valeur: 1,
- mettre la feature passbook qui se trouve dans parametersClient.yml dans la config par enseigne
    - carte.passbook: true

Questionnement :
- surcharge des variables d'environnement par projet ? (.env et .env.local par enseigne ?)
    -> tentative avec src/Services/DotEnvEnseigneLoader.php (ne fonctionne pas encore)
- rendre l'enseigne facultative ?

Fait:
- conditionner les routes après la montée de version en utilisant le service feature flipper
    -> fait pour client activate
    -> fait pour les dons
- déplacer les templates dans le dossier enseigne/{{.ENSEIGNE}}/templates
    -> ok (twig.yaml convertie en php afin de conditionner l'ajout des templates à la config ENSEIGNE)
- surcharge du serializer par enseigne

FEP
- authentification bug :
- manquant dans le formulaire de login : <input type="hidden" name="_token" value="{{ csrf_token('authenticate') }}"/>

Info:
- feature flipper : si une valeur est surchargée dans la config d'une enseigne, les autres valeurs du FF sont supprimées
    -> penser à reprendre toutes les valeurs de la config par défaut
