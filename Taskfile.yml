# https://taskfile.dev

version: '3'

vars:
  GITLAB_PROJECT: 'dev-projets-aquitem/zefid_portail_client'
  PREPROD_SERVER: 'xxx'
  PREPROD_SERVICE: 'web-xxxx_front_web'
  PROD_SERVER: 'xxx-prod'
  PROD_SERVICE: 'web-xxxx_front_web'
  PROJECT_NAME: 'portail-client-generique'
  ENSEIGNE: ''

includes:
  docker: https://gitlab.alienor.net/-/snippets/11/raw/main/Taskfile.yml

tasks:
  default:
    cmds:
      - task: docker:exec
    silent: true

  expose-dev:
    desc: Expose dev
    cmds:
      -  ssh -i ~/.ssh/id_rsa -p 2222 -R {{.PROJECT_NAME}}:80:localhost:80 devadmin@**************

  wiremock-start:
    desc: Start wiremock
    cmds:
      - '{{.DOCKER_SYMFONY}} wiremock:start'
  tests:
    desc: Run tests
    cmds:
      - cmd: |
            if [ -n "{{.ENSEIGNE}}" ]; then
              {{.DOCKER_PHP}} vendor/bin/phpunit -c "enseigne/{{.ENSEIGNE}}/phpunit.xml.dist"
            else
              {{.DOCKER_PHP}} vendor/bin/phpunit
            fi
        ignore_error: true
      - task: wiremock-start

  cleanup-test:
    desc: Remove x.received.html if x.approved.html exists and is identical, and they are untracked in specific directory
    cmds:
      - cmd: |
            # Get the list of untracked files with 'git ls-files --others --exclude-standard'
            for file in $(git ls-files --others --exclude-standard | grep '^enseigne/{{.ENSEIGNE}}/tests/Integration/approvals/.*\.received\.html$'); do
              approved_file="${file%.received.html}.approved.html"
              # Check if the corresponding .approved.html file exists and if they are identical
              if [ -f "$approved_file" ] && cmp -s "$file" "$approved_file"; then
                echo "Removing untracked file $file as it is identical to $approved_file"
                rm "$file"  # Remove the untracked file from the filesystem
              fi
            done

  tests-approval-clean:
    desc: Clean received file from approval tests
    cmds:
      - 'rm tests/Integration/approvals/*.received.html'
      - 'git checkout tests/Integration/approvals/*.received.html'

  clean-enseignes:
    desc: Clean other compagny than {{.ENSEIGNE}}
    cmds:
      - 'find enseigne -mindepth 1 -maxdepth 1 ! -name "{{.ENSEIGNE}}" -print'

  cs-fixer:
      desc: Clean code with php cs fixer
      cmds:
          - '{{.DOCKER_EXEC}} PHP_CS_FIXER_IGNORE_ENV=1 php ./vendor/bin/php-cs-fixer fix'
  
  new-enseigne:
    desc: Create a new enseigne
    vars:
        NEWENSEIGNE: "normandiepharma"  # Valeur par défaut de la variable "name"
    cmds:
        - |
            if [ -z "{{.NEWENSEIGNE}}" ]; then
              echo "Error: NEWENSEIGNE is empty!"
              exit 1
            fi
        - mkdir -p enseigne/{{.NEWENSEIGNE}}
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/config
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/config/packages/
        - cp -n config/packages/meteo_concept_h_captcha.yaml enseigne/{{.NEWENSEIGNE}}/config/packages/meteo_concept_h_captcha.yaml
        - sed -i 's/10000000-ffff-ffff-ffff-000000000001/XXX-A-REMPLACER-PAR-UN-SITE-KEY-VALIDE/g' enseigne/{{.NEWENSEIGNE}}/config/packages/meteo_concept_h_captcha.yaml
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/assets
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/assets/images
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/assets/css
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/src
        - touch enseigne/{{.NEWENSEIGNE}}/src/.gitkeep
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/services
        - mkdir -p enseigne/{{.NEWENSEIGNE}}/templates
        - sed -i 's/ENSEIGNE="[A-Za-z]*"/ENSEIGNE="{{.NEWENSEIGNE}}"/g' .env
        - sed -i "s/ENSEIGNE:\ '[A-Za-z]*'/ENSEIGNE:\ '{{.NEWENSEIGNE}}'/g" Taskfile.yml
        - cp -n enseigne/normandiepharma/phpunit.xml.dist enseigne/{{.NEWENSEIGNE}}/phpunit.xml.dist
        - sed -i 's/name=\"ENSEIGNE\" value=\"normandiepharma\"/name="ENSEIGNE" value=\"{{.NEWENSEIGNE}}\"/g' enseigne/{{.NEWENSEIGNE}}/phpunit.xml.dist
        - sed -i 's/name=\"PROGRAMME_AQUITEM_KEY\" value=\"normandiepharmafakekey\"/name="ENSEIGNE" value=\"{{.NEWENSEIGNE}}fakekey\"/g' enseigne/{{.NEWENSEIGNE}}/phpunit.xml.dist
        - cp -n config/parametersClient.yml enseigne/{{.NEWENSEIGNE}}/config/parametersClient.yml
