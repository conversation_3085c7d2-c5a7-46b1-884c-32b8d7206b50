{% extends 'client_activate/layout.html.twig' %}

{% block body %}
    {{ parent() }}

	<div class="container-fluid">
		<div class="row">
			<div class="column large-12 small-12">
				{% include 'Common/messages.html.twig' %}
				{{ form_errors(form) }}
				<div class="column large-12 small-12 activation">
					<div class="activation-puces">
						<div class="activation-puce activation-puce-active">
							01
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce">
							02
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce">
							03
						</div>
					</div>
					<div  class="centered">
						<h1>{{ 'activation.title' | trans | raw}}</h1>
					</div>
					<div class="activation-line"></div>
					<div class="activation-etape">
						{{ 'activation.activation1' | trans }}
					</div>
					<div class="row">
						<p class="column large-5 large-centered small-12 small-centered activation-etape1-text">	
							{{ 'activation.enregistrer' | trans }}
						</p>
					</div>
					<div class="row">
						<div class="large-5 small-12 activationForm1">
							{{ form_start(form, {method:'post'}) }}
							<div class="row">
								<div class="column medium-5 small-12">
									<span class=""><label for="clientActivate_codeCarte" class="required">{{'activation.codeCarte' | trans | raw }}</label></span>
								</div>
								<div class="column medium-7 small-12">
									{{ form_widget(form.codeCarte) }}
								</div>
							</div>
							<div class="row">
								<div class="column medium-5 small-12">
									<span class=""><label for="clientActivate_codeAcces" class="required">{{'activation.codeAcces' | trans | raw }}</label></span>
								</div>
								<div class="column medium-7 small-12">
									{{ form_widget(form.codeAcces) }}
								</div>
							</div>
							<div class="formatDate">
								{{ 'formatDate'|trans }}
							</div>
							{{ form_errors(form.captcha) }}
							<div class="row">
								<div class="column medium-5 small-12 labelCaptcha">
									<span class=""><label for="clientActivate_captcha" class="required">{{ 'login.formatCaptcha'|trans }} <br> <span class="ralewayBold label-captcha">{{ form_label(form.captcha, label_captcha) }}</span></label></span>
									
								</div>
								<div class="column medium-7 small-12">
									{{ form_widget(form.captcha) }}
								</div>
							</div>
							<div class="mtl small-mbl text-center">
								{{ form_end(form) }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}