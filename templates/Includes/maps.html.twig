<div class="bubble-container is-two-third">
	<div class="bubble bubble--map">
        {% if client.magasin %}
            {# <script src='https://maps.googleapis.com/maps/api/js?v=3.exp&key=AIzaSyDtsnA2jIoKA_EoL8RysnTuJFT-r55Sqas&callback=init_map'></script><div id="map" style='overflow:hidden;border-radius: 5px;height:400px;width:100%;'><div id='gmap_canvas' style='height:400px;width:100%;'></div><style>#gmap_canvas img{max-width:none!important;background:none!important}</style></div><script type='text/javascript'>function init_map(){var myOptions = {zoom:10,center:new google.maps.LatLng({{client.magasin.localisation}}),mapTypeId: google.maps.MapTypeId.ROADMAP};map = new google.maps.Map(document.getElementById('gmap_canvas'), myOptions);marker = new google.maps.Marker({map: map,position: new google.maps.LatLng({{client.magasin.localisation}})});infowindow = new google.maps.InfoWindow({content:'<strong>{{client.magasin.libelle}}</strong><br>{{client.magasin.ville}}<br><a class="underline" target="_blank" href="http://www.google.com/maps/place/{{client.magasin.localisation}}">Voir dans maps</a>'});google.maps.event.addListener(marker, 'click', function(){infowindow.open(map,marker);});infowindow.open(map,marker);}google.maps.event.addDomListener(window, 'load', init_map);</script> #}
            <div id="map" data-lat="{{client.magasin.latitude}}" data-long="{{client.magasin.longitude}}" data-nom-mag="{{client.magasin.libelle}}"></map>
        {% endif %}
    </div>
</div>
