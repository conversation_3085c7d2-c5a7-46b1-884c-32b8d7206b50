{% if featureChecker.hasChequeAnniversaire %}
<div class="bubble-container is-third">
	<div class="bloc-icon">
		<i class="fa fa-birthday-cake" aria-hidden="true"></i>
	</div>
	<div class="titreBubble">
		<p class="center mtm">{{ 'anniversaire.remiseAnniversaire' |trans }}</p>
	</div>
	<div class="bubble">
		{% if client.dateDernierChequeAnniv is empty %}
			{{ 'anniversaire.aucunCheque'|trans }}
		{% else %}
			<span class="black">{{ 'anniversaire.dernierChequeEmisLe'|trans }}</span> {{ client.dateDernierChequeAnniv|date("d/m/Y") }}<br>
			<span class="black">{{ 'avantage.dUnMontantDe'|trans }}</span>
			{% if getParam('remise.anniversaire.pourcentageAllow') %}
				{% if client.montantDernierChequeAnniv > 0 %}
					{{ client.montantDernierChequeAnniv }}<sup>€</sup>
				{% else %}
					{% if client.pourcentageDernierChequeAnniv > 0 %}
						-{{ client.pourcentageDernierChequeAnniv }}<sup>%</sup>
					{% else %}
						{{ client.montantDernierChequeAnniv }}<sup>€</sup>
					{% endif %}
				{% endif %}
			{% else %}
				{{ client.montantDernierChequeAnniv }}<sup>€</sup>
			{% endif %}
			<br><span style="white-space:nowrap;">(N°{{ client.NumDernierChequeAnniv }})</span><br>
			{% if client.dateFinValiditeDernierChequeAnniv %}
				<span class="black">{{ 'avantage.dateDeValidite'|trans }}</span> {{ client.dateFinValiditeDernierChequeAnniv|date("d/m/Y") }}
			{% endif %}
			{% if client.dateEncaissementDernierChequeAnniv is empty %}
                {% if featureChecker.hasChequeAnniversaire
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Anniversaire.name]
                %}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'anniv': 'true', 'bienvenue':'false'} ) }}" class="btn btn-default btn-telecharger ralewayBold"><i class="fa fa-download fa-lg mrs" aria-hidden="true"></i>{{ 'anniversaire.telecharger'|trans }}</a>
				{% endif %}
			{% else %}
				<span class="ralewayBold offreUtilisee">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncaissementDernierChequeAnniv|date("d/m/Y") }}</span><br><br>
			{% endif %}
		{% endif %}
	</div>
</div>
{% endif %}
