<div class="bubble-container historique" data-block-id="historique">
    <p class="center titreBubble">
        <span class="ralewayBold">
            {{ 'menu.monHistorique'|trans }}
        </span>
    </p>

    <div class="bubble bubble--white">
        {% if client.mouvementPoints|length %}
            <ul class="historique-list is-flex is-flex-column">
            {% for mouvementPoint in client.mouvementPoints %}
                <li class="is-flex">
                    <div>
                        {% if mouvementPoint.isPositif() %}
                            {{ 'client.historiqueLabel'|trans|raw }}
                        {% else %}
                            {{ 'client.historiqueLabelNegatif'|trans|raw }}
                        {% endif %}
                    </div>
                    <div class="historique-points strong">
                        {% if cagnotteTypeSymbole %}
                            {{ mouvementPoint.nbPoints|cagnotte_format(cagnotteFormater) }} {{ cagnotteTypeSymbole|trans({'%count%': mouvementPoint.nbPoints}) }}
                        {% else %}
                            {{ mouvementPoint.nbPoints|cagnotte_format(cagnotteFormater) }} {{ cagnotteType|trans({'%count%': mouvementPoint.nbPoints}) }}
                        {% endif %}
                    </div>
                    <div class="">
                        {{ mouvementPoint.dateOperation|date("d/m/Y") }}
                    </div>
                </li>
            {% endfor %}
            </ul>
        {% else %}
            <div class="notice">{{ 'client.journalPoint.noJournalPoint' | trans }}</div>
        {% endif %}
    </div>
</div>
