{% if featureChecker.hasChequeBienvenue %}
<div class="bubble-container is-third">
	<div class="bloc-icon">
		<i class="fa fa-gift" aria-hidden="true"></i>
	</div>
	<div class="titreBubble">
		<p class="center mtm">{{ 'bienvenue.remiseBienvenue' |trans }}</p>
	</div>
	<div class="bubble">
		{% if client.dateDernierChequeBvnu is empty %}
			{{ 'bienvenue.aucunCheque'|trans }}
		{% else %}
			<span class="black">{{ 'bienvenue.dernierChequeEmisLe'|trans }}</span> {{ client.dateDernierChequeBvnu|date("d/m/Y") }}<br>
			<span class="black">{{ 'avantage.dUnMontantDe'|trans }}</span>
			{% if getParam('remise.bienvenue.pourcentageAllow') %}
				{% if client.montantDernierChequeBvnu > 0 %}
					{{ client.montantDernierChequeBvnu }}<sup>€</sup>
				{% else %}
					{% if client.pourcentageDernierChequeBvnu > 0 %}
						-{{ client.pourcentageDernierChequeBvnu }}<sup>%</sup>
					{% else %}
						{{ client.montantDernierChequeBvnu }}<sup>€</sup>
					{% endif %}
				{% endif %}
			{% else %}
				{{ client.montantDernierChequeBvnu }}<sup>€</sup>
			{% endif %}
			<br><span style="white-space:nowrap;">(N°{{ client.numDernierChequeBvnu }})</span><br>
			{% if client.dateFinValiditeDernierChequeBvnu %}
				<span class="black">{{ 'avantage.dateDeValidite'|trans }}</span> {{ client.dateFinValiditeDernierChequeBvnu|date("d/m/Y") }}
			{% endif %}
			{% if client.dateEncasDernierChequeBvnu is empty %}
                {% if featureChecker.hasChequeBienvenue
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Bienvenue.name]
                %}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'anniv': 'false', 'bienvenue':'true'} ) }}" class="btn btn-default btn-telecharger ralewayBold"><span class="glyphicon glyphicon-download-alt">&nbsp;</span>{{ 'bienvenue.telechargerMonCheque'|trans }}</a>
				{% endif %}
			{% else %}
				<span class="ralewayBold offreUtilisee">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncasDernierChequeBvnu|date("d/m/Y") }}</span><br><br>
			{% endif %}
		{% endif %}
	</div>
</div>
{% endif %}
