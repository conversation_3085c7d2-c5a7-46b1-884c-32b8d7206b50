{% if featureChecker.hasChequeFidelite %}
<div class="bubble-container is-third">
	<div class="bloc-icon">
		<i class="fa fa-tag" aria-hidden="true"></i>
	</div>
	<div class="titreBubble">
		<p class="center mtm">{{ 'fidelite.remiseCheque' |trans }}</p>
	</div>
	<div class="bubble">
		{% if client.dateDernierCheque is empty %}
			{{ 'cheque.aucunChequeFidelite'|trans }}
		{% else %}
			<span class="black">{{ 'avantage.dernierChequeFideliteEmisLe'|trans }}</span> {{ client.dateDernierCheque|date("d/m/Y") }}<br>
			<span class="black">{{ 'avantage.dUnMontantDe'|trans }}</span>
				{{ client.montantDernierCheque }}<sup>€</sup>
			<span style="white-space:nowrap;">(N°{{ client.NumDernierCheque }})</span><br>
			{% if client.dateFinValiditeDernierCheque is not empty %}
				<span class="black">{{ 'avantage.dateDeValidite'|trans }}</span> {{ client.dateFinValiditeDernierCheque|date("d/m/Y") }}
			{% endif %}
			{% if client.dateEncaissementDernierCheque is empty %}
                {% if featureChecker.hasChequeFidelite
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Fidelite.value]
                %}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'anniv': 'false'} ) }}" class="btn btn-default btn-telecharger ralewayBold"><span class="glyphicon glyphicon-download-alt">&nbsp;</span>{{ 'anniversaire.telechargerMonCheque'|trans }}</a> #}
				{% endif %}
			{% else %}
				<span class="ralewayBold offreUtilisee">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncaissementDernierCheque|date("d/m/Y") }}</span><br>
			{% endif %}
		{% endif %}
		<br>
		{%  if cagnotteReste != 0 %}
			{{ 'client.ilVousManque' |trans}} <span class="ralewayBold">{{ cagnotteReste|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotteReste})}}</span> {{ 'client.pourRecevoirVotreProchaineChequeFid' |trans}}
		{% else %}
			{{ 'client.zeroptsrestant'|trans }}
		{% endif %}
	</div>
</div>
{% endif %}
