{% set notices = app.session.flashbag.get('notice') %}
{% if notices | length %}
    {% for flashMessage in notices %}
        <div class="callout primary" data-closable>
            <button class="close-button" aria-label="Dismiss alert" type="button" data-close>
                <span aria-hidden="true">&times;</span>
            </button>
            {{ flashMessage }}
        </div>
    {% endfor %}
{% endif %}
{% set successes = app.session.flashbag.get('success') %}
{% if successes | length %}
    {% for flashMessage in successes %}
        <div class="callout success" data-closable>
            <button class="close-button" aria-label="Dismiss alert" type="button" data-close>
                <span aria-hidden="true">&times;</span>
            </button>
            {{ flashMessage }}
        </div>
    {% endfor %}
{% endif %}
{% set warnings = app.session.flashbag.get('warning') %}
{% if warnings | length %}
    {% for flashMessage in warnings %}
        <div class="callout warning" data-closable>
            <button class="close-button" aria-label="Dismiss alert" type="button" data-close>
                <span aria-hidden="true">&times;</span>
            </button>
            {{ flashMessage }}
        </div>
    {% endfor %}
{% endif %}
{% set errors = app.session.flashbag.get('error') %}
{% if errors | length %}
    {% for flashMessage in errors %}
        <div class="callout alert" data-closable>
            <button class="close-button" aria-label="Dismiss alert" type="button" data-close>
                <span aria-hidden="true">&times;</span>
            </button>
            {{ flashMessage }}
        </div>
    {% endfor %}
{% endif %}