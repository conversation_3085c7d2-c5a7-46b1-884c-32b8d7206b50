{# templates/data_collector/template.html.twig #}
{% extends '@WebProfiler/Profiler/layout.html.twig' %}

{% block toolbar %}
    {% set icon %}
        {# this is the content displayed as a panel in the toolbar #}
        <svg version="1.1" id="Calque_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
             x="0px" y="0px"
             viewBox="0 0 100 55" style="enable-background:new 0 0 100 55;" xml:space="preserve">
            <style type="text/css">
                .st0 {
                    fill: #FFFFFF;
                }
            </style>
            <g>
                <g>
                    <path class="st0" d="M3.48,45.64l-0.77,1.81c-0.11,0.27-0.32,0.65-0.32,0.84c0,0.16,0.11,0.25,0.38,0.25v0.27H0v-0.27
			c0.38,0,0.47-0.07,0.93-1.06l4.09-9.12c0.23-0.52,0.36-0.72,0.36-0.88c0-0.14-0.18-0.27-0.41-0.27v-0.27h3.3v0.27
			c-0.27,0-0.36,0.09-0.36,0.27c0,0.18,0.16,0.43,0.41,0.95l4.34,8.99c0.52,1.06,0.54,1.13,1.06,1.13v0.27h-3.14v-0.27
			c0.36,0,0.45-0.11,0.45-0.32c0-0.14-0.05-0.23-0.29-0.77l-0.86-1.81H3.48z M4.2,44.15h4.9l-2.55-5.33L4.2,44.15z"/>
                    <path class="st0" d="M26.31,50.81l-0.14-0.16c0.16-0.14,0.14-0.27,0.14-0.36c0-0.16-0.29-0.65-1.33-1.69
			c-0.72,0.23-1.56,0.34-2.44,0.34c-3.91,0-6.8-2.17-6.8-6.07c0-3.91,2.89-6.07,6.8-6.07s6.8,2.17,6.8,6.07
			c0,2.35-1.04,4.06-2.76,5.06c0.7,0.72,0.99,1.15,1.35,1.15c0.07,0,0.14-0.02,0.2-0.09l0.18,0.18L26.31,50.81z M25.43,46.7
			c1.22-0.75,1.92-2.08,1.92-3.84c0-2.82-1.78-4.54-4.81-4.54s-4.81,1.72-4.81,4.54c0,2.82,1.78,4.54,4.81,4.54
			c0.43,0,0.84-0.04,1.2-0.11c-1.24-1.13-1.35-1.42-2.19-1.42c-0.14,0-0.23,0.05-0.32,0.05c-0.14,0-0.2-0.05-0.2-0.16
			c0-0.32,0.5-0.61,1.38-0.61C23.78,45.14,24.59,45.82,25.43,46.7z"/>
                    <path class="st0" d="M33.13,37.73c0-0.32-0.11-0.54-0.54-0.54v-0.27h2.98v0.27c-0.43,0-0.54,0.23-0.54,0.54v7.23
			c0,1.69,1.33,2.35,3.82,2.35c2.48,0,3.82-0.65,3.82-2.35v-7.23c0-0.32-0.11-0.54-0.54-0.54v-0.27h2.98v0.27
			c-0.43,0-0.54,0.23-0.54,0.54v6.73c0,3.57-2.17,4.47-5.71,4.47c-3.55,0-5.71-0.9-5.71-4.47V37.73z"/>
                    <path class="st0" d="M49.25,48.8v-0.27c0.43,0,0.54-0.23,0.54-0.54V37.73c0-0.32-0.11-0.54-0.54-0.54v-0.27h2.98v0.27
			c-0.43,0-0.54,0.23-0.54,0.54v10.25c0,0.32,0.11,0.54,0.54,0.54v0.27H49.25z"/>
                    <path class="st0" d="M59.91,48.8v-0.27c0.43,0,0.54-0.23,0.54-0.54v-9.57h-3.79c-0.56,0-0.79,0.14-0.79,0.59H55.6v-2.66h0.27
			c0,0.38,0.27,0.59,0.79,0.59h9.48c0.52,0,0.79-0.2,0.79-0.59h0.27V39h-0.27c0-0.45-0.23-0.59-0.79-0.59h-3.79v9.57
			c0,0.32,0.11,0.54,0.54,0.54v0.27H59.91z"/>
                    <path class="st0" d="M71.07,37.73c0-0.32-0.11-0.54-0.54-0.54v-0.27h9.46c0.77,0,0.9-0.11,0.9-0.45h0.27v2.57h-0.27
			c0-0.38-0.2-0.63-0.88-0.63h-7.05v3.57h4.58c0.54,0,0.93-0.16,0.93-0.63h0.27v2.73h-0.27c0-0.23-0.07-0.38-0.2-0.47
			c-0.14-0.09-0.36-0.14-0.68-0.14h-4.63v3.84h7.54c0.63,0,0.93-0.2,0.93-0.77h0.27v2.76h-0.27c0-0.36-0.23-0.5-0.79-0.5H70.53
			v-0.27c0.43,0,0.54-0.23,0.54-0.54V37.73z"/>
                    <path class="st0" d="M85.59,48.8v-0.27c0.47,0,0.59-0.23,0.59-0.54V37.73c0-0.32-0.11-0.54-0.63-0.54v-0.27h3.82v0.27
			c-0.38,0-0.52,0.14-0.52,0.29c0,0.09,0.05,0.18,0.09,0.25l3.68,7.23l3.82-7.23c0.02-0.09,0.09-0.18,0.09-0.27
			c0-0.14-0.16-0.27-0.57-0.27v-0.27H100v0.27c-0.52,0-0.63,0.23-0.63,0.54v10.25c0,0.32,0.11,0.54,0.63,0.54v0.27h-3.16v-0.27
			c0.52,0,0.63-0.23,0.63-0.54v-8.92l-0.07-0.05l-4.81,9.55l-4.81-9.55l-0.02,0.05v8.92c-0.02,0.32,0.09,0.54,0.52,0.54v0.27H85.59z
			"/>
                </g>
                <path class="st0" d="M60.12,16.22c-1.12-0.86-2.52-1.38-4.04-1.38H45.43c-2.23,0-4.04-1.81-4.04-4.04c0-2.23,1.81-4.04,4.04-4.04
		h10.65c2.23,0,4.04,1.81,4.04,4.04V16.22z M56.08,25.49H45.43c-2.23,0-4.04-1.81-4.04-4.04v-5.42c1.12,0.86,2.52,1.38,4.04,1.38
		h10.65c2.23,0,4.04,1.81,4.04,4.04C60.12,23.68,58.31,25.49,56.08,25.49 M56.08,4.19H45.43c-3.65,0-6.61,2.96-6.61,6.61v10.65
		c0,3.65,2.96,6.61,6.61,6.61h10.65c3.65,0,6.61-2.96,6.61-6.61V10.8C62.69,7.15,59.73,4.19,56.08,4.19"/>
            </g>
        </svg>

        <span class="sf-toolbar-value">
            {% if collector.isEmpty %}
                Enseigne non configurée
            {% else %}
                {{ collector.enseigne }}
            {% endif %}
        </span>
    {% endset %}

    {% set text %}
        {# this is the content displayed when hovering the mouse over
           the toolbar panel #}
        <div class="sf-toolbar-info-piece">
            <b>Enseigne</b>
            <span>
            {% if collector.isEmpty %}
                Enseigne non configurée
            {% else %}
                {{ collector.enseigne }}
            {% endif %}
            </span>
        </div>

        <div class="sf-toolbar-info-piece">
            <b>Fichier</b>
            <span>.env(.local)</span>
        </div>
    {% endset %}

    {# the 'link' value set to 'false' means that this panel doesn't
       show a section in the web profiler #}
    {% if collector.isEmpty %}
        {% set status = 'red' %}
    {% else %}
        {% set status = 'green' %}
    {% endif %}

    {{ include('@WebProfiler/Profiler/toolbar_item.html.twig', { link: false, status: status }) }}
{% endblock %}
