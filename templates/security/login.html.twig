{% extends 'home.html.twig' %}
{% form_theme form '/Form/fields.html.twig' %}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="imgTop">
		<div class="container-fluid">
			<div class="row">
				<div class="column large-5 small-12">
					<div id="divBienvenue" class="col-bienvenu">
						<div class="column small-12 medium-4">
							<img src="{{asset('images/' ~ getParam('logoClient'))}}" alt="">
						</div>
						<div class="column small-12 medium-8">
							<div id="bienvenue"><span class="ralewayBold">{{ 'login.bienvenue'|trans }}</span><hr></div>
							<p>{{ 'login.soustitre' | trans | raw }}</p>
						</div>
					</div>
				</div>
				<div class="column large-7 small-12">
					{{ include('/Common/messages.html.twig') }}
					{% if errors %}
						<div class="callout alert" data-closable>
							<button class="close-button" aria-label="Dismiss alert" type="button" data-close>
								<span aria-hidden="true">&times;</span>
							</button>
							{{ errors.messageKey|trans(errors.messageData, 'security') }}
						</div>
					{% endif %}
					{{ form_errors(form) }}
					<div class="banner-optin vertical login formLogin">
						<h1><span class="ralewayBold">{{ 'login.mainTitle.first'|trans }}</span>{{ 'login.mainTitle.last'|trans }}
						</h1>
						<div class="row">
							<div class="column small-12 medium-6">
								{{ form_start(form, {method:'post'}) }}
								<p>{{ 'login.libelleForm'|trans }}<p>
								{{ form_row(form._username)}}
								{{ form_row(form._password)}}
								<div class="formatDate">
									{{ 'formatDate'|trans }}
								</div>
                                {%  if form.captcha is defined %}
                                    {{ form_errors(form.captcha) }}
                                    <p>
                                        {{ 'login.formatCaptcha'|trans }}
                                    </p>
                                    <div class="row">
                                        <div class="column medium-3 small-12 labelCaptcha">
                                            <span class="ralewayBold label-captcha">{{ form_label(form.captcha) }}</span>
                                        </div>
                                        <div class="column medium-9 small-12">
                                            {{ form_widget(form.captcha) }}
                                        </div>
                                    </div>
                                {% endif %}
								<div class="text-right mtl small-mbl">
								    <input type="hidden" name="_token" value="{{ csrf_token('authenticate') }}"/>

									{{ form_end(form) }}
								</div>
							</div>
						</div>
					</div>
					{% if featureChecker.hasActivation() %}
					<div class="banner-optin vertical moncompte">
						<h1>
							<span class="ralewayBold">
								{{ 'login.moncompte.title.first'|trans }}
							</span>
							{{ 'login.moncompte.title.last'|trans }}
						</h1>
						<div class="row">
							<div class="column small-12 medium-6">
								<p>{{ 'login.moncompte.texte'|trans }}</p>
							</div>
							<div class="column small-12 medium-6">
								<div class="activation">
									<img src="{{asset('images/cartes/carte_login.png')}}" alt="">
									<a href="{{path('activate')}}">
										<span class="btn">{{ 'login.moncompte.btnCarte'|trans }}</span>
									</a>
								</div>
							</div>
						</div>
					</div>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
		<div class="row">
		{% include '/footer.html.twig' %}
		</div>
	{% endif %}
{% endblock %}
