{% extends 'home.html.twig' %}
{% form_theme formCompte 'Form/fields.html.twig' %}
{% set montantFidelite = 8%}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="banniere banniere-activation"></div>
	<div class="pageClient" id="accueil">
		{% include 'home/menu.html.twig' %}
		<div id="maMagasin">
			<div class="row">
				{% include 'Common/messages.html.twig' %}
				{# <div class="{{getColSize(menuLateral,{colXs:12,colLg:8,colLgOffset:4})}}"> #}
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Mon' |trans }} <span class="ralewayBold">{{ 'menu.magasin' |trans }}</span></h2>
					<div class="separator"></div>
					{% if not menuLateral %}
						<div class="column large-4 {% if responsive %}hide-for-small-only{% endif %}">
							<a href="{{path('logout')}}" class="pull-right btn btn-default btn-deconnexion margin-top-40">
								<i class="far fa-times-circle fa-lg mrs" aria-hidden="true"></i>{{'client.deconnexion'|trans}}
							</a>
						</div>
					{% endif %}
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{% if getParam('map') %}
						<div class="bubble-wrapper">
							{% include "Includes/monmagasin.html.twig" %}
							{% include "Includes/maps.html.twig" %}
						</div>
					{% endif %}
				</div>
			</div>
		</div>
		<div id="maCarte" class="blocBackground2">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Ma' |trans }} <span class="ralewayBold">{{ 'menu.carte' |trans }}</span></h2>
					<div class="separator"></div>
					<p class="sous-titre">{{ 'client.capSurVosAvantages' |trans}}</p>
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<div class="bubble-wrapper">
						{% include "Includes/macarte.html.twig" %}
						{% if getParam('carte.passbook') %}
							{% include "Includes/passbook.html.twig" %}
						{% endif %}
						{% include "Includes/mespoints.html.twig" %}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<div class="bubble-wrapper">
						{% include "Includes/historique.html.twig" %}
					</div>
				</div>
			</div>
		</div>
		<div id="monCompte" class="blocBackground1" data-block-id="monCompte">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{{ form_start(formCompte, {'action': path('home_vue'),'method': 'POST'}) }}
						<h2>{{ 'menu.Mon' |trans}} <span class="ralewayBold">{{ 'menu.Compte' |trans}}</span></h2>
						<div class="separator"></div>
						<p class="sous-titre">{{ 'compte.pensezAVerifier' |trans}}</p>
					<div class="fieldsetWith">
						<div class="row">
							<div class="column small-12 medium-6 first">
								<div class="checked">
									<i class="fa fa-arrow-circle-right form-icon mrs"></i>
									<span>
									{{ client.civilite }} {{ client.prenom }} {{ client.nom }}
									</span>
								</div>
								<div class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.dateNaissance) }}</span>
									</div>
									<div class="form-group form-group--margin">
										{{ form_widget(formCompte.dateNaissance) }}
									</div>
								</div>
								<div id="adresse" class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ 'compte.Adresse'|trans }}</span>
									</div>
									<div class="form-group form-group--margin">
										{{ form_row(formCompte.numero)}}
										{{ form_row(formCompte.voie)}}
										{{ form_row(formCompte.escalier)}}
										{{ form_row(formCompte.batiment)}}
										{{ form_row(formCompte.lieuDit)}}
										{{ form_row(formCompte.codepostal)}}
										{{ form_row(formCompte.ville)}}
										{{ form_row(formCompte.codePaysClient)}}
									</div>
								</div>
							</div>
							<div class="column small-12 medium-6 ">
								<div class="checked mobile {% if client.telephoneMobile is empty %}emptyInput{% endif %}">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.telephoneMobile)}}
										<div class="has-error">{{ form_errors(formCompte.telephoneMobile)}}</div></span>
									</div>
									<div class="form-group form-group--margin">{{ form_widget(formCompte.telephoneMobile)}}</div>
								</div>
								<div class="checked {% if client.email is empty %}emptyInput{% endif %}">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.email)}}
										<div class="has-error">{{ form_errors(formCompte.email)}}</div></span>
									</div>
									<div class="form-group form-group--margin">{{ form_widget(formCompte.email)}}</div>
								</div>
							</div>
						</div>
						<div class="row submit">
							<button type="submit" class="btn btn-default btn-valider-modif ralewayBold mlxl"><i class="fas fa-pen-square fa-lg mrs" aria-hidden="true"></i>{{'client.save'|trans}}</button>
						</div>
						<p class="mentions-legales">
						* mentions obligatoires <br>
							RGPD Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum porta molestie quam. Donec vulputate maximus euismod. In ut convallis velit. Sed vestibulum lectus sit amet rutrum fermentum. Vivamus neque odio, feugiat in tellus eu, fringilla euismod ipsum. Praesent a nunc et ante rutrum accumsan vitae non orci. Pellentesque eget quam eu enim suscipit auctor. Maecenas egestas urna aliquet pulvinar luctus. Ut suscipit eu augue eget dictum. Sed tortor risus, laoreet eu tincidunt non, consequat mattis orci. Nulla aliquam posuere turpis at cursus. Vestibulum at maximus erat. Suspendisse hendrerit sit amet diam ac lobortis.
						</p>
					</div>
					{{ form_end(formCompte) }}
				</div>
			</div>
		</div>
		<div id="mesAvantages">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Mes' |trans}} <span class="ralewayBold">{{ 'menu.Avantages' |trans}}</span></h2>
					<div class="separator"></div>
					<div class="is-flex bubble-wrapper">
						{# Bienvenue #}
                        {% include "Includes/blocBienvenue.html.twig" %}
						{# Anniversaire #}
                        {% include "Includes/blocAnniversaire.html.twig" %}
						{# Fidélité #}
                        {% include "Includes/blocFidelite.html.twig" %}
					</div>
				</div>
			</div>
		</div>
		<div id="monProgramme" class="blocBackground1">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Mon' |trans}} <span class="ralewayBold">{{ 'menu.Programme' |trans}}</span></h2>
					<div class="separator"></div>
					<p class="sous-titre">{{ 'programme.soustitre' |trans}}</p>
					<div class="is-flex-wrap">
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<div class="bloc-icon">
								<i class="fa fa-gift" aria-hidden="true"></i>
							</div>
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">{{ 'programme.block1.titre'|trans|raw }}</span>
								</div>
								<p>{{ 'programme.block1.contenu'|trans|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<div class="bloc-icon ralewayBold">
								5<sup>€</sup>
							</div>
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block2.titre'|trans({'%montant%':montantFidelite})|raw }}
									</span>
								</div>
								<p>{{ 'programme.block2.contenu'|trans({'%montant%':montantFidelite})|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<div class="bloc-icon">
								<i class="fa fa-star" aria-hidden="true"></i>
							</div>
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block3.titre'|trans|raw }}
									</span>
								</div>
								<p>{{ 'programme.block3.contenu'|trans|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<div class="bloc-icon">
								<i class="far fa-heart" aria-hidden="true"></i>
							</div>
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block4.titre'|trans|raw }}
									</span>
								</div>
								<p>{{ 'programme.block4.contenu'|trans|raw }}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
	<div class="row">
		<div class="{{getColSize(menuLateral)}}">
			{% include '/footer.html.twig' %}
		</div>
	</div>
	{% endif %}
{% endblock %}
{% block javascripts %}
	{% if getParam('carte.donuts') %}
		<script type="text/javascript" src="{{asset('js/d3.min.js')}}"></script>
	{% endif %}

	<script type="text/javascript">
		function verifentier(champs) {
		    var PxTmp = parseInt(champs.val(), 10);
		    if (isNaN(PxTmp)){
		        PxTmp = '';
			}
		    champs.val(PxTmp);
		    return PxTmp !='';
		}

		$(document).ready(function() {
			$('#client_telephoneMobile').blur(function(){
				$('#client_envoieSmsInterne').prop('checked', true);
			});
			$('#client_email').blur(function(){
				$('#client_envoieEmailInterne').prop('checked', true);
			});
			{% if getParam('carte.donuts') %}
				var color = null;
				var dataset = null;
				if ($('#donuts').length) {
					if ({{ cagnotte }} != 0 && {{ cagnotteReste }} != 0) {
						var colorDonus = "#f0be38";
						color = d3.scale.ordinal().range([colorDonus, '#ffffff', '#cccccc', '#ffffff']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}) * 50, 2,{{ cagnotteReste }} * 50, 2]
						};
					}
					else {
						color = d3.scale.ordinal().range(['#004b95', '#f0be38']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}),{{ cagnotteReste }} ]
						};
					}

					var width = 100,
							height = 100,
							radius = Math.min(width, height) / 2;
					var pie = d3.layout.pie()
							.sort(null);
					var arc = d3.svg.arc()
							.innerRadius(radius - 17)
							.outerRadius(radius);
					var svg = d3.select("#donuts").append("svg")
							.attr("width", width)
							.attr("height", height)
							.append("g")
							.attr("transform", "translate(" + width / 2 + "," + height / 2 + ")");
					var path = svg.selectAll("path")
							.data(pie(dataset.apples))
							.enter().append("path")
							.attr("fill", function(d, n) {
								return color(n);
							})
							.attr("d", arc);
				}
			{% endif %}
		});
	</script>
{% endblock %}
