SUDO=sudo -u www-data
CONSOLE=php bin/console
BRANCH=zefid-client-generique
<PERSON>=

init: git-clone install

install: git-subup composer-install cache-clear assets cache-clear

update-pull: git-update-pull cache-clear assets cache-clear

update: git-update cache-clear assets cache-clear



### GIT ###
git-clone:
	$(SUDO) <NAME_EMAIL>:dev-projets-aquitem/zefid_portail_client.git  ./repertoireTmpCopie -b $(BRANCH)
	$(SUDO) cp -rf repertoireTmpCopie/* ./
	$(SUDO) cp -rf repertoireTmpCopie/.git* ./
	$(SUDO) rm -rf repertoireTmpCopie/
	$(SUDO) git checkout -b $(NEWBRANCH)

git-subup:
	$(SUDO) git submodule update --init

git-subprod:
	cp ./.gitmodules ~/
	find ~/ -name '.gitmodules' -exec sed -i 's/git-user@git\.dev\.int/git-user@git-ext/g' {} \;
	find ~/ -name '.gitmodules' -exec sed -i 's/git\.dev\.int/git-user@git-ext/g' {} \;
	$(SUDO) cp ~/.gitmodules ./
	rm ~/.gitmodules
	$(SUDO) git submodule sync

git-update:
	$(SUDO) git fetch origin
	$(SUDO) git stash
	$(SUDO) git merge origin/$(NEWBRANCH)-dev
	$(SUDO) git stash pop

git-update-pull:
	$(SUDO) git stash
	$(SUDO) git pull
	$(SUDO) git stash pop

### COMPOSER ###
composer-install:
	$(SUDO) php -d "apc.enable_cli=0" ../composer.phar install
	$(SUDO) git checkout app/config/parameters.yml

composer-update:
	$(SUDO) php -d "apc.enable_cli=0" ../composer.phar update
	$(SUDO) git checkout app/config/parameters.yml

### CONSOLE ###
cache-clear:
	$(SUDO) $(CONSOLE) cache:clear
	$(SUDO) $(CONSOLE) cache:clear --env=prod

assets:
	$(SUDO) $(CONSOLE) assets:install --symlink

sass: 
	sass --watch src/ZeFid/PortailClientBundle/Resources/scss/styles.scss:src/ZeFid/PortailClientBundle/Resources/public/css/styles.css
