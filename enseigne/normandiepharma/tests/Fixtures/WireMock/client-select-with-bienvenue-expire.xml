<?xml version="1.0" encoding="utf-8"?>
<xml>
    <etat>OK</etat>
    <resultat>
        <client CODECARTE="2902902906900" CIVILITE="M." NOM="NOM50" PRENOM="PRENOM50" CODEPOSTAL="33000" NUMERO="1"
                VOIE="RUE TEST" VILLE="BORDEAUX" DATENAISSANCE="16/07/1987" EMAIL="<EMAIL>"
                DATECREATION="21/09/2023" ENVOIESMSINTERNE="1" ENVOIEEMAILINTERNE="1" ENVOIECOURRIERINTERNE="1"
                ENVOIESMSEXTERNE="0" ENVOIEEMAILEXTERNE="0" ENVOIECOURRIEREXTERNE="1" MONTANTCART="500,00"
                CAGNOTTE="500,00" NBREPTSRESTANTCHQ="0,00" MONTANTDERNIERACHAT="0,00" DATEDERNIERCHEQUE="17/07/2024"
                MONTANTDERNIERCHEQUE="5,00" NODERNIERCHEQUE="1009100180611" DATEFINVALIDITEDERNIERCHEQUE="02/02/2025"
                DATEDERNIERCHQBIENV="01/09/2023" NODERNIERCHQBIENV="9002900000020"
                DATEFINVALIDITEDERNIERCHQBIENV="01/10/2023" MONTANTANNIVERSAIRE="0,00"
                URLWALLET="https://wallet.zefid.fr/ws/card/470000000029/2902902906900/aca8f953e0">
            <collectionEnfant/>
            <magasin SITE="0000000029" MAGASIN="999999041" LIBELLE="TEST WINPHARMA"/>
            <collectionVente/>
            <collectionCategorieArticle/>
            <collectionMouvement>
                <mouvement DATEOPERATION="17/07/2024" OPERATION="Creation test" SITE="0000000029" MAGASIN="999999041"
                           TYPEMONTANTCART="2" MONTANT="500,00"/>
            </collectionMouvement>
            <collectionCentreInteret/>
            <collectionAnimal/>
        </client>
    </resultat>
</xml>
