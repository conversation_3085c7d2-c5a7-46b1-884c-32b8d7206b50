<?php

namespace Normandie<PERSON>rma\Tests\Integration;

use App\DTO\WebserviceError;
use App\Tests\ApiTestCase;
use Normandiepharma\Services\CodeClientConverter;
use Normandiepharma\Tests\Fixtures\WireMock\MockLoader;
use WireMock\Client\WireMock;

class CodeClientConverterTest extends ApiTestCase
{
    private WireMock $wireMock;
    private CodeClientConverter $converter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->wireMock = WireMock::create('wiremock', 8080);
        $this->wireMock->reset();
        MockLoader::configureMocks($this->wireMock, self::getContainer()->getParameter('kernel.project_dir'));

        $this->converter = self::getContainer()->get(CodeClientConverter::class);
    }

    public function test_successful_conversion(): void
    {
        $this->wireMock->stubFor(
            WireMock::get(WireMock::urlPathEqualTo('/PhDCartesAlphaSelect.php'))
                ->willReturn(WireMock::aResponse()
                    ->withStatus(200)
                    ->withHeader('Content-Type', 'application/xml')
                    ->withBody(file_get_contents(__DIR__.'/../Fixtures/WireMock/code-carte-convert-successful.xml')))
        );

        $result = $this->converter->convert('2F04D030B60S4');

        $this->assertSame('2902902906900', $result);
    }

    public function test_conversion_error(): void
    {
        $this->wireMock->stubFor(
            WireMock::get(WireMock::urlPathEqualTo('/PhDCartesAlphaSelect.php'))
                ->willReturn(WireMock::aResponse()
                    ->withStatus(200)
                    ->withHeader('Content-Type', 'application/xml')
                    ->withBody(file_get_contents(__DIR__.'/../Fixtures/WireMock/code-carte-convert-failure.xml')))
        );

        $result = $this->converter->convert('BADCODE123');

        $this->assertInstanceOf(WebserviceError::class, $result);
    }
}
