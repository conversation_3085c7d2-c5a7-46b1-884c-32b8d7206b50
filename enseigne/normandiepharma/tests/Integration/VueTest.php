<?php

namespace <PERSON><PERSON><PERSON>rma\Tests\Integration;

use App\Services\NewClientsService;
use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;
use Normandiepharma\Tests\Fixtures\WireMock\MockLoader;
use <PERSON>ymfony\Component\DomCrawler\Crawler;
use WireMock\Client\WireMock;

class VueTest extends ApiTestCase
{
    private $client;
    private WireMock $wireMock;

    public function getVue(): ?Crawler
    {
        return $this->client->request('GET', '/vue/');
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
        $this->wireMock = WireMock::create('wiremock', 8080);
        $this->wireMock->reset();
        MockLoader::configureMocks($this->wireMock, self::getContainer()->getParameter('kernel.project_dir'));
        $this->loginUser();
    }

    private function loginUser(): void
    {
        $user = self::getContainer()->get(NewClientsService::class)->authenticate(MockLoader::CLIENT_1_CODE_CARTE, MockLoader::CLIENT_1_PASSWORD);
        $this->client->loginUser($user, 'secured_area');
    }

    public function test_client_resume(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'resume');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_magasin(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'magasin');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_carte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'macarte');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_passbook(): void
    {
        $crawler = $this->getVue();
        self::expectException(\InvalidArgumentException::class);
        self::expectExceptionMessage('html block passbook not found');
        $this->getHtmlBlock($crawler, 'passbook');
    }

    public function test_client_mes_points(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'mesPoints');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_historique(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'historique');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_mon_compte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'monCompte');
        $reindentedBlock = $this->scrubTokens($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }
}
