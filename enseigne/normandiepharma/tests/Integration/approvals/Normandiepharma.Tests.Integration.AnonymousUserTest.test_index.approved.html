<html>
    <head>
        <meta charset="UTF-8">
        <title><PERSON><PERSON> </title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/foundation.min.css" rel="stylesheet">
        <link rel="stylesheet" href="/assets/vendor/@fortawesome/fontawesome-free/css/all-__hash__.css">
        <link rel="stylesheet" href="/assets/css/styles-__hash__.css">
        <link rel="stylesheet" href="/assets/vendor/leaflet/dist/leaflet.min-__hash__.css">
        <script type="importmap">
{
    "imports": {
        "app": "/assets/app-__hash__.js",
        "@fortawesome/fontawesome-free/css/all.css": "data:application/javascript,",
        "/assets/css/styles.css": "data:application/javascript,",
        "leaflet/dist/leaflet.min.css": "data:application/javascript,",
        "leaflet": "/assets/vendor/leaflet/leaflet.index-__hash__.js",
        "d3": "/assets/vendor/d3/d3.index-__hash__.js",
        "/assets/js/main.js": "/assets/js/main-__hash__.js",
        "/assets/js/streetmap.js": "/assets/js/streetmap-__hash__.js",
        "d3-array": "/assets/vendor/d3-array/d3-__hash__.js",
        "d3-axis": "/assets/vendor/d3-axis/d3-__hash__.js",
        "d3-brush": "/assets/vendor/d3-brush/d3-__hash__.js",
        "d3-chord": "/assets/vendor/d3-chord/d3-__hash__.js",
        "d3-color": "/assets/vendor/d3-color/d3-__hash__.js",
        "d3-contour": "/assets/vendor/d3-contour/d3-__hash__.js",
        "d3-delaunay": "/assets/vendor/d3-delaunay/d3-__hash__.js",
        "d3-dispatch": "/assets/vendor/d3-dispatch/d3-__hash__.js",
        "d3-drag": "/assets/vendor/d3-drag/d3-__hash__.js",
        "d3-dsv": "/assets/vendor/d3-dsv/d3-__hash__.js",
        "d3-ease": "/assets/vendor/d3-ease/d3-__hash__.js",
        "d3-fetch": "/assets/vendor/d3-fetch/d3-__hash__.js",
        "d3-force": "/assets/vendor/d3-force/d3-__hash__.js",
        "d3-format": "/assets/vendor/d3-format/d3-__hash__.js",
        "d3-geo": "/assets/vendor/d3-geo/d3-__hash__.js",
        "d3-hierarchy": "/assets/vendor/d3-hierarchy/d3-__hash__.js",
        "d3-interpolate": "/assets/vendor/d3-interpolate/d3-__hash__.js",
        "d3-path": "/assets/vendor/d3-path/d3-__hash__.js",
        "d3-polygon": "/assets/vendor/d3-polygon/d3-__hash__.js",
        "d3-quadtree": "/assets/vendor/d3-quadtree/d3-__hash__.js",
        "d3-random": "/assets/vendor/d3-random/d3-__hash__.js",
        "d3-scale": "/assets/vendor/d3-scale/d3-__hash__.js",
        "d3-scale-chromatic": "/assets/vendor/d3-scale-chromatic/d3-__hash__.js",
        "d3-selection": "/assets/vendor/d3-selection/d3-__hash__.js",
        "d3-shape": "/assets/vendor/d3-shape/d3-__hash__.js",
        "d3-time": "/assets/vendor/d3-time/d3-__hash__.js",
        "d3-time-format": "/assets/vendor/d3-time-format/d3-__hash__.js",
        "d3-timer": "/assets/vendor/d3-timer/d3-__hash__.js",
        "d3-transition": "/assets/vendor/d3-transition/d3-__hash__.js",
        "d3-zoom": "/assets/vendor/d3-zoom/d3-__hash__.js",
        "internmap": "/assets/vendor/internmap/internmap.index-__hash__.js",
        "delaunator": "/assets/vendor/delaunator/delaunator.index-__hash__.js",
        "robust-predicates": "/assets/vendor/robust-predicates/robust-__hash__.js",
        "fontawesome": "/assets/vendor/fontawesome/fontawesome.index-__hash__.js",
        "stylus-type-utils": "/assets/vendor/stylus-type-utils/stylus-__hash__.js",
        "jquery": "/assets/vendor/jquery/jquery.index-__hash__.js"
    }
}
</script>
        <script>
if (!HTMLScriptElement.supports || !HTMLScriptElement.supports('importmap')) (function () {
    const script = document.createElement('script');
    script.src = 'https://ga.jspm.io/npm:es-module-shims@1.10.0/dist/es-__hash__.js';
    script.setAttribute('crossorigin', 'anonymous');
    script.setAttribute('integrity', 'sha384-ie1x72Xck445i0j4SlNJ5W5iGeL3Dpa0zD48MZopgWsjNB/lt60SuG1iduZGNnJn');
    document.head.appendChild(script);
})();
</script>
        <link rel="modulepreload" href="/assets/app-__hash__.js">
        <link rel="modulepreload" href="/assets/vendor/leaflet/leaflet.index-__hash__.js">
        <link rel="modulepreload" href="/assets/vendor/d3/d3.index-__hash__.js">
        <script type="module">import 'app';</script>
        <link rel="icon" type="image/png" href="/assets/images/favicon-VMJs1gA.png">
    </head>
    <body>
        <div class="container">
            <div class="imgTop">
                <div class="login-block">
                    <div class="banner-optin vertical login formLogin">
                        <h1>
                            <span class="ralewayBold">Votre espace</span> Normandie Pharma 
                        </h1>
                        <p class="legendFormLogin">
                            Vous avez d&eacute;j&agrave; un compte ? 
                            <br>
                            Identifiez-vous 
                        </p>
                        <form name="login" method="post">
                            <div>
                                <label class="sr-only required" for="login__username">Username<span class="asterisque">*</span></label>
                                <input type="text" id="login__username" name="login[_username]" required="required" placeholder="N&deg; de carte">
                            </div>
                            <div>
                                <label class="sr-only required" for="login__password">Password<span class="asterisque">*</span></label>
                                <input type="date" id="login__password" name="login[_password]" required="required" placeholder="Date d'anniversaire">
                            </div>
                            <div class="text-right connexion">
                                <input type="hidden" name="_token" value="csrf-token">
                                <button type="submit" id="login_submit" name="login[submit]" class="btn">Valider</button>
                            </div>
                            <input type="hidden" id="login__token" name="login[_token]" data-controller="csrf-protection" value="csrf-token">
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <footer class="footer">
                <div class="footer-top">
                    <ul>
                        <li><a href="https://www.normandiepharma.fr/fr/" target="_blank">Trouvez-nous</a></li>
                        <li><a href="/enseigne/normandiepharma/Liste%20des%20pharmacies%20participantes.pdf" target="_blank">Pharmacies participantes</a></li>
                        <li><a href="/enseigne/normandiepharma/CGU%20PROGRAMME%20DE%20FIDELITE%20NORMANDIE%20PHARMA.pdf" target="_blank">Conditions du programme</a></li>
                        <li><a href="/enseigne/normandiepharma/ML%20portail%20Normandie%20Pharma.pdf" target="_blank">Mentions l&eacute;gales</a></li>
                    </ul>
                </div>
                <div class="column small-12 medium-6">
                    <p>&copy; Normandie Pharma. Tout droits r&eacute;serv&eacute;s</p>
                </div>
                <div class="column small-12 medium-6 small-text-center medium-text-right"> <a target="_blank" href="http://www.zefid.fr" class="footer-bottom-link">www.zefid.fr</a> </div>
            </footer>
        </div>
    </body>
</html>