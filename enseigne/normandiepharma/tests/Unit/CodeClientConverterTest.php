<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Tests\Unit;

use App\DTO\WebserviceError;
use App\Serializer\ResponseDeserializerInterface;
use Normandiepharma\Services\CodeClientConverter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class CodeClientConverterTest extends TestCase
{
    private HttpClientInterface $httpClient;
    private ResponseDeserializerInterface $deserializer;
    private ParameterBagInterface $parameterBag;

    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->parameterBag = $this->createMock(ParameterBagInterface::class);
        $this->deserializer = $this->createMock(ResponseDeserializerInterface::class);
        $this->codeConverterParams = [
            'PROGRAMME' => 'fakeProgram',
            'MAGASIN' => '123',
            'salt_client' => 'fakeSalt',
        ];
    }

    public function test_throws_when_not_alphanumeric(): void
    {
        $this->expectException(CustomUserMessageAuthenticationException::class);

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );
        $converter->convert('BAD-CODE');
    }

    public function test_returns_plain_when_numeric(): void
    {
        $numericCode = '123456';
        // HttpClient should not be called
        $this->httpClient->expects($this->never())->method('request');

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $this->assertSame($numericCode, $converter->convert($numericCode));
    }

    public function test_successful_conversion(): void
    {
        $codeCarte = '2F04D030B60S4';

        $expectedToken = substr(
            md5(sprintf(
                'PROGRAMME=%s&MAGASIN=%s&ANCIENCODECARTE=%smdp=%s',
                'fakeProgram',
                '123',
                $codeCarte,
                'fakeSalt'
            )),
            9,
            10
        );

        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock
            ->method('getContent')
            ->willReturn('<irrelevant/>');

        $this->httpClient
            ->expects($this->once())
            ->method('request')
            ->with(
                'GET',
                CodeClientConverter::CODECARTE_CONVERT_ROUTE,
                ['query' => [
                    'PROGRAMME' => 'fakeProgram',
                    'MAGASIN' => '123',
                    'ANCIENCODECARTE' => $codeCarte,
                    'TOKEN' => $expectedToken,
                ]]
            )
            ->willReturn($responseMock);

        $this->deserializer
            ->expects($this->once())
            ->method('deserialize')
            ->with('<irrelevant/>')
            ->willReturn('convertedCode');

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $this->assertSame('convertedCode', $converter->convert($codeCarte));
    }

    public function test_conversion_error_loads_fixture(): void
    {
        $codeCarte = 'BADCODE123';
        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock
            ->method('getContent')
            ->willReturn('<irrelevant/>');

        $this->httpClient
            ->method('request')
            ->willReturn($responseMock);

        $errorMock = $this->createMock(WebserviceError::class);
        $this->deserializer
            ->method('deserialize')
            ->willReturn($errorMock);

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $result = $converter->convert($codeCarte);
        $this->assertSame($errorMock, $result);
    }
}
