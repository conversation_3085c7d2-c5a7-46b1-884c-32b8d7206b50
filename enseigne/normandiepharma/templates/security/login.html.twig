{% extends 'home.html.twig' %}
{% form_theme form '/Form/fields.html.twig' %}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="imgTop">
        <div class="login-block">
            {{ include('/Common/messages.html.twig') }}
            {% if errors %}
                <div class="callout alert" data-closable>
                    <button class="close-button" aria-label="Dismiss alert" type="button" data-close>
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ errors.messageKey|trans(errors.messageData, 'security') }}
                </div>
            {% endif %}
            {{ form_errors(form) }}
            <div class="banner-optin vertical login formLogin">
                <h1><span class="ralewayBold">{{ 'login.mainTitle.first'|trans }}</span> {{ 'login.mainTitle.last'|trans }}
                </h1>
                <p class="legendFormLogin">
                    Vous avez déjà un compte ?<br>
                    Identifiez-vous
                </p>
                {{ form_start(form, {method:'post'}) }}
                {{ form_row(form._username)}}
                {{ form_row(form._password)}}
                {%  if form.captcha is defined %}
                {{ form_errors(form.captcha) }}
                <div>
                    {{ form_widget(form.captcha) }}
                </div>
                {% endif %}
                <div class="text-right connexion">
                    <input type="hidden" name="_token" value="{{ csrf_token('authenticate') }}"/>
                    {{ form_widget(form.submit) }}
                </div>
                {{ form_end(form) }}
            </div>
            {% if featureChecker.hasActivation() %}
                <div class="banner-optin vertical moncompte">
                    <h1>
                    <span class="ralewayBold">
                        {{ 'login.moncompte.title.first'|trans }}
                    </span>
                        {{ 'login.moncompte.title.last'|trans }}
                    </h1>
                    <div class="row">
                        <div class="column small-12 medium-6">
                            <p>{{ 'login.moncompte.texte'|trans }}</p>
                        </div>
                        <div class="column small-12 medium-6">
                            <div class="activation">
                                <img src="{{asset('images/cartes/carte_login.png')}}" alt="">
                                <a href="{{path('activate')}}">
                                    <span class="btn">{{ 'login.moncompte.btnCarte'|trans }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
		<div class="row">
		{% include '/footer.html.twig' %}
		</div>
	{% endif %}
{% endblock %}
