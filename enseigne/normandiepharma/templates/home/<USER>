{% extends 'home.html.twig' %}
{% form_theme formCompte 'Form/fields.html.twig' %}
{% set montantFidelite = 8%}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="row">
		<div class="{{getColSize(menuLateral)}} pl0">
			<div class="banniere banniere-activation"></div>
		</div>
	</div>
	<div class="pageClient" id="accueil">
		{% include 'home/menu.html.twig' %}
		<div id="maCarte" class="blocBackground1">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
                    {% include 'Common/messages.html.twig' %}
					<h2>{{ 'menu.Ma' |trans }} <span class="ralewayBold">{{ 'menu.carte' |trans }}</span></h2>
					<div class="separator"></div>
				</div>
			</div>
            <div class="row">
                <div class="{{getColSize(menuLateral)}}">
					<div class="bubble-wrapper">
                        {% include "Includes/macarte.html.twig" %}
						{% include "Includes/mespoints.html.twig" %}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<div class="bubble-wrapper">
						{% include "Includes/historique.html.twig" %}
					</div>
				</div>
			</div>
		</div>
        <div id="mesAvantages" class="blocBackground2" data-block-id="mesAvantages">
            <div class="row">
                <div class="{{getColSize(menuLateral)}}">
                    <h2>{{ 'menu.Mes' |trans}} <span class="ralewayBold">{{ 'menu.Avantages' |trans}}</span></h2>
                    <div class="separator"></div>
                    <div class="is-flex bubble-wrapper">
                        {# Bienvenue #}
                        {% include "Includes/blocBienvenue.html.twig" %}
                        {# Anniversaire #}
                        {% include "Includes/blocAnniversaire.html.twig" %}
                        {# Fidélité #}
                        {% include "Includes/blocFidelite.html.twig" %}
                    </div>
                </div>
            </div>
        </div>
        <div id="monProgramme" class="blocBackground1">
            <div class="row">
                <div class="{{getColSize(menuLateral)}}">
                    <h2>{{ 'menu.Mon' |trans}} <span class="ralewayBold">{{ 'menu.Programme' |trans}}</span></h2>
                    <div class="separator"></div>
                    <p class="sous-titre">
                        {{ 'programme.soustitre'|trans }}
                    </p>
                    <div class="is-flex-wrap">
                        <div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
                            <img class="bloc-icon" src="{{ asset('images/btn-offre-point.png')}}" alt="" />
                            <div class="monProgramme__content">
                                <p>{{ 'programme.block1.contenu'|trans|raw }}</p>
                            </div>
                        </div>
                        <div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
                            <img class="bloc-icon" src="{{ asset('images/btn-offre-adhesion.png')}}" alt="" />
                            <div class="monProgramme__content">
                                <p>{{ 'programme.block2.contenu'|trans({'%montant%':montantFidelite})|raw }}</p>
                            </div>
                        </div>
                        <div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
                            <img class="bloc-icon" src="{{ asset('images/btn-offre-surprise.png')}}" alt="" />
                            <div class="monProgramme__content">
                                <p>{{ 'programme.block3.contenu'|trans|raw }}</p>
                            </div>
                        </div>
                        <div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
                            <img class="bloc-icon" src="{{ asset('images/btn-offre-avantage.png')}}" alt="" />
                            <div class="monProgramme__content">
                                <p>{{ 'programme.block4.contenu'|trans|raw }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
		<div id="monCompte" class="blocBackground2" data-block-id="monCompte">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{{ form_start(formCompte, {'action': path('home_vue'),'method': 'POST'}) }}
						<h2>{{ 'menu.Mon' |trans}} <span class="ralewayBold">{{ 'menu.Compte' |trans}}</span></h2>
						<div class="separator"></div>
						<p class="sous-titre ralewayBold">
						</p>
                        <div class="fieldsetWith">
                            <div class="row">
                                <div class="column small-12 medium-6 first">
                                    <div class="checked">
                                        <i class="fa fa-arrow-circle-right form-icon mrs"></i>
                                        <span>
                                    {{ client.civilite }} {{ client.prenom }} {{ client.nom }}
                                    </span>
                                    </div>
                                    <div class="checked">
                                        <div class="form-group-head">
                                            <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
                                            <span class="ralewayBold">{{ form_label(formCompte.dateNaissance) }}</span>
                                        </div>
                                        <div class="form-group form-group--margin">
                                            {{ form_widget(formCompte.dateNaissance) }}
                                        </div>
                                    </div>
                                    <div id="adresse" class="checked">
                                        <div class="form-group-head">
                                            <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
                                            <span class="ralewayBold">{{ 'compte.Adresse'|trans }}</span>
                                        </div>
                                        <div class="form-group form-group--margin">
                                            {{ form_row(formCompte.numero)}}
                                            {{ form_row(formCompte.voie)}}
                                            {{ form_row(formCompte.escalier)}}
                                            {{ form_row(formCompte.batiment)}}
                                            {{ form_row(formCompte.lieuDit)}}
                                            {{ form_row(formCompte.codepostal)}}
                                            {{ form_row(formCompte.ville)}}
                                            {{ form_row(formCompte.codePaysClient)}}
                                        </div>
                                    </div>
                                </div>
                                <div class="column small-12 medium-6 ">
                                    <div class="checked mobile noMargin{% if client.telephoneMobile is empty %}emptyInput{% endif %}">
                                        <div class="form-group-head">
                                            <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
                                            <span class="ralewayBold">{{ form_label(formCompte.telephoneMobile)}}
                                        <div class="has-error">{{ form_errors(formCompte.telephoneMobile)}}</div></span>
                                        </div>
                                        <div class="form-group form-group--margin">{{ form_widget(formCompte.telephoneMobile)}}</div>
                                    </div>
                                    <div class="checked {% if client.email is empty %}emptyInput{% endif %}">
                                        <div class="form-group-head">
                                            <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
                                            <span class="ralewayBold">{{ form_label(formCompte.email)}}
                                        <div class="has-error">{{ form_errors(formCompte.email)}}</div></span>
                                        </div>
                                        <div class="form-group form-group--margin">{{ form_widget(formCompte.email)}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row submit flexright">
                                <button type="submit" class="btn btn-default btn-valider-modif ralewayBold mlxl"><i class="fas fa-pen-square fa-lg mrs" aria-hidden="true"></i>{{'client.save'|trans}}</button>
                            </div>
                            <p class="mentions-legales">
                                * mentions obligatoires <br>
                                Les informations recueillies sur ce formulaire sont nécessaires à la création et la
                                gestion de votre compte fidélité. A défaut d’être renseignées, votre compte de fidélité
                                ne pourra pas être créé et vous ne pourrez pas bénéficier du programme de fidélité ni
                                recevoir les offres commerciales associées. Les autres informations collectées
                                permettent de mieux vous connaître et d’améliorer les offres et services fournis dans le
                                cadre du programme de fidélisation. Les informations que nous collectons sont utilisées
                                exclusivement pour la gestion du programme de fidélisation, ainsi qu’à des fins
                                statistiques. Les données collectées sont conservées pendant une durée maximum de 3 ans
                                suivant la fin de la relation commerciale, hors obligation légale d’archivage. Le
                                responsable du traitement est l’Officine dans laquelle vous avez adhéré au programme
                                ainsi que les entités du groupement à laquelle elle appartient, à savoir Normandie
                                Pharma, 1 rue du Bocage 14460 Colombelles. Les données sont collectées sur la base de
                                votre consentement conformément à l’article 6.1 a) du Règlement (UE) 2016/679
                                (Règlement Général sur la Protection des Données - RGPD). Ces données sont destinées
                                à la société Normandie Pharma, à toutes les officines qui participent au Programme de
                                fidélité Normandie Pharma, ainsi qu’aux prestataires et sous-traitants sélectionnés pour
                                la gestion du programme de fidélité. Conformément aux dispositions du Règlement (UE)
                                2016/679 RGPD, vous disposez d'un droit d'accès à vos données, d'un droit de
                                rectification ou d'effacement, d'un droit de limitation de leur traitement, du droit de
                                retirer votre consentement à tout moment, d'un droit de portabilité ainsi que d'un droit
                                d'opposition à la collecte de vos données. Vous pouvez exercer vos droits auprès du
                                responsable de traitement en adressant une demande en justifiant votre identité
                                directement dans l’Officine participant au programme, par courrier à l’adresse 1 rue du
                                Bocage 14460 Colombelles ou par mail à l’adresse <EMAIL>. Vous
                                disposez également du droit d'introduire une réclamation auprès de la Commission
                                Nationale Informatique et Libertés (CNIL).
                            </p>
                        </div>
					{{ form_widget(formCompte._token) }}
					{{ form_end(formCompte, {'render_rest': false}) }}
				</div>
			</div>
		</div>
        <div id="maMagasin" class="blocBackground1">
            <div class="row">
                {# <div class="{{getColSize(menuLateral,{colXs:12,colLg:8,colLgOffset:4})}}"> #}
                <div class="{{getColSize(menuLateral)}}">
                    <h2>{{ 'menu.Ma' |trans }} <span class="ralewayBold">{{ 'menu.magasin' |trans }}</span></h2>
                    <div class="separator"></div>
                    {% if not menuLateral %}
                        <div class="column large-4 {% if responsive %}hide-for-small-only{% endif %}">
                            <a href="{{path('logout')}}" class="pull-right btn btn-default btn-deconnexion margin-top-40">
                                <i class="far fa-times-circle fa-lg mrs" aria-hidden="true"></i>{{'client.deconnexion'|trans}}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="row">
                <div class="{{getColSize(menuLateral)}}">
                    {% if getParam('map') %}
                        <div class="bubble-wrapper">
                            {% include "Includes/monmagasin.html.twig" %}
                            {% include "Includes/maps.html.twig" %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
    <div class="footer">
        <div class="row">
            <div class="{{getColSize(menuLateral)}}">
                {% include '/footer.html.twig' %}
            </div>
        </div>
    </div>
	{% endif %}
{% endblock %}
{% block javascripts %}
	<script type="module">
		function verifentier(champs) {
		    var PxTmp = parseInt(champs.val(), 10);
		    if (isNaN(PxTmp)){
		        PxTmp = '';
			}
		    champs.val(PxTmp);
		    return PxTmp !='';
		}

		$(document).ready(function() {
			$('#client_telephoneMobile').blur(function(){
				$('#client_envoieSmsInterne').prop('checked', true);
			});
			$('#client_email').blur(function(){
				$('#client_envoieEmailInterne').prop('checked', true);
			});
			{% if getParam('carte.donuts') %}
				var color = null;
				var dataset = null;
				if ($('#donuts').length) {
					if ({{ cagnotteReste }} != 0) {
						var colorDonus = "#f7585d";
						color = d3.scaleOrdinal([colorDonus, '#ffffff', '#CCCCCC', '#ffffff']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}), {{seuilUtilisationCagnotte /200}},{{ cagnotteReste }}, {{seuilUtilisationCagnotte /200}}]
						};
					}
					else {
						color = d3.scaleOrdinal(['#004b95', '#f0be38']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}),{{ cagnotteReste }} ]
						};
					}

                    const width = 100,
                        height = 100,
                        radius = Math.min(width, height) / 2;

                    const pie = d3.pie()
                        .sort(null);

                    const arc = d3.arc()
                        .innerRadius(radius - 17)
                        .outerRadius(radius);

                    const svg = d3.select("#donuts").append("svg")
                        .attr("width", width)
                        .attr("height", height)
                        .append("g")
                        .attr("transform", `translate(${width / 2}, ${height / 2})`);

                    svg.selectAll("path")
                        .data(pie(dataset.apples))  // Assure-toi que dataset.apples est un tableau de nombres
                        .enter().append("path")
                        .attr("fill", (d, i) => color(i))
                        .attr("d", arc);
				}
			{% endif %}
		});

		jQuery.expr[":"].Contains = jQuery.expr.createPseudo(function(arg) {
			return function( elem ) {
				return jQuery(elem).text().toUpperCase().indexOf(arg.toUpperCase()) >= 0;
			};
		});
    </script>
{% endblock %}
