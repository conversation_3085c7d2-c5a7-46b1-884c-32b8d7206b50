<div class="bubble-container is-half mesPoints" data-block-id="mesPoints">
    <p class="center titreBubble"><span class="ralewayBold">{{ 'menu.mesPoints'|trans }}</span></p>
	<div class="bubble bubble--white">
        {% if getParam('carte.donuts') %}
            <div id="donuts"></div>
        {% endif %}
        <p id="blocPoint" class="isGrey">
            {{ client.civilite }} {{client.prenom}} {{client.nom}}, {{ 'client.pointCumule' |trans}}<br>
            <span id="ptsatteind" class="ralewayBold">{{ cagnotte|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotte})}}</span><br>
        </p>
        <p class="isGrey">
            {% if client.magasin is defined and client.magasin and client.magasin.libelle is not empty %}
                {{ 'client.dansVotre' |trans}} {{ client.magasin.libelle }}.
            {% endif %}
            {%  if cagnotteReste != 0 %}
                {{ 'client.ilVousManque' |trans}}&nbsp;{{ cagnotteReste|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotteReste})}} {{ 'client.pourRecevoirVotreChequeFid' |trans}}<br>
            {% else %}
                {{ 'client.zeroptsrestant'|trans }}
            {% endif %}
        </p>
    </div>
</div>
