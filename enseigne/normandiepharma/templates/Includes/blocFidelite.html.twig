{% if featureChecker.hasChequeFidelite %}
<div class="bubble-container is-third">
	<div class="bloc-icon">
        <img src="{{ asset('images/btn-offre-cheque.png')}}" alt="" />
	</div>
	<div class="titreBubble">
		<p class="center mtm">{{ 'fidelite.remiseCheque' |trans }}</p>
	</div>
	<div class="bubble">
		{% if client.dateDernierCheque is empty %}
			{{ 'cheque.aucunChequeFidelite'|trans }}
		{% else %}
            {{ 'avantage.dernierChequeFideliteEmisLe'|trans }} <span class="grey">{{ client.dateDernierCheque|date("d/m/Y") }}</span><br>
			{{ 'avantage.dUnMontantDe'|trans }}
			{% if getParam('remise.bienvenue.pourcentageAllow') %}
				{% if client.montantDernierCheque > 0 %}
					{{ client.montantDernierCheque }}<sup>€</sup>
				{% else %}
					{% if client.pourcentageDernierCheque > 0 %}
						-{{ client.pourcentageDernierCheque }}<sup>%</sup>
					{% else %}
						{{ client.montantDernierCheque }}<sup>€</sup>
					{% endif %}
				{% endif %}
			{% else %}
				{{ client.montantDernierCheque }}<sup>€</sup>
			{% endif %}
            {% if client.NumDernierCheque %}
			<span style="white-space:nowrap;">(N°{{ client.NumDernierCheque }})</span>
            {% endif %}<br>
			{% if client.dateFinValiditeDernierCheque is not empty %}
				{{ 'avantage.dateDeValidite'|trans }} <span class="grey">{{ client.dateFinValiditeDernierCheque|date("d/m/Y") }}</span>
			{% endif %}
			{% if client.dateEncaissementDernierCheque is empty %}
				{% if featureChecker.hasChequeFidelite
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Fidelite.value]
				%}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'type': enum('App\\Services\\Cheque\\ChequeType').Fidelite.value }) }}" class="btn btn-default btn-telecharger ralewayBold"><span class="glyphicon glyphicon-download-alt">&nbsp;</span>{{ 'fidelite.telecharger'|trans }}</a>
				{% endif %}
			{% else %}
				<span class="ralewayBold offreUtilisee">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncaissementDernierCheque|date("d/m/Y") }}</span><br>
			{% endif %}
		{% endif %}
		<br>
		{%  if cagnotteReste != 0 %}
			{{ 'client.ilVousManque' |trans}} <span class="ralewayBold green">{{ cagnotteReste|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotteReste})}}</span> {{ 'client.pourRecevoirVotreProchaineChequeFid' |trans}}
		{% else %}
			{{ 'client.zeroptsrestant'|trans }}
		{% endif %}
	</div>
</div>
{% endif %}
