parameters:
  ### CONFIGURATION WEBSERVICES ###
  client:                                   "Normandie Pharma"

  ### CONFIGURATION PASSBOOK ###
  sel:                                      zefidbyaquitem
  id_enseigne:                              zefid
  carte.passbook:                           false
  passbook.enseigneId:                      470000000001
  passbook.saltClient:                      '%env(salt_client)%'
  passbook.url:                             '%env(passbook_url)%'

  ### CONFIGURATION DONUTS ###
  carte.donuts: 		                    true

  # Définie si le site est responsive. 
  # -> Si false, pensez à fixer la taille du container
  template.isResponsive:                    true
  template.menuLateral:                     true
  template.footer:                          true

  # CARTE
  carte.telechargement:	                    true

  # MAP
  map:                                      true

  # LOGO
  logoClient:                               "/logo/logo_zefid.png"

  # BIENVENUE
  remise.bienvenue.pourcentageAllow:        false

  # ANNIVERSAIRE
  remise.anniversaire.pourcentageAllow:     false

  # FIDELITE
  remise.fidelite.pourcentageAllow:         false
  
    # GEOLOCALISATION DU CLIENT
  adresse.geolocalisation: false
  alienor_requeteur_adresse.wsUrl: http://aqui-symfony.alienor.net/getAdresse
  alienor_requeteur_adresse.client: testalienor
  alienor_requeteur_adresse.cle: CLEALIENOREREQUETEURADRESSE

