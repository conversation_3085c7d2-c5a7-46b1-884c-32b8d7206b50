App\Entity\Client:
    attributes:
        mouvementPoints:
            serialized_name: "collectionMouvement"
        nbrePoints:
            serialized_name: "@CAGNOTTE"
        envoiCourrierInterne:
            serialized_name: "@ENVOIECOURRIERINTERNE"
        nbrePtsRestantCheque:
            serialized_name: "@NBREPTSRESTANTCHQ"
        numDernierCheque:
            serialized_name: "@NODERNIERCHEQUE"

        dateDernierChequeAnniv:
            serialized_name: "@DATEDERNIERCHQANNIV"
        dateFinValiditeDernierChequeAnniv:
            serialized_name: "@DATEFINVALIDITEDERNIERCHQANNIV"
        numDernierChequeAnniv:
            serialized_name: "@NODERNIERCHQANNIV"
        dateEncaissementDernierChequeAnniv:
            serialized_name: "@DATEENCAISDERNIERANNIVERSAIRE"
        montantDernierChequeAnniv:
            serialized_name: "@MONTANTANNIVERSAIRE"
            
        dateDernierChequeBvnu:
            serialized_name: "@DATEDERNIERCHQBIENV"
        montantDernierChequeBvnu:
            serialized_name: "@"
        numDernierChequeBvnu:
            serialized_name: "@NODERNIERCHQBIENV"
        dateFinValiditeDernierChequeBvnu:
            serialized_name: "@DATEFINVALIDITEDERNIERCHQBIENV"
        dateDebutValiditeDernierChequeBvnu:
            serialized_name: "@"
        dateEncasDernierChequeBvnu:
            serialized_name: "@"

#Normandiepharma\Entity\Client:
#    attributes:
#        enfants:
#            serialized_name: "collectionEnfant"
#        animals:
#            serialized_name: "collectionAnimal"
#        autreAnimal:
#            serialized_name: "@ANIMALAUTRE"
