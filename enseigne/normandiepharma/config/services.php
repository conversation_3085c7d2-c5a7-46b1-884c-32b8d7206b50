<?php

declare(strict_types=1);

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use App\Form\Login\LoginTypeInterface;
use App\Form\Login\LoginWithDateType;
use App\Form\Login\LoginWithDateWithoutCaptchaType;
use App\Serializer\CodeClientDeserializer;
use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteFormaterStrategyInterface;
use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteIntegerFormaterStrategy;
use App\Services\Cagnotte\CagnotteStrategies\CagnotteFromClient;
use App\Services\Cagnotte\CagnotteStrategies\CagnotteStrategyInterface;
use App\Services\Cagnotte\CagnotteTypes\CagnotteTypeStrategyInterface;
use App\Services\Cagnotte\CagnotteTypes\PointCagnotte;
use App\Services\Cheque\ChequeSecurityFactoryInterface;
use App\Services\ClientAuthenticatorInterface;
use App\Services\NewClientsService;
use Normandiepharma\Services\Cheque\ChequeSecurityFactory;
use Normandiepharma\Services\ClientAuthenticator;
use Normandiepharma\Services\CodeClientConverter;
use Normandiepharma\Services\CodeClientConverterInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

return function (ContainerConfigurator $container): void {
    // default configuration for services in *this* file
    $services = $container->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
    ;

    $container->parameters()
        ->set('features', [
            'activation' => false,
            'don' => false,

            // BIENVENUE
            'remise.bienvenue' => true,
            'remise.bienvenue.telechargement' => true,

            // ANNIVERSAIRE
            'remise.anniversaire' => true,
            'remise.anniversaire.telechargement' => true,

            // FIDELITE
            'remise.fidelite' => true,
            'remise.fidelite.telechargement' => true,
        ])
        ->set('code_converter_params', [
            'PROGRAMME' => '%env(string:PROGRAMME_AQUITEM_KEY)%',
            'MAGASIN' => '999999999',
            'salt_client' => '%env(string:SALT_CLIENT_CONVERTER)%',
        ])
        ->set('adresse.geolocalisation', false)
    ;

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Normandiepharma\\', '../src/')
        ->exclude('../src/{DependencyInjection,Entity,Kernel.php}');

    $services->alias(CagnotteStrategyInterface::class, CagnotteFromClient::class)->public();
    $services->alias(CagnotteTypeStrategyInterface::class, PointCagnotte::class)->public();
    $services->alias(CagnotteFormaterStrategyInterface::class, CagnotteIntegerFormaterStrategy::class)->public();

    // Define custom webservice baseUri for code carte conversion
    //    $env = $container->env();
    //    $isTestEnv = in_array($env, ['test','dev'], true);
    //    $isProd = $env === 'prod' && (param('is_webservice_prod_enabled') === true);

    //    $baseUriConverter = match (true) {
    //        $isTestEnv => 'http://wiremock:8080',
    //        $isProd => '%webservice_code_converter.uri_prod%',
    //        default => '%webservice_code_converter.uri_preprod%',
    //    };
    //    $services->set('code_converter_client', HttpClient::class)
    //        ->factory([HttpClient::class, 'createForBaseUri'])
    //        ->args([$baseUriConverter])
    //        ->public()
    //    ;

    $services->get(CodeClientConverter::class)
        ->arg('$codeClientDeserializer', service(CodeClientDeserializer::class))
        ->arg('$codeConverterParams', param('code_converter_params'));

    $services->alias(CodeClientConverterInterface::class, CodeClientConverter::class);

    //    $services->alias('aquitemClient', 'http_client.ws1')->public();
    //    $services->alias('codeConverterClient', 'http_client.ws2')->public();

    //    $services->get(CodeClientConverter::class)
    //        ->arg('$aquitemClient', service('code_converter_client'))
    //        ->arg('$codeClientDeserializer', service(CodeClientDeserializer::class));

    //    $services->bind(
    //        HttpClientInterface::class . '$aquitemClient',
    //        service('http_client.code_converter_client')
    //    );

    $services->alias(ChequeSecurityFactoryInterface::class, ChequeSecurityFactory::class);

    //    $services->alias(CagnotteFormaterInterface::class, WithDecimalCagnotte::class)->public();

    $services->alias(LoginTypeInterface::class, LoginWithDateType::class)->public();
    if ('prod' !== $container->env()) {
        $services->alias(LoginTypeInterface::class, LoginWithDateWithoutCaptchaType::class)->public();
    }
    //    $services->alias('enseigne_authenticator', HCaptchaAuthenticator::class)->public();
    //    $services->alias(GlobalDefinitionsServiceInterface::class, NewGlobalDefinitionsService::class)->public();
    //    $services->alias(NewClientsServiceInterface::class, NewClientsService::class)->public();
    //    $services->alias(ClientTypeInterface::class, ClientType::class)->public();

    $services->get(ClientAuthenticator::class)->arg('$authenticator', service(NewClientsService::class));
    $services->alias(ClientAuthenticatorInterface::class, ClientAuthenticator::class);
};
