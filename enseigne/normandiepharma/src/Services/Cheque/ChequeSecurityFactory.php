<?php

namespace Normandiepharma\Services\Cheque;

use App\Entity\Client;
use App\Services\Cheque\ChequeFideliteSecurityChecker;
use App\Services\Cheque\ChequeSecurityCheckerInterface;
use App\Services\Cheque\ChequeSecurityFactoryInterface;
use App\Services\Cheque\ChequeType;

class ChequeSecurityFactory implements ChequeSecurityFactoryInterface
{
    public function build(ChequeType $type, Client $client): ChequeSecurityCheckerInterface
    {
        return match ($type) {
            ChequeType::Bienvenue => new ChequeBienvenueSecurityChecker($client),
            ChequeType::Anniversaire => new ChequeAnniversaireSecurityChecker($client),
            ChequeType::Fidelite => new ChequeFideliteSecurityChecker($client),
        };
    }
}
