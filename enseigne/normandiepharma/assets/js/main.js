$(document).ready(function(){
	//effet scroll du menu
	$('.menu ul a').on('click', function(e){
		e.preventDefault();
		var scroll = '#' + $(this).attr("href").split('/vue/#')[1];
		var time = Math.abs($(scroll).offset().top - $(document).scrollTop());
		if(time > 1000)
			time = 1000;
		$('html, body').animate({
			scrollTop: $(scroll).offset().top+'px'
		}, time);
		return false;
	});
	$('.radioSelect .radio input:not([checked])').parent().hide();
	$('.radioSelect .radio label, .radioSelect .arrow').mouseup(function(){
		var obj= $(this).parent();
		if(obj.attr('class').indexOf('radioSelect')<0){
			obj = obj.parent();
		}
		if(obj.children('div.radio').attr('class').indexOf('grand') < 0){
			$('.radioSelect .radio').removeClass('grand');
			obj.children('div.radio').addClass('grand');
			obj.children('div.radio').children('label').show();
			obj.children('span.arrow').hide();

		}else{
			obj.children('div.radio').removeClass('grand');
			obj.children('div.radio').children('label').hide();
			$(this).show();
			obj.children('span.arrow').show();
		}
	});
});
