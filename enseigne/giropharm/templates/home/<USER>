{% extends 'home.html.twig' %}
{% form_theme formCompte 'Form/fields.html.twig' %}
{% set montantFidelite = 8%}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="row">
		<div class="{{getColSize(menuLateral)}} pl0">
			<div class="banniere banniere-activation"></div>
		</div>
	</div>
	<div class="pageClient" id="accueil">
		{% include 'home/menu.html.twig' %}
		<div id="maMagasin">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{% include 'Common/messages.html.twig' %}
				</div>
				{# <div class="{{getColSize(menuLateral,{colXs:12,colLg:8,colLgOffset:4})}}"> #}
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Ma' |trans }} <span class="ralewayBold">{{ 'menu.magasin' |trans }}</span></h2>
					<div class="separator"></div>
					{% if not menuLateral %}
						<div class="column large-4 {% if responsive %}hide-for-small-only{% endif %}">
							<a href="{{path('logout')}}" class="pull-right btn btn-default btn-deconnexion margin-top-40">
								<i class="far fa-times-circle fa-lg mrs" aria-hidden="true"></i>{{'client.deconnexion'|trans}}
							</a>
						</div>
					{% endif %}
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{% if getParam('map') %}
						<div class="bubble-wrapper">
							{% include "Includes/monmagasin.html.twig" %}
							{% include "Includes/maps.html.twig" %}
						</div>
					{% endif %}
				</div>
			</div>
		</div>
		<div id="maCarte" class="">
			<div class="row">
				<div class="{{getColSize(menuLateral)}} blocBackground2">
					<h2>{{ 'menu.Ma' |trans }} <span class="ralewayBold">{{ 'menu.carte' |trans }}</span></h2>
					<div class="separator"></div>
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}} blocBackground2">
					<div class="bubble-wrapper">
                        {% include "Includes/macarte.html.twig" %}
						{% if getParam('carte.passbook') %}
							{% include "Includes/passbook.html.twig" %}
						{% endif %}
						{% include "Includes/mespoints.html.twig" %}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="{{getColSize(menuLateral)}} blocBackground2">
					<div class="bubble-wrapper">
						{% include "Includes/historique.html.twig" %}
					</div>
				</div>
			</div>
		</div>
		<div id="monCompte" class="" data-block-id="monCompte">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					{{ form_start(formCompte, {'action': path('home_vue'),'method': 'POST'}) }}
						<h2>{{ 'menu.Mes' |trans}} <span class="ralewayBold">{{ 'menu.Compte' |trans}}</span></h2>
						<div class="separator"></div>
						<p class="sous-titre ralewayBold">
							{{ 'compte.soustitre1' |trans}}<br>
							{{ 'compte.soustitre2' |trans}}
						</p>
					<div class="fieldsetWith">
						<div class="row">
							<div class="column small-12 medium-6 first">
								<div class="checked">
									<i class="fa fa-arrow-circle-right form-icon mrs"></i>
									<span>
									{{ client.civilite }} {{ client.prenom }} {{ client.nom }}
									</span>
								</div>
								<div class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.dateNaissance) }}</span>
									</div>
									<div class="form-group form-group--margin">
										{{ form_widget(formCompte.dateNaissance) }}
									</div>
								</div>
								{% if client.dateCreation is defined %}
									<div class="checked">
										<i class="fa fa-arrow-circle-right form-icon mrs"></i>
										<span>
										{{ 'compte.MembreDepuisLe'|trans }} {{ client.dateCreation|date('d/m/Y') }}
										</span>
									</div>
								{% endif %}
								{% if client.magasin is defined %}
									<div class="checked">
										<i class="fa fa-arrow-circle-right form-icon mrs"></i>
										<span class="ralewayBold">{{ 'compte.MonMagasin'|trans }}</span>:
										<p class="form-group form-group--margin">
											{% if client.magasin.libelle is defined and client.magasin.libelle is not empty %}
												{{ client.magasin.libelle }}<br>
											{% endif %}
											{% if client.magasin.adresse1 is defined and client.magasin.adresse1 is not empty %}
												{{ client.magasin.adresse1 }}<br>
											{% endif %}
											{% if client.magasin.adresse2 is defined and client.magasin.adresse2 is not empty %}
												{{ client.magasin.adresse2 }}<br>
											{% endif %}
											{% if (client.magasin.codepostal is defined and client.magasin.codepostal is not empty) or (client.magasin.ville is defined and client.magasin.ville is not empty) %}
												{{ client.magasin.codepostal }} {{ client.magasin.ville }}<br>
											{% endif %}
											{% if client.magasin.telephone is defined and client.magasin.telephone is not empty %}
												{{ client.magasin.telephone }}
											{% endif %}
										</p>
									</div>
								{% endif %}
								<div class="checked mobile {% if client.telephoneMobile is empty %}emptyInput{% endif %}">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.telephoneMobile)}}
										<div class="has-error">{{ form_errors(formCompte.telephoneMobile)}}</div></span>
									</div>
									<div class="form-group form-group--margin">{{ form_widget(formCompte.telephoneMobile)}}</div>
								</div>
								<div class="checked mobile {% if client.telephoneFixe is empty %}emptyInput{% endif %}">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.telephoneFixe)}}
										<div class="has-error">{{ form_errors(formCompte.telephoneFixe)}}</div></span>
									</div>
									<div class="form-group form-group--margin">{{ form_widget(formCompte.telephoneFixe)}}</div>
								</div>
								<div class="checked {% if client.email is empty %}emptyInput{% endif %}">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(formCompte.email)}}
										<div class="has-error">{{ form_errors(formCompte.email)}}</div></span>
									</div>
									<div class="form-group form-group--margin">{{ form_widget(formCompte.email)}}</div>
								</div>
							</div>
							<div class="column small-12 medium-6 ">
								<div id="adresse" class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ 'compte.Adresse'|trans }}</span>
									</div>
									<div class="form-group form-group--margin">
										{{ form_row(formCompte.numero)}}
										{{ form_row(formCompte.voie)}}
										{{ form_row(formCompte.escalier)}}
										{{ form_row(formCompte.batiment)}}
										{{ form_row(formCompte.lieuDit)}}
										{{ form_row(formCompte.codepostal)}}
										{{ form_row(formCompte.ville)}}
										{{ form_row(formCompte.codePaysClient)}}
									</div>
								</div>
							</div>
						</div>
						<div class="checked mbl ptl">
							<div class="form-group-head">
								<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
								<span class="ralewayBold margin-top-50">{{'compte.monFoyer'|trans}}</span>
							</div>
							<div class="form-group form-group--margin">
								<div class="row">
									<div class="column small-12 medium-6 first">
										{# SITUATION FAMILLE #}
										<div class="form-field">
											<div class="form-group-head">
												<span class="ralewayBold">{{ form_label(formCompte.situationFamiliale)}}
												<div class="has-error">{{ form_errors(formCompte.situationFamiliale)}}</div></span>
											</div>
											{{ form_widget(formCompte.situationFamiliale,{'label_attr' : {'class': 'ralewayBold'}})}}
										</div>
									</div>
								</div>

								{# Enfants #}
								<div class="form-field">
								    <label class="ralewayBold">{{ 'compte.avezvousdesenfants'|trans }}</label>
									<div id="enfants">
											<div id="prototypeEnfants" data-index="{{ client.enfants|length }}"
											data-prototype="{{ form_widget(formCompte.enfants.vars.prototype)|e }}">
											{% set accompagnantType = 2 %}
											{% if client.enfants|length > 0 %}
												{% for key, enfant in client.enfants %}
													{% if formCompte.enfants[key] is defined %}
														<fieldset class="no_fieldset enfant-field">
															<div class="enfant-field-form">
																{% if form_errors(formCompte.enfants[key].prenom) %}
																	<div class="has-error">{{ form_errors(formCompte.enfants[key].prenom) }}</div>
																{% endif %}
																<div id="client_enfants_{{ key }}">
																	<div class="field field_align_label">
																		<label for="client_enfants_{{ key }}_prenom"
																			class="required">{{ 'enfant.prenom' | trans }}</label>
																		{{ form_widget(formCompte.enfants[key].prenom) }}
																	</div>
																	<div class="field field_align_label">
																		{% if form_errors(formCompte.enfants[key].dateNaissance) %}
																			<div class="has-error">{{ form_errors(formCompte.enfants[key].dateNaissance) }}</div>
																		{% endif %}
																		<label for="client_enfants_{{ key }}_dateNaissance"
																			class="required">{{ 'enfant.dateNaissance' | trans }}</label>
																		{{ form_widget(formCompte.enfants[key].dateNaissance) }}
																	</div>
																	<div class="field field_align_label">
																		{% if form_errors(formCompte.enfants[key].genre) %}
																			<div class="has-error">{{ form_errors(formCompte.enfants[key].genre) }}</div>
																		{% endif %}
																		<label for="client_enfants_{{ key }}_genre"
																			class="required">{{ 'enfant.genre' | trans }}</label>
																		{{ form_widget(formCompte.enfants[key].genre) }}
																	</div>
																	<div class="field field_align_label delete-btn">
																		<a href="" class="btn btn_cancel delete btn--light"><span><i
																						class="fa fa-times fa-2x"></i></span></a>
																	</div>
																	{% if(loop.last) %}
																		<div class="add-btn">
																			<a href="#"
																			class="btn btn_add btn-default btn--light btnColor_1 addChild"><span><i
																							class="fa fa-plus fa-2x"></i></span></a>
																		</div>
																	{% endif %}
																</div>
															</div>
														</fieldset>
													{% endif %}
												{% endfor %}
											{% else %}
												<div class="add-btn">
													<a href="#" class="btn btn_add btn-default btn--light btnColor_1 addChild">
														<span><i class="fa fa-plus fa-2x"></i></span>
													</a>
												</div>
											{% endif %}
										</div>
									</div>
								</div>

								{# ANIMAUX #}
								<div class="form-field">
									{{ form_errors(formCompte.autreAnimal) }}
									<label class="ralewayBold">{{ 'compte.possedezvousanimaux'|trans }}</label>
									<div>
                                        {% if formCompte.animals|length > 0 %}
										{% for i in 0..formCompte.animals|length-1 %}
											<div>
												{{ form_widget(formCompte.animals[i]) }}
												{{ form_label(formCompte.animals[i]) }}
											</div>
										{% endfor %}
                                        {% endif %}
									</div>
									{{ form_widget(formCompte.autreAnimal,{'attr' : {'class': 'width-auto'}}) }}
								</div>
							</div>
						</div>
						<div class="checked mtm">
							<div class="form-group-head">
								<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
								<span class="ralewayBold margin-top-50">Mes centres d'intérêts </span>
							</div>
							<div class="form-group form-group--margin">
								<span class="ralewayBold">{{ 'compte.souhaitezvousrecevoir'|trans }}</span>
								<div class="row">
									<div class="column small-12 large-6">
                                        {% if formCompte.categorieArticles|length > 0 %}
										{% set premiereColumn = true %}
										{% for i in 0..formCompte.categorieArticles|length-1 %}
											{% if i >= (formCompte.categorieArticles|length/2) and premiereColumn %}
												{% set premiereColumn = false %}
											</div>
											<div class="column small-12 large-6">
											{% endif %}
											<div>
												{{ form_widget(formCompte.categorieArticles[i]) }}
												{{ form_label(formCompte.categorieArticles[i]) }}
											</div>
										{% endfor %}
                                        {% endif %}
									</div>
								</div>
							</div>
						</div>
						<p class="mtm avertissement-form">
							En validant mes informations, je confirme que j'ai bien pris connaissance des <a href="{{asset('images/cgu.pdf') }}" target="_blank">conditions générales d'utilisation</a> de "Ma carte Giropharm" et je consens à la collecte et au traitement de mes données personnelles dans le cadre de ce dernier.
						</p>
						<div class="row submit centered">
							<button type="submit" class="btn btn-default btn-valider-modif ralewayBold mlxl"><i class="fas fa-pen-square fa-lg mrs" aria-hidden="true"></i>{{'client.save'|trans}}</button>
						</div>
						<p class="mentions-legales">
							* mentions obligatoires <br>
							Les informations recueillies à partir de ce formulaire et identifiées par un astérisque sont nécéssaires à la création et la gestion de votre compte-client. A défaut d'être renseignées, votre compte-client ne pourra pas être créé et vous ne pourrez pas participer au programme "Ma carte Giropharm" (le " Programme ") ni recevoir les offres commerciales associées. Les autres informations collectées sur le formulaire permettent de mieux vous connaître et d'améliorer les offres et services fournis dans le cadre du Programme. L'utilisation de la Carte entraîne la collecte des données afférentes à vos achats. Ces données sont utilisées exclusivement pour la gestion du Programme. Les données collectées sont conservées pendant une durée maximum de 3 ans suivant la fin de la relation commerciale, hors obligation légale d'archivage. Le responsable du traitement est la société Giropharm située sis 2 Place Gustave Eiffel Immeuble Dublin Silic, 94150 RUNGIS. Les données sont collectées sur la base de votre consentement conformément à l'article 6.1 a) du Règlement (UE) 2016/679 (Règlement Général sur la Protection des Données - RGPD). Ces données sont destinnées à toutes les pharmacies membre du réseau GIROPHARM qui participent au Programme, ainsi qu'aux prestataires et sous-traitants sélectionnées pour la gestion du Programme. Conformément aux dispositions du Règlement (UE) 2016/679 RGPD, vous disposez d'un droit d'accès à vos données, d'un droit de rectification ou d'effacement, d'un droit de limitation de leur traitement, du droit de retirer votre consentement à tout moment, d'un droit de portabilité ainsi qu'un droit d'opposition à la collecte de vos données. Vous pouvez exercer vos droits auprès du responsable de traitement en adressant une demande via le site internet www.giropharm.fr ou directement dans la pharmacie adhérente paricipant au Programme. Par email à l'adresse : <EMAIL>. Par courrier à l'adresse suivante (en justifiant de son identité) : Giropharm - Service Ma Carte - 2 Place Gustave Eiffel Immeuble Dublin Silic, 94528 RUNGIS Cedex. Vous disposez également un droit d'introduire une réclamation auprès de la Comission Nationale Informatique et Libertés (CNIL).
						</p>
					</div>
					{{ form_widget(formCompte._token) }}
					{{ form_end(formCompte, {'render_rest': false}) }}
				</div>
			</div>
		</div>
		<div id="mesAvantages" class="">
			<div class="row">
				<div class="{{getColSize(menuLateral)}} blocBackground2">
					<h2>{{ 'menu.Mes' |trans}} <span class="ralewayBold">{{ 'menu.Avantages' |trans}}</span></h2>
					<div class="separator"></div>
					<div class="is-flex bubble-wrapper">
						{# Bienvenue #}
                        {% include "Includes/blocBienvenue.html.twig" %}
						{# Anniversaire #}
                        {% include "Includes/blocAnniversaire.html.twig" %}
						{# Fidélité #}
                        {% include "Includes/blocFidelite.html.twig" %}
					</div>
				</div>
			</div>
		</div>
		<div id="monProgramme" class="">
			<div class="row">
				<div class="{{getColSize(menuLateral)}}">
					<h2>{{ 'menu.Mon' |trans}} <span class="ralewayBold">{{ 'menu.Programme' |trans}}</span></h2>
					<div class="separator"></div>
					<p class="sous-titre">
						<img src="{{ asset('/build/giropharm/img/soustitremonprogramme.jpg')}}" />
					</p>
					<div class="is-flex-wrap">
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<img class="bloc-icon" src="{{ asset('/build/giropharm/img/1euro1point.jpg')}}" />
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">{{ 'programme.block1.titre'|trans|raw }}</span>
								</div>
								<p>{{ 'programme.block1.contenu'|trans|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<img class="bloc-icon" src="{{ asset('/build/giropharm/img/fideliterecompensee.jpg')}}" />
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block2.titre'|trans({'%montant%':montantFidelite})|raw }}
									</span>
								</div>
								<p>{{ 'programme.block2.contenu'|trans({'%montant%':montantFidelite})|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<img class="bloc-icon" src="{{ asset('/build/giropharm/img/anniversaire-bloc.jpg')}}" />
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block3.titre'|trans|raw }}
									</span>
								</div>
								<p>{{ 'programme.block3.contenu'|trans|raw }}</p>
							</div>
						</div>
						<div class="{{getProgrammeSize(menuLateral)}} monProgramme__bloc">
							<img class="bloc-icon" src="{{ asset('/build/giropharm/img/bonsplans.jpg')}}" />
							<div class="monProgramme__content">
								<div class="monProgramme__titre">
									<span class="ralewayBold">
										{{ 'programme.block4.titre'|trans|raw }}
									</span>
								</div>
								<p>{{ 'programme.block4.contenu'|trans|raw }}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
	<div class="row">
		<div class="{{getColSize(menuLateral)}}">
			{% include '/footer.html.twig' %}
		</div>
	</div>
	{% endif %}
{% endblock %}
{% block javascripts %}
	{% if getParam('carte.donuts') %}
		<script type="text/javascript" src="{{asset('js/d3.min.js')}}"></script>
	{% endif %}
	<script type="text/javascript" src="{{asset('js/common.js')}}"></script>

	<script type="text/javascript">
		function verifentier(champs) {
		    var PxTmp = parseInt(champs.val(), 10);
		    if (isNaN(PxTmp)){
		        PxTmp = '';
			}
		    champs.val(PxTmp);
		    return PxTmp !='';
		}

		$(document).ready(function() {
			$('#client_telephoneMobile').blur(function(){
				$('#client_envoieSmsInterne').prop('checked', true);
			});
			$('#client_email').blur(function(){
				$('#client_envoieEmailInterne').prop('checked', true);
			});
			{% if getParam('carte.donuts') %}
				var color = null;
				var dataset = null;
				if ($('#donuts').length) {
					if ({{ cagnotteReste }} != 0){
						var colorDonus = "#f7585d";
						color = d3.scale.ordinal().range([colorDonus, '#ffffff', '#CCCCCC', '#ffffff']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}), {{seuilUtilisationCagnotte /200}},{{ cagnotteReste }}, {{seuilUtilisationCagnotte /200}}]
						};
					}
					else {
						color = d3.scale.ordinal().range(['#004b95', '#f0be38']);
						//donnuts
						dataset = {
							apples: [({{ seuilUtilisationCagnotte }}-{{ cagnotteReste }}),{{ cagnotteReste }} ]
						};
					}

					var width = 100,
							height = 100,
							radius = Math.min(width, height) / 2;
					var pie = d3.layout.pie()
							.sort(null);
					var arc = d3.svg.arc()
							.innerRadius(radius - 17)
							.outerRadius(radius);
					var svg = d3.select("#donuts").append("svg")
							.attr("width", width)
							.attr("height", height)
							.append("g")
							.attr("transform", "translate(" + width / 2 + "," + height / 2 + ")");
					var path = svg.selectAll("path")
							.data(pie(dataset.apples))
							.enter().append("path")
							.attr("fill", function(d, n) {
								return color(n);
							})
							.attr("d", arc);
				}
			{% endif %}
		});
	</script>
    <script type="text/javascript">
		jQuery.expr[":"].Contains = jQuery.expr.createPseudo(function(arg) {
			return function( elem ) {
				return jQuery(elem).text().toUpperCase().indexOf(arg.toUpperCase()) >= 0;
			};
		});
		$(document).ready(function() {
			{# ANIMAUX #}
			// Init "Autres" field state
			var idAutreRadio = $('#client_autreAnimal').parent().find('label:Contains("autre")').attr('for');
			$('#client_autreAnimal').prop('disabled', !$('#'+ idAutreRadio).prop('checked'));
			// Toggle "Autres" field
			$('#' +  idAutreRadio).on('change', function(e){
				$('#client_autreAnimal').prop('disabled', !$(this).prop('checked'));
				if (! $(this).is(':checked')) {
					$('#client_autreAnimal').val('');
				}
			});
			$nbEnfants = {{client.enfants|length}}
			{# ENFANTS #}
			function addTagForm($collectionHolder) {
				// Get the data-prototype explained earlier
				var prototype = $collectionHolder.data('prototype');
				// get the new index
				var index = $collectionHolder.data('index');
				// Replace '__name__' in the prototype's HTML to
				// instead be a number based on how many items we have
				var newForm = prototype.replace(/__name__/g, index);
				// increase the index with one for the next item
				$collectionHolder.data('index', index + 1);
				if ($nbEnfants == 0) {
					var $newFormLi = $('<fieldset class="no_fieldset enfant-field"><div class="enfant-field-form"></div></fieldset>');
					$newForm = $(newForm).append($('<div class="delete-btn"><a style="display: none;" href="#" class="btn btn_cancel delete confirmDelete"><span><i class="fa fa-times fa-2x"></i></span></a></div>'));
					$newForm.append($('<div class="add-btn"><a href="#" class="btn btn_add btn-default btn--light btnColor_1 addChild"><span><i class="fa fa-plus fa-2x"></i></span></a></div>'));
				}
				else {
					var $newFormLi = $('<fieldset class="no_fieldset enfant-field"><div class="enfant-field-form"></div></fieldset>');
					$newForm = $(newForm).append($('<div class="delete-btn"><a href="#" class="btn btn_cancel delete confirmDelete"><span><i class="fa fa-times fa-2x"></i></span></a></div>'));
					$newForm.append($('<div class="add-btn"><a href="#" class="btn btn_add btn-default btn--light btnColor_1 addChild"><span><i class="fa fa-plus fa-2x"></i></span></a></div>'));
				}

				$newFormLi.find('.enfant-field-form').append($newForm);
				// Remove add button
				$('.add-btn').remove();
				// Display the form in the page in an li, before the "Add a tag" link li
				$collectionHolder.append($newFormLi);
			}


			// Get the ul that holds the collection of tags
			var $collectionHolder = $('#prototypeEnfants');

			// count the current form inputs we have (e.g. 2), use that as the new
			// index when inserting a new item (e.g. 2)
			$collectionHolder.data('index', $collectionHolder.find(':input').length);
			$collectionHolder.on('click', '.addChild', function (e) {
				// prevent the link from creating a "#" on the URL
				e.preventDefault();
				// add a new tag form (see next code block)
				addTagForm($collectionHolder);
				$nbEnfants += 1;
				return false;
			});

			if($collectionHolder.data('index') == 0){
				$('.addChild').trigger('click');
			}
			$collectionHolder.on('click', '.delete', function (e) {
				e.preventDefault();
				if ($("div[id^='client_enfants_']").length > 1) {
					$(this).closest('fieldset').remove();
				}
				else {
					var $fieldSet = $(this).closest('fieldset');
					$fieldSet.find('input').each(function (k, input) {
						$(input).val('');
					});
					$fieldSet.find('select').val('');
				}

				// Plus de bouton ajout ?
				var $addButton = $('.add-btn');
				if ($addButton.length === 0) {
					$("div[id^='client_enfants_']").last().append($('<div class="add-btn"><a href="#" class="btn btn_add btn-default btn--light btnColor_1 addChild"><span><i class="fa fa-plus fa-2x"></i></span></a></div>'));
				}
				return false;
			});
		});
    </script>
{% endblock %}
