{% extends 'home.html.twig' %}
{% form_theme form '/Form/fields.html.twig' %}
{% set responsive = getParam('template.isResponsive') %}
{% set menuLateral = getParam('template.menuLateral') %}

{% block body %}
	<div class="imgTop">
		<div class="container-fluid">
			<div class="row">
				<div class="column large-offset-3 large-5 small-12 mtl">
					<div id="divBienvenue" class="col-bienvenu">
						<img src="{{asset('/build/giropharm/img/logo.jpg')}}" alt="" class="logo-login">
					</div>
				</div>
				{# <div class="column large-offset-6 large-4 small-12"> #}
				<div class="column large-4 small-12">
					{{ include('/Common/messages.html.twig') }}
					{% if errors and errors.message != 'Bad credentials.' %}
						<div class="callout alert" data-closable>
							<button class="close-button" aria-label="Dismiss alert" type="button" data-close>
								<span aria-hidden="true">&times;</span>
							</button>
							{{ errors.message|trans(errors.messageData, 'security') }}
						</div>
					{% endif %}
					{{ form_errors(form) }}
					<div class="banner-optin vertical login formLogin">
						<h1><span class="ralewayBold">{{ 'login.mainTitle.first'|trans }}</span>{{ 'login.mainTitle.last'|trans }}
						</h1>
						<div class="row">
							<div class="column small-12">
								{{ form_start(form, {method:'post'}) }}
								<p>{{ 'login.libelleForm'|trans }}<p>
								{{ form_row(form._username)}}
								{{ form_row(form._password)}}
								{# {{ form_row(form.codePostal)}} #}
								<div class="formatDate">
									{{ 'formatDate'|trans }}
								</div>
								{{ form_row(form.captcha) }}
								<div class="text-right mtl small-mbl">
                                    <input type="hidden" name="_token" value="{{ csrf_token('authenticate') }}"/>

									{{ form_end(form) }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}
{% block footer %}
	{% if getParam('template.footer') %}
		<div class="row">
		{% include '/footer.html.twig' %}
		</div>
	{% endif %}
{% endblock %}
