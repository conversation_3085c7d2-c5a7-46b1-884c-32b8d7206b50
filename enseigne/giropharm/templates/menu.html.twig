<div id="menuWrap">
	<div class="menuWrapper container-fluid">
		<div id="menu" class="menu col-xs-12 col-sm-4">
			<div class="fixed">
				<a href="/vue/#" class="visible-xs close glyphicon glyphicon-remove-circle">
					<span class="sr-only">Toggle navigation</span>
				</a>
				<div class="hidden-xs logo col-xs-12">
					<img alt="logo" src="/images/carteCeCFidelity.png">
				</div>
				<div class="hidden-xs highlight col-xs-offset-1 col-xs-10">
					<p class="strong">{{ client.civilite|trans }} {{client.nom}} {{client.prenom}}</p>
					<p>{{ 'client.pointAtteint' | trans}} <span class="strong">{{ cagnotte }} {{ cagnotteType | trans({'%count%' : cagnotte })}}</span><br/>
						{{ 'client.surVotreCompte' | trans}}.<br/>
						{%  if cagnotteReste != 0 %}
							{{ 'client.ilVousManque' |trans}} {{ cagnotteReste }} {{ cagnotteType | trans({'%count%' : cagnotteReste})}} {{ 'client.pourRecevoirVotreChequeFid' |trans}}<br>
						{% else %}
							{{ 'client.zeroptsrestant'|trans }}
						{% endif %}
					</p>
					<a href="{{path('home_logout')}}" class="col-center btn btn-default btn-deconnexion">
						<span class="glyphicon glyphicon-remove-circle">&nbsp;</span>{{'client.deconnexion'|trans}}
					</a>
				</div>
				<div class="listeMenu col-xs-offset-1 col-xs-10">
					<ul>
						<li>
							<a class="ralewayBold" href="/vue/#accueil">{{'menu.accueil'|trans}}</a>
						</li>
						<li>
							<a class="ralewayBold" href="/vue/#maCarte">{{'menu.maCarte'|trans}}</a>
						</li>
						<li>
							<a class="ralewayBold" href="/vue/#mesAvantages">{{'menu.mesAvantages'|trans}}</a>
						</li>
						<li>
							<a class="ralewayBold" href="/vue/#monCompte">{{'menu.monCompte'|trans}}</a>
						</li>
						<li>
							<a class="ralewayBold" href="/vue/#monBateau">{{'menu.monBateau'|trans}}</a>
						</li>
						<li>
							<a class="ralewayBold" href="/vue/#monProgramme">{{'menu.monProgramme'|trans}}</a>
						</li>
					</ul>
					<div class="visible-xs highlight">
						<a href="{{path('home_logout')}}" class="col-center btn btn-default btn-deconnexion">
							<span class="glyphicon glyphicon-remove-circle">&nbsp;</span>{{'client.deconnexion'|trans}}
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
