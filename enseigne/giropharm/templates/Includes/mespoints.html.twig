<div class="bubble-container is-half mesPoints" data-block-id="mesPoints">
    <p class="center titreBubble"><span class="ralewayBold">{{ 'menu.mesPoints'|trans }}</span></p>
	<div class="bubble bubble--white">
        {% if getParam('carte.donuts') %}
            <div id="donuts"></div>
        {% endif %}
        <p id="blocPoint" class="isGrey">
            {{ client.civilite }} {{client.prenom}} {{client.nom}}, {{ 'client.pointAtteint' |trans}}<br>
            <span id="ptsatteind" class="ralewayBold">{{ cagnotte|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotte })}}</span><br>
        </p>
        <p class="isGrey">
            {{ 'client.surVotreCompte' |trans}}<br>
            {%  if cagnotteReste != 0 %}
                {{ 'client.ilVousManque' |trans}} {{ cagnotteReste|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%' : cagnotteReste })}} {{ 'client.pourRecevoirVotreChequeFid' |trans}}<br>
            {% else %}
                {{ 'client.zeroptsrestant'|trans }}
            {% endif %}
        </p>
    </div>
</div>
