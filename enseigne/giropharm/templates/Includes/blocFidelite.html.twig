{% if featureChecker.hasChequeFidelite %}
<div class="bubble-container is-third">
	<img class="bloc-icon" src="{{ asset('/build/giropharm/img/cheque-fid-icone.jpg')}}" />
	<div class="titreBubble">
		<p class="center mtm">{{ 'fidelite.remiseCheque' |trans }}</p>
	</div>
	<div class="bubble bubble--white">
		{% if client.dateDernierCheque is empty %}
			{{ 'cheque.aucunChequeFidelite'|trans }}
		{% else %}
			<span class="black">{{ 'avantage.dernierChequeFideliteEmisLe'|trans }}</span> <span class="mainColor">{{ client.dateDernierCheque|date("d/m/Y") }}</span><br>
			<span class="black">{{ 'avantage.dUnMontantDe'|trans }}</span>
			{% if getParam('remise.bienvenue.pourcentageAllow') %}
				{% if client.montantDernierCheque > 0 %}
					{{ client.montantDernierCheque }}<sup>€</sup>
				{% else %}
					{% if client.pourcentageDernierCheque > 0 %}
						-{{ client.pourcentageDernierCheque }}<sup>%</sup>
					{% else %}
						{{ client.montantDernierCheque }}<sup>€</sup>
					{% endif %}
				{% endif %}
			{% else %}
				<span class="mainColor">{{ client.montantDernierCheque }}<sup>€</sup></span>
			{% endif %}
			<span style="white-space:nowrap;" class="mainColor">(N°{{ client.NumDernierCheque }})</span><br>
			{% if client.dateFinValiditeDernierCheque is not empty %}
				<span class="black">{{ 'avantage.dateDeValidite'|trans }}</span><br><span class="mainColor">{{ client.dateFinValiditeDernierCheque|date("d/m/Y") }}</span>
			{% endif %}
			{% if client.dateEncaissementDernierCheque is empty %}
                {% if featureChecker.hasChequeFidelite
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Fidelite.value]
                %}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'anniv': 'false'} ) }}" class="btn btn-default btn-telecharger ralewayBold"><span class="glyphicon glyphicon-download-alt">&nbsp;</span>{{ 'anniversaire.telechargerMonCheque'|trans }}</a> #}
				{% endif %}
			{% else %}
				<span class="ralewayBold offreUtilisee mainColor">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncaissementDernierCheque|date("d/m/Y") }}</span><br>
			{% endif %}
		{% endif %}
		<br>
		{%  if cagnotteReste != 0 %}
			<span class="ralewayBold mainColor">{{ 'client.ilVousManque' |trans}} {{ cagnotteReste|cagnotte_format(cagnotteFormater) }} {{ cagnotteType | trans({'%count%': cagnotteReste})}} {{ 'client.pourRecevoirVotreProchaineChequeFid' |trans}}</span>
		{% else %}
			{{ 'client.zeroptsrestant'|trans }}
		{% endif %}
	</div>
</div>
{% endif %}
