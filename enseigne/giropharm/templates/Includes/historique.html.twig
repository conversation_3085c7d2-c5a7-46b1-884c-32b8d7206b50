<div class="bubble-container is-half historique" data-block-id="historique">
    <p class="center titreBubble">
        <span class="ralewayBold">
            {{ 'menu.monHistorique'|trans }}
        </span>
    </p>

    <div class="bubble bubble--white">
        {% if client.mouvementPoints|length %}
            <ul class="historique-list is-flex is-flex-column">
            {% for mouvementPoint in client.mouvementPoints %}
                <li class="is-flex">
                    <div class="historique-points">
                        {% if mouvementPoint.isBonusBienvenue() %}
                            Vos points de Bienvenue : <br>
                        {% elseif mouvementPoint.isGenerationChequeFid() %}
                            Vous avez reçu votre chèque fidélité :
                        {% elseif mouvementPoint.isUtilisationChequeFid() %}
                             Vous avez utilisé votre chèque fidélité :
                        {% elseif mouvementPoint.isUtilisationCoupon() %}
                            Vous avez utilisé un coupon : <br>
                        {% elseif mouvementPoint.isAnnulationCheque() %}
                             {{ mouvementPoint.operation }} :
                        {% elseif mouvementPoint.isPositif() %}
                            {{ 'client.historiqueLabel'|trans|raw }}
                        {% else %}
                            {{ 'client.historiqueLabelNegatif'|trans|raw }}
                        {% endif %}
                        <span class="ralewayBold historique-points-color">
                        {% if cagnotteTypeSymbole %}
                            {{ mouvementPoint.nbPoints|cagnotte_format(cagnotteFormater) }} {{ cagnotteTypeSymbole|trans({'%count%': mouvementPoint.nbPoints}) }}
                        {% else %}
                            {{ mouvementPoint.nbPoints|cagnotte_format(cagnotteFormater) }} {{ cagnotteType|trans({'%count%': mouvementPoint.nbPoints}) }}
                        {% endif %}
                        </span>
                    </div>
                    <div class="floatRight">
                        {{ mouvementPoint.dateOperation|date("d/m/Y") }}
                    </div>
                </li>
            {% endfor %}
            </ul>
        {% else %}
            <div class="notice">{{ 'client.journalPoint.noJournalPoint' | trans }}</div>
        {% endif %}
    </div>
</div>
<div class="bubble-container is-half">
    <img class="img-responsive" src="{{ asset('/build/giropharm/img/modalite.jpg') }}" />
</div>
