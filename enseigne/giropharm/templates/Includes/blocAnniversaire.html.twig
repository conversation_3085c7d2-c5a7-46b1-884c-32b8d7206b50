{% if featureChecker.hasChequeAnniversaire %}
<div class="bubble-container is-third">
	<img class="bloc-icon" src="{{ asset('/build/giropharm/img/anniversaire-icone.jpg')}}" />
	<div class="titreBubble">
		<p class="center mtm">{{ 'anniversaire.remiseAnniversaire' |trans }}</p>
	</div>
	<div class="bubble bubble--white">
		{% if client.dateDernierChequeAnniv is empty %}
			{{ 'anniversaire.aucunCheque'|trans }}
		{% else %}
			<span class="black">{{ 'anniversaire.dernierChequeEmisLe'|trans }}</span> <span class="mainColor">{{ client.dateDernierChequeAnniv|date("d/m/Y") }}</span>
			<br>
			<span style="white-space:nowrap;" class="mainColor">(N°{{ client.NumDernierChequeAnniv }})</span>
			<br>
			{% if client.dateFinValiditeDernierChequeAnniv %}
				<span class="black">{{ 'avantage.dateDeValidite'|trans }}</span><br>
				<span class="mainColor">{{ client.dateFinValiditeDernierChequeAnniv|date("d/m/Y") }}</span><br>
			{% endif %}
			{% if client.dateEncaissementDernierChequeAnniv is empty %}
                {% if featureChecker.hasChequeAnniversaire
                    and allDownloadSecurityChecker[enum('App\\Services\\Cheque\\ChequeType').Anniversaire.name]
                %}
					<br>
					<a target="_blank" href="{{ path("generateECheque",{'anniv': 'true', 'bienvenue':'false'} ) }}" class="btn btn-default btn-telecharger ralewayBold"><i class="fa fa-download fa-lg mrs" aria-hidden="true"></i>{{ 'anniversaire.telecharger'|trans }}</a>
				{% endif %}
			{% else %}
				<span class="mainColor">{{ 'avantage.encaisseLe'|trans }} {{ client.dateEncaissementDernierChequeAnniv|date("d/m/Y") }}</span>
			{% endif %}
		{% endif %}
	</div>
</div>
{% endif %}
