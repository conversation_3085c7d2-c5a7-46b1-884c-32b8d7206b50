{% extends 'client_activate/layout.html.twig' %}
{% form_theme form 'Form/fields.html.twig' %}

{% block body %}
    {{ parent() }}

	<div class="container-fluid">
		<div class="row">
			<div class="column large-12 small-12">
				{% include 'Common/messages.html.twig' %}
				{{ form_errors(form) }}
				<div class="column large-12 small-12 activation">
					<div class="activation-puces">
						<div class="activation-puce">
							01
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce activation-puce-active">
							02
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce">
							03
						</div>
					</div>
					<div  class="centered">
						<h1>{{ 'activation.title' | trans | raw}}</h1>
					</div>
					<div class="activation-line"></div>
					<div class="activation-etape">
						{{ 'activation.activation2' | trans }}
					</div>
					<div class="row">
						<p class="column large-5 large-centered small-12 small-centered activation-etape1-text">	
							{{ 'activation.compte' | trans }}
						</p>
					</div>
					{{ form_start(form, {method:'post'}) }}
					<div class="fieldsetWith large-8 large-centered activation-compte">
						<div class="row">
							<div class="column small-12 medium-6 first">
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ form_label(form.civilite) }}</span>
									<div class="has-error">{{ form_errors(form.civilite)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.civilite) }}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ form_label(form.nom) }}</span>
									<div class="has-error">{{ form_errors(form.nom)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.nom) }}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ form_label(form.prenom) }}</span>
									<div class="has-error">{{ form_errors(form.prenom)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.prenom) }}</div>
								</div>
								<div class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ form_label(form.dateNaissance)}}</span>
									</div>
									<div class="has-error">{{ form_errors(form.dateNaissance)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.dateNaissance) }}</div>
								</div>
								<div class="checked mobile {% if client.telephoneMobile is empty %}emptyInput{% endif %}">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">
									{{ form_label(form.telephoneMobile)}}
									<div class="has-error">{{ form_errors(form.telephoneMobile)}}
										</div>
									</span>
									<div class="form-group form-group--margin">{{ form_widget(form.telephoneMobile)}}</div>
								</div>
								<div class="checked {% if client.Email is empty %}emptyInput{% endif %}">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ form_label(form.email)}}
										<div class="has-error">{{ form_errors(form.email)}}</div>
										<div class="form-group form-group--margin">{{ form_widget(form.email)}}</div>
									</span>
								</div>
							</div>
							<div class="column small-12 medium-6">
							<div id="adresse" class="checked">
									<div class="form-group-head">
										<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
										<span class="ralewayBold">{{ 'compte.Adresse'|trans }}</span>
									</div>
									<div class="form-group form-group--margin">
										{{ form_row(form.numero)}}
										{{ form_row(form.voie)}}
										{{ form_row(form.escalier)}}
										{{ form_row(form.batiment)}}
										{{ form_row(form.lieuDit)}}									
										<div>
											<label for="client_codepostal" class="required">{{ 'client.codepostal'|trans }} :</label>
											<div class="has-error">{{ form_errors(form.codepostal)}}</div>
											{{ form_widget(form.codepostal) }}
										</div>
										<div>
											<label for="client_ville" class="required">{{ 'client.ville'|trans }} :</label>
											<div class="has-error">{{ form_errors(form.ville)}}</div>
											{{ form_widget(form.ville) }}
										</div>
										{{ form_row(form.codePaysClient)}}
									</div>
								</div>
								{# <div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg form-icon mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'client.magasinRattachement'|trans }}* :</span>
									<div class="has-error">{{ form_errors(form.magasin)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.magasin)}}</div>
								</div>	#}
							</div>
						</div>
						<p>{{'activation.cgu'|trans}}</p>
						<div class="column small-12 centered">
							<button type="submit" class="btn btn-default btn-valider-modif ralewayBold"><i class="fa fa-check-circle fa-lg mrs" aria-hidden="true"></i>{{'activation.save'|trans}}</button>
						</div>
						<p>{{'client.mentionsObligatoire'|trans}}</p>
						<p class="cnilMessageInfoRecueillies">
							{{ "cnil.messageInfoRecueillies"|trans({'%lien%':app.request.getSchemeAndHttpHost})|raw}}
						</p>
					</div>
					{{ form_widget(form._token) }}
					{{ form_end(form, {render_rest:false}) }}
				</div>
			</div>
		</div>
	</div>

{% endblock %}


{% block javascripts %} 
	<script type="text/javascript">
		$(document).ready(function() {
			/**
			* Renseignement automatique des checkbox d'optin
			*/
			var autoOptin = function(fromField, checkField) {
				fromField.on('keyup', function(event) {
					if ($(this).val()) {
						checkField.prop("checked", true);
					}
					else {
						checkField.prop("checked", false);
					}
				});
			}
			var $client_adresse = $('#client_numero, #client_voie, #client_escalier, #client_batiment, #client_lieuDit, #client_codepostal, #client_ville, #client_codePaysClient'),
			$client_telephoneMobile = $('#client_telephoneMobile'),
			$client_email = $('#client_email'),
			$client_envoieSmsInterne = $('#client_envoieSmsInterne'),
			$client_envoieEmailInterne = $('#client_envoieEmailInterne'),
			$client_envoiCourrierInterne = $('#client_envoiCourrierInterne');

			autoOptin($client_adresse, $client_envoiCourrierInterne);
			autoOptin($client_telephoneMobile, $client_envoieSmsInterne);
			autoOptin($client_email, $client_envoieEmailInterne);
		});
	</script>
{% endblock %}