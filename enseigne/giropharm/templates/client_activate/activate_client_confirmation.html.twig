{% extends 'client_activate/layout.html.twig' %}

{% block body %}
    {{ parent() }}

	<div class="container-fluid">
		<div class="row">
			<div class="column large-12 small-12">
				<div class="column large-12 small-12 activation">
					<div class="activation-puces">
						<div class="activation-puce">
							01
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce">
							02
						</div>
						<div class="activation-puce-separator">
						</div>
						<div class="activation-puce activation-puce-active">
							03
						</div>
					</div>
					<div  class="centered">
						<h1>{{ 'activation.title' | trans | raw}}</h1>
					</div>
					<div class="activation-line"></div>
					<div class="activation-etape">
						{{ 'activation.activation3' | trans }}
					</div>
					<div class="row">
						<p class="column large-5 large-centered small-12 small-centered activation-etape3-text1">	
							{{ 'activation.votreCarteMaCarteEnseigne' | trans }}
						</p>
					</div>
					<div class="row">
						<p class="column large-5 large-centered small-12 small-centered activation-etape3-text2">	
							{{ 'activation.votreCarteMaCarteEnseigne2' | trans }}
						</p>
					</div>
					<div class="mtl small-mbl text-center">
						<a href="{{path('home_login')}}" class="btn btn-default btnColor_1 activation-button-confirmation ralewayBold">
							{{ 'activation.voirCompte' |trans }}
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	
{% endblock %}