<select id="{{ form.vars.name }}" name="{{ form.vars.full_name }}" class="form-control">
	<option value="">{{ form.vars.empty_value| trans }}</option>
{% for id, choice in form.vars.choices %}
	<option 
	{% if choice.value == form.vars.value%} 
	selected="selected" 
	{% endif %} 
	value="{{ choice.value }}">
	{{choice.label}} ({{ choice.value }})
	</option>
{% endfor %}
</select>
{% do form.setRendered %}
