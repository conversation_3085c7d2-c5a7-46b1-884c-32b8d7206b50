{% form_theme form 'BraincraftedBootstrapBundle:Form:bootstrap.html.twig' %}
{% block captcha_widget %}
    {% if is_human %}
        -
    {% else %}
        	<div class="row">
				<div class="col-md-6 col-sm-6 col-xs-12">
					<label for="{{ id }}"><img class="captcha-img" id="{{ image_id }}" src="{{ captcha_code }}" alt="captcha" title="captcha" width="{{ captcha_width }}" height="{{ captcha_height }}" /></label>
					{% if reload %}
	                <script type="text/javascript">
	                    function reload_{{ image_id }}() {
	                        var img = document.getElementById('{{ image_id }}');
	                        img.src = '{{ captcha_code }}?n=' + (new Date()).getTime();
	                    }
	                </script>
				</div>
				<div class="col-md-6 col-sm-6 col-xs-12">
					{{ form_widget(form) }}
				</div>
			</div>			
			<div class="span-captcha" onclick="javascript:reload_{{ image_id }}();" type="button">
			[{{ 'login.autreImage'|trans }}]</div>
            {% endif %}
    {% endif %}
{% endblock %}

{% block form_label %}
	{% if label is not same as(false) %}
		{% if not compound %}
			{% set label_attr = label_attr|merge({'for': id}) %}
		{% endif %}
		{% if required %}
			{% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' required')|trim}) %}
		{% endif %}
		{% if label is empty %}
			{% set label = name|humanize %}
		{% endif %}
        {% if compound %}
            <legend {% for attrname, attrvalue in label_attr %} {{ attrname }}="{{ attrvalue }}"{% endfor %}>{{ label|trans({}, translation_domain)|raw}}{% if required %}<span class="asterisque">*</span>{% endif %}{% if help is defined and help %}<br/><span id="{{ id }}_help" class="help">{{ help|trans({}, translation_domain) }}</span>{% endif %}</legend>
        {% else %}
		    <label{% for attrname, attrvalue in label_attr %} {{ attrname }}="{{ attrvalue }}"{% endfor %}>{{ label|trans({}, translation_domain)|raw}}{% if required %}<span class="asterisque">*</span>{% endif %}{% if help is defined and help %}<br/><span id="{{ id }}_help" class="help">{{ help|trans({}, translation_domain) }}</span>{% endif %}</label>
        {% endif %}
	{% endif %}
{% endblock form_label %}