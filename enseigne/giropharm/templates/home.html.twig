<!doctype html>
<html>
	<head>
		<meta charset="UTF-8" />
		<title>{% block title %}{% endblock %}{{ 'title' | trans }} </title>
{#        <base href="{{ app.request.getSchemeAndHttpHost() }}/"/>#}
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        {{ encore_entry_link_tags('app') }}
{#		<link rel="stylesheet" type="text/css" href="{{ asset('css/foundation.min.css') }}" />#}
{#		<link rel="stylesheet" type="text/css" href="{{ asset('css/fontawesome-all.min.css') }}" />#}
{#		<link rel="stylesheet" type="text/css" href="{{ asset('css/styles.css') }}" />#}
		{# <link rel="stylesheet" type="text/css" href="{{ asset('css/leaflet.css') }}" /> #}
		{% block stylesheets %}
		{% endblock %}

		{% block favicon %}
			<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />
		{% endblock %}

		{% block meta %}
		{% endblock %}
	</head>
	<body>
		<div class="container">
			{% block body %}{% endblock %}
		</div>
		{% block footer %}{% endblock %}

		{% if getParam('carte.donuts') %}
			<script type="text/javascript" src="{{asset('js/d3.min.js')}}"></script>
		{% endif %}
        {{ encore_entry_script_tags('app') }}

{#		<script type="text/javascript" src="{{asset('js/jquery-2.1.0.min.js')}}"></script>#}
{#		<script type="text/javascript" src="{{asset('js/foundation.min.js')}}"></script>#}
{#		<script type="text/javascript" src="{{asset('js/modernizr.min.js')}}"></script>#}
{#		<script type="text/javascript" src="{{asset('js/respond.min.js')}}"></script>#}
{#        <script type="text/javascript" src="{{asset('js/tarteaucitron/tarteaucitron.js') }}"></script>#}

        <script type="text/javascript">
            tarteaucitron.init({
                "privacyUrl": "", /* Privacy policy url */

                "hashtag": "#tarteaucitron", /* Open the panel with this hashtag */
                "cookieName": "tarteaucitron", /* Cookie name */

                "orientation": "bottom", /* Banner position (top - bottom) */

                "showAlertSmall": false, /* Show the small banner on bottom right */
                "cookieslist": false, /* Show the cookie list */

                "showIcon": false, /* Show cookie icon to manage cookies */
                "iconPosition": "BottomRight", /* Position of the icon between BottomRight, BottomLeft, TopRight and TopLeft */

                "adblocker": false, /* Show a Warning if an adblocker is detected */

                "DenyAllCta" : true, /* Show the deny all button */
                "AcceptAllCta" : true, /* Show the accept all button when highPrivacy on */
                "highPrivacy": true, /* HIGHLY RECOMMANDED Disable auto consent */

                "handleBrowserDNTRequest": false, /* If Do Not Track == 1, disallow all */

                "removeCredit": true, /* Remove credit link */
                "moreInfoLink": true, /* Show more info link */
                "useExternalCss": false, /* If false, the tarteaucitron.css file will be loaded */

                //"cookieDomain": ".my-multisite-domaine.fr", /* Shared cookie for subdomain website */

                "readmoreLink": "", /* Change the default readmore link pointing to tarteaucitron.io */

                "mandatory": true /* Show a message about mandatory cookies */
            });
        </script>
		<script type="text/javascript">
		tarteaucitron.user.gajsUa = 'UA-XXXXXXXX-X';
		tarteaucitron.user.gajsMore = function () { /* add here your optionnal _ga.push() */ };
		(tarteaucitron.job = tarteaucitron.job || []).push('gajs');
		</script>
        {% block javascripts %}{% endblock %}
	</body>
</html>
