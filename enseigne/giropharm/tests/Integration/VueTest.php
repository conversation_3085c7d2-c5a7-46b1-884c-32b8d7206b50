<?php

namespace Giropharm\Tests\Integration;

use App\Services\NewClientsService;
use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;
use Giropharm\Tests\Fixtures\WireMock\MockLoader;
use Symfony\Component\DomCrawler\Crawler;
use WireMock\Client\WireMock;

class VueTest extends ApiTestCase
{
    private static $wireMock;
    private static $client;

    public function getVue(): ?Crawler
    {
        return self::$client->request('GET', '/vue/');
    }

    protected function setUp(): void
    {
        parent::setUp();

        self::$client = self::createClient();
        self::$wireMock = WireMock::create('wiremock', 8080);
        MockLoader::configureMocks(self::$wireMock, self::getContainer()->getParameter('kernel.project_dir'));

        $this->loginUser();
    }

    public function tearDown(): void
    {
        parent::tearDown();
        if (isset(self::$wireMock) && self::$wireMock->isAlive()) {
            self::$wireMock->reset();
        }
    }

    private function loginUser(): void
    {
        $user = self::getContainer()->get(NewClientsService::class)->authenticate('1001001000012', '25/10/1949');
        self::$client->loginUser($user, 'secured_area');
    }

    public function test_client_resume(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'resume');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_magasin(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'magasin');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_carte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'macarte');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_passbook(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'passbook');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_mes_points(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'mesPoints');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_historique(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'historique');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_mon_compte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'monCompte');
        $reindentedBlock = $this->scrubTokens($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }
}
