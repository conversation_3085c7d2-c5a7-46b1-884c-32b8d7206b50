<?php

namespace Giropharm\Tests\Integration;

use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;
use Normandiepharma\Tests\Fixtures\WireMock\MockLoader;
use WireMock\Client\WireMock;

class AnonymousUserTest extends ApiTestCase
{
    private static $client;

    protected function setUp(): void
    {
        parent::setUp();

        self::$client = self::createClient();
        self::$wireMock = WireMock::create('wiremock', 8080);
        MockLoader::configureMocks(self::$wireMock, self::getContainer()->getParameter('kernel.project_dir'));
    }

    public function test_index(): void
    {
        $crawler = self::$client->request('GET', '/');
        $reindentedBlock = $this->indenter->indent($crawler->outerHtml());
        $reindentedBlock = $this->scrubTokens($reindentedBlock);
        $reindentedBlock = $this->scrubCaptcha($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }
}
