<html>
    <head>
        <meta charset="UTF-8">
        <title>ENSEIGNE </title>
        <base href="http://localhost/">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" type="text/css" href="/giropharm/css/foundation.min.css">
        <link rel="stylesheet" type="text/css" href="/giropharm/css/fontawesome-all.min.css">
        <link rel="stylesheet" type="text/css" href="/giropharm/css/styles.css">
        <link rel="icon" type="image/x-icon" href="/giropharm/favicon.ico">
    </head>
    <body>
        <div class="container">
            <div class="imgTop">
                <div class="container-fluid">
                    <div class="row">
                        <div class="column large-offset-3 large-5 small-12 mtl">
                            <div id="divBienvenue" class="col-bienvenu">
                                <img src="/giropharm/images/logo.jpg" alt class="logo-login">
                            </div>
                        </div>
                        <div class="column large-4 small-12">
                            <div class="banner-optin vertical login formLogin">
                                <h1>
                                    <span class="ralewayBold">Identifiez</span>-vous

                                </h1>
                                <div class="row">
                                    <div class="column small-12">
                                        <form name="login" method="post">
                                            <p>Remplissez les 2 champs ci-dessous :</p>
                                            <p></p>
                                            <div>
                                                <label class="sr-only required" for="login__username">Username<span class="asterisque">*</span></label>
                                                <input type="text" id="login__username" name="login[_username]" required="required" placeholder="N° de carte ENSEIGNE">
                                            </div>
                                            <div>
                                                <label class="sr-only required" for="login__password">Password<span class="asterisque">*</span></label>
                                                <input type="text" id="login__password" name="login[_password]" required="required" placeholder="Date de naissance">
                                            </div>
                                            <div class="formatDate">
JJ/MM/AAAA
</div>
                                            <div>
                                                <div class="form-group">
                                                    <div class="h-captcha" data-sitekey="c988d044-d2ba-438a-a138-818c1a287f4e"></div>
                                                    <script src="https://hcaptcha.com/1/api.js" async defer></script>
                                                </div>
                                            </div>
                                            <div class="text-right mtl small-mbl">
                                                <input type="hidden" name="_token" value="csrf-token">
                                                <div>
                                                    <button type="submit" id="login_submit" name="login[submit]" class="btn">Valider</button>
                                                </div>
                                                <input type="hidden" id="login__token" name="login[_token]" data-controller="csrf-protection" value="csrf-token">
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <footer class="footer">
                <div class="footer-top">
                    <p> <a href="/giropharm/images/cgu.pdf">Conditions du programme</a> - <a href="/giropharm/images/mentions.pdf">Mentions légales</a> </p>
                </div>
                <div class="column small-12 medium-6">
                    <p> © ZeFid' by Aquitem. Tous droits réservés | <a href="https://www.groupe-aquitem.fr/zefid/" target="_blank">Création Zefid’ by Aquitem</a> </p>
                </div>
                <div class="column small-12 medium-6 small-text-center medium-text-right"> <a href="http://www.giropharm.fr" class="footer-bottom-link">www.giropharm.fr</a> </div>
            </footer>
        </div>
        <script type="text/javascript" src="/giropharm/js/d3.min.js"></script>
        <script type="text/javascript" src="/giropharm/js/jquery-2.1.0.min.js"></script>
        <script type="text/javascript" src="/giropharm/js/foundation.min.js"></script>
        <script type="text/javascript" src="/giropharm/js/modernizr.min.js"></script>
        <script type="text/javascript" src="/giropharm/js/respond.min.js"></script>
        <script type="text/javascript" src="/giropharm/js/tarteaucitron/tarteaucitron.js"></script>
        <script type="text/javascript">
            tarteaucitron.init({
                "privacyUrl": "", /* Privacy policy url */

                "hashtag": "#tarteaucitron", /* Open the panel with this hashtag */
                "cookieName": "tarteaucitron", /* Cookie name */

                "orientation": "bottom", /* Banner position (top - bottom) */

                "showAlertSmall": false, /* Show the small banner on bottom right */
                "cookieslist": false, /* Show the cookie list */
                
                "showIcon": false, /* Show cookie icon to manage cookies */
                "iconPosition": "BottomRight", /* Position of the icon between BottomRight, BottomLeft, TopRight and TopLeft */

                "adblocker": false, /* Show a Warning if an adblocker is detected */

                "DenyAllCta" : true, /* Show the deny all button */
                "AcceptAllCta" : true, /* Show the accept all button when highPrivacy on */
                "highPrivacy": true, /* HIGHLY RECOMMANDED Disable auto consent */

                "handleBrowserDNTRequest": false, /* If Do Not Track == 1, disallow all */

                "removeCredit": true, /* Remove credit link */
                "moreInfoLink": true, /* Show more info link */
                "useExternalCss": false, /* If false, the tarteaucitron.css file will be loaded */

                //"cookieDomain": "__token__", /* Shared cookie for subdomain website */

                "readmoreLink": "", /* Change the default readmore link pointing to tarteaucitron.io */
                
                "mandatory": true /* Show a message about mandatory cookies */
            });
        </script>
        <script type="text/javascript">
		tarteaucitron.user.gajsUa = 'UA-XXXXXXXX-X';
		tarteaucitron.user.gajsMore = function () { /* add here your optionnal _ga.push() */ };
		(tarteaucitron.job = tarteaucitron.job || []).push('gajs');
		</script>
    </body>
</html>