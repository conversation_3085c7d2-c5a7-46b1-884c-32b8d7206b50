<div id="monCompte" class data-block-id="monCompte">
    <div class="row">
        <div class="column small-12 large-8 large-offset-4">
            <form name="client" method="post" action="/vue/">
                <h2>
                    Mes <span class="ralewayBold">compte</span>
                </h2>
                <div class="separator"></div>
                <p class="sous-titre ralewayBold">
                    Aidez-nous à mieux vous connaître !
                    <br>
                    Les informations que vous nous confiez nous permettont de vous proposer des avantages personnalisés

                </p>
                <div class="fieldsetWith">
                    <div class="row">
                        <div class="column small-12 medium-6 first">
                            <div class="checked">
<i class="fa fa-arrow-circle-right form-icon mrs"></i>
<span>
MME JIMMY HENDRIX
</span>
</div>
                            <div class="checked">
                                <div class="form-group-head">
                                    <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>

                                    <span class="ralewayBold">
                                        <label for="client_dateNaissance" class="required">Date de naissance<span class="asterisque">*</span></label>
                                    </span>
                                </div>
                                <div class="form-group form-group--margin">
                                    <input type="date" id="client_dateNaissance" name="client[dateNaissance]" required="required" value="1949-10-25">
                                </div>
                            </div>
                            <div class="checked">
<i class="fa fa-arrow-circle-right form-icon mrs"></i>
<span>
Membre depuis le 11/08/2020
</span>
</div>
                            <div class="checked">
                                <i class="fa fa-arrow-circle-right form-icon mrs"></i>
<span class="ralewayBold">Mon magasin</span>:

                                <p class="form-group form-group--margin">
                                    PHARMACIE ARC EN CIEL
                                    <br>
                                    19 RUE ALPHONSE DAUDET
                                    <br>
                                    31170 TOURNEFEUILLE
                                    <br>
                                    0561862154

                                </p>
                            </div>
                            <div class="checked mobile ">
                                <div class="form-group-head">
                                    <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>

                                    <span class="ralewayBold">
                                        <label for="client_telephoneMobile">Mobile</label>
                                        <div class="has-error"></div>
                                    </span>
                                </div>
                                <div class="form-group form-group--margin">
                                    <input type="text" id="client_telephoneMobile" name="client[telephoneMobile]" maxlength="15" value="0606060606">
                                </div>
                            </div>
                            <div class="checked mobile ">
                                <div class="form-group-head">
                                    <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>

                                    <span class="ralewayBold">
                                        <label for="client_telephoneFixe">Téléphone Fixe</label>
                                        <div class="has-error"></div>
                                    </span>
                                </div>
                                <div class="form-group form-group--margin">
                                    <input type="text" id="client_telephoneFixe" name="client[telephoneFixe]" maxlength="15" value="0505050500">
                                </div>
                            </div>
                            <div class="checked ">
                                <div class="form-group-head">
                                    <i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>

                                    <span class="ralewayBold">
                                        <label for="client_email" class="required">Email<span class="asterisque">*</span></label>
                                        <div class="has-error"></div>
                                    </span>
                                </div>
                                <div class="form-group form-group--margin">
                                    <input type="email" id="client_email" name="client[email]" required="required" maxlength="90" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        <div class="column small-12 medium-6 ">
                            <div id="adresse" class="checked">
                                <div class="form-group-head">
<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
<span class="ralewayBold">Adresse</span>
</div>
                                <div class="form-group form-group--margin">
                                    <div>
                                        <label for="client_numero">Numéro</label>
                                        <input type="text" id="client_numero" name="client[numero]" maxlength="20" value="345">
                                    </div>
                                    <div>
                                        <label for="client_voie">Rue</label>
                                        <input type="text" id="client_voie" name="client[voie]" maxlength="40" value="AVENUE DE TIVOLI">
                                    </div>
                                    <div>
                                        <label for="client_escalier">Escalier</label>
                                        <input type="text" id="client_escalier" name="client[escalier]" maxlength="38" value="2">
                                    </div>
                                    <div>
                                        <label for="client_batiment">Bâtiment</label>
                                        <input type="text" id="client_batiment" name="client[batiment]" maxlength="38" value="4">
                                    </div>
                                    <div>
                                        <label for="client_lieuDit">Lieu-dit</label>
                                        <input type="text" id="client_lieuDit" name="client[lieuDit]" maxlength="40" value="ALIENOR">
                                    </div>
                                    <div>
                                        <label for="client_codepostal" class="required">Code Postal<span class="asterisque">*</span></label>
                                        <input type="text" id="client_codepostal" name="client[codepostal]" required="required" class="codepostal" maxlength="10" value="33110">
                                    </div>
                                    <div>
                                        <label for="client_ville" class="required">Ville<span class="asterisque">*</span></label>
                                        <input type="text" id="client_ville" name="client[ville]" required="required" maxlength="40" value="LE BOUSCAT">
                                    </div>
                                    <div>
                                        <label for="client_codePaysClient" class="required">Pays<span class="asterisque">*</span></label>
                                        <select id="client_codePaysClient" name="client[codePaysClient]" required="required" class="codepays">
                                            <option value>-</option>
                                            <option value="ALA">ALAND</option>
                                            <option value="GGY">GUERNESEY</option>
                                            <option value="IMN">MAN ILES</option>
                                            <option value="JEY">JERSEY</option>
                                            <option value="MNE">MONTENEGRO</option>
                                            <option value="BLM">SAINT-BARTHELEMY</option>
                                            <option value="MAF">SAINT-MARTIN</option>
                                            <option value="SRB">SERBIE</option>
                                            <option value="TLS">TIMOR ORIENTAL</option>
                                            <option value="AFG">AFGHANISTAN</option>
                                            <option value="ZAF">AFRIQUE DU SUD</option>
                                            <option value="ALB">ALBANIE</option>
                                            <option value="DZA">ALGERIE</option>
                                            <option value="DEU">ALLEMAGNE</option>
                                            <option value="AND">ANDORRE</option>
                                            <option value="AGO">ANGOLA</option>
                                            <option value="AIA">ANGUILLA</option>
                                            <option value="ATA">ANTARCTIQUE</option>
                                            <option value="ATG">ANTIGUA-ET-BARBUDA</option>
                                            <option value="ANT">ANTILLES NEERLANDAISES</option>
                                            <option value="SAU">ARABIE SAOUDITE</option>
                                            <option value="ARG">ARGENTINE</option>
                                            <option value="ARM">ARMENIE</option>
                                            <option value="ABW">ARUBA</option>
                                            <option value="AUS">AUSTRALIE</option>
                                            <option value="AUT">AUTRICHE</option>
                                            <option value="AZE">AZERBAIDJAN</option>
                                            <option value="BHS">BAHAMAS</option>
                                            <option value="BHR">BAHREIN</option>
                                            <option value="BGD">BANGLADESH</option>
                                            <option value="BRB">BARBADE</option>
                                            <option value="BLR">BIELORUSSIE</option>
                                            <option value="BEL">BELGIQUE</option>
                                            <option value="BLZ">BELIZE</option>
                                            <option value="BEN">BENIN</option>
                                            <option value="BMU">BERMUDES</option>
                                            <option value="BTN">BHOUTAN</option>
                                            <option value="BOL">BOLIVIE</option>
                                            <option value="BIH">BOSNIE-HERZEGOVINE</option>
                                            <option value="BWA">BOTSWANA</option>
                                            <option value="BVT">BOUVET ILE</option>
                                            <option value="BRA">BRESIL</option>
                                            <option value="BRN">BRUNEI DARUSSALAM</option>
                                            <option value="BGR">BULGARIE</option>
                                            <option value="BFA">BURKINA FASO</option>
                                            <option value="BDI">BURUNDI</option>
                                            <option value="CYM">CAIMANES ILE</option>
                                            <option value="KHM">CAMBODGE</option>
                                            <option value="CMR">CAMEROUN</option>
                                            <option value="CAN">CANADA</option>
                                            <option value="CPV">CAP-VERT</option>
                                            <option value="CAF">CENTRAFRICAINE REPUBLIQUE</option>
                                            <option value="CHL">CHILI</option>
                                            <option value="CHN">CHINE</option>
                                            <option value="CXR">CHRISTMAS ILE</option>
                                            <option value="CYP">CHYPRE</option>
                                            <option value="CCK">COCOS (KEELING) ILES</option>
                                            <option value="COL">COLOMBIE</option>
                                            <option value="COM">COMORES</option>
                                            <option value="COG">CONGO</option>
                                            <option value="COD">CONGO LA REPUBLIQUE DEMOCRATIQUE DU</option>
                                            <option value="COK">COOK ILES</option>
                                            <option value="KOR">COREE SUD REPUBLIQUE DE</option>
                                            <option value="PRK">COREE DU NORD</option>
                                            <option value="CRI">COSTA RICA</option>
                                            <option value="CIV">COTE D'IVOIRE</option>
                                            <option value="HRV">CROATIE</option>
                                            <option value="CUB">CUBA</option>
                                            <option value="DNK">DANEMARK</option>
                                            <option value="DJI">DJIBOUTI</option>
                                            <option value="DOM">DOMINICAINE REPUBLIQUE</option>
                                            <option value="DMA">DOMINIQUE</option>
                                            <option value="EGY">EGYPTE</option>
                                            <option value="SLV">EL SALVADOR</option>
                                            <option value="ARE">EMIRATS ARABES UNIS</option>
                                            <option value="ECU">EQUATEUR</option>
                                            <option value="ERI">ERYTHREE</option>
                                            <option value="ESP">ESPAGNE</option>
                                            <option value="EST">ESTONIE</option>
                                            <option value="USA">ETATS UNIS</option>
                                            <option value="ETH">ETHIOPIE</option>
                                            <option value="FLK">FALKLAND ILES (MALVINAS)</option>
                                            <option value="FRO">FEROE ILES</option>
                                            <option value="FJI">FIDJI</option>
                                            <option value="FIN">FINLANDE</option>
                                            <option value="FRA" selected>FRANCE</option>
                                            <option value="GAB">GABON</option>
                                            <option value="GMB">GAMBIE</option>
                                            <option value="GEO">GEORGIE</option>
                                            <option value="SGS">GEORGIE DU SUD ET LES ILES SANDWICH DU SUD</option>
                                            <option value="GHA">GHANA</option>
                                            <option value="GIB">GIBRALTAR</option>
                                            <option value="GRC">GRECE</option>
                                            <option value="GRD">GRENADE</option>
                                            <option value="GRL">GROENLAND</option>
                                            <option value="GLP">GUADELOUPE</option>
                                            <option value="GUM">GUAM</option>
                                            <option value="GTM">GUATEMALA</option>
                                            <option value="GIN">GUINEE</option>
                                            <option value="GNB">GUINEE-BISSAU</option>
                                            <option value="GNQ">GUINEE EQUATORIALE</option>
                                            <option value="GUY">GUYANA</option>
                                            <option value="GUF">GUYANE FRANCAISE</option>
                                            <option value="HTI">HAITI</option>
                                            <option value="HMD">HEARD ILE ET MCDONALD ILES</option>
                                            <option value="HND">HONDURAS</option>
                                            <option value="HKG">HONG-KONG</option>
                                            <option value="HUN">HONGRIE</option>
                                            <option value="UMI">ILES MINEURES ELOIGNEES DES ETATS-UNIS</option>
                                            <option value="VGB">ILES VIERGES BRITANNIQUES</option>
                                            <option value="VIR">ILES VIERGES DES ETATS-UNIS</option>
                                            <option value="IND">INDE</option>
                                            <option value="IDN">INDONESIE</option>
                                            <option value="IRN">IRAN</option>
                                            <option value="IRQ">IRAK</option>
                                            <option value="IRL">IRLANDE</option>
                                            <option value="ISL">ISLANDE</option>
                                            <option value="ISR">ISRAEL</option>
                                            <option value="ITA">ITALIE</option>
                                            <option value="JAM">JAMAIQUE</option>
                                            <option value="JPN">JAPON</option>
                                            <option value="JOR">JORDANIE</option>
                                            <option value="KAZ">KAZAKSTAN</option>
                                            <option value="KEN">KENYA</option>
                                            <option value="KIR">KIRIBATI</option>
                                            <option value="KGZ">KIRGHIZISTAN</option>
                                            <option value="KWT">KOWEIT</option>
                                            <option value="LAO">LAOS</option>
                                            <option value="LSO">LESOTHO</option>
                                            <option value="LVA">LETTONIE</option>
                                            <option value="LBN">LIBAN</option>
                                            <option value="LBR">LIBERIA</option>
                                            <option value="LBY">LIBYE</option>
                                            <option value="LIE">LIECHTENSTEIN</option>
                                            <option value="LTU">LITUANIE</option>
                                            <option value="LUX">LUXEMBOURG</option>
                                            <option value="MAC">MACAO</option>
                                            <option value="MKD">MACEDOINE</option>
                                            <option value="MDG">MADAGASCAR</option>
                                            <option value="MYS">MALAISIE</option>
                                            <option value="MWI">MALAWI</option>
                                            <option value="MDV">MALDIVES</option>
                                            <option value="MLI">MALI</option>
                                            <option value="MLT">MALTE</option>
                                            <option value="MNP">MARIANNES DU NORD ILES</option>
                                            <option value="MAR">MAROC</option>
                                            <option value="MHL">MARSHALL (PAYS)</option>
                                            <option value="MTQ">MARTINIQUE</option>
                                            <option value="MUS">MAURICE</option>
                                            <option value="MRT">MAURITANIE</option>
                                            <option value="MYT">MAYOTTE</option>
                                            <option value="MEX">MEXIQUE</option>
                                            <option value="FSM">MICRONESIE ETATS FEDERES DE</option>
                                            <option value="MDA">MOLDOVA REPUBLIQUE DE</option>
                                            <option value="MCO">MONACO</option>
                                            <option value="MNG">MONGOLIE</option>
                                            <option value="MSR">MONTSERRAT</option>
                                            <option value="MOZ">MOZAMBIQUE</option>
                                            <option value="MMR">BIRMANIE</option>
                                            <option value="NAM">NAMIBIE</option>
                                            <option value="NRU">NAURU</option>
                                            <option value="NPL">NEPAL</option>
                                            <option value="NIC">NICARAGUA</option>
                                            <option value="NER">NIGER</option>
                                            <option value="NGA">NIGERIA</option>
                                            <option value="NIU">NIOUE</option>
                                            <option value="NFK">NORFOLK ILE</option>
                                            <option value="NOR">NORVEGE</option>
                                            <option value="NCL">NOUVELLE-CALEDONIE</option>
                                            <option value="NZL">NOUVELLE-ZELANDE</option>
                                            <option value="IOT">OCEAN INDIEN TERRITOIRE BRITANNIQUE DE L</option>
                                            <option value="OMN">OMAN</option>
                                            <option value="UGA">OUGANDA</option>
                                            <option value="UZB">OUZBEKISTAN</option>
                                            <option value="PAK">PAKISTAN</option>
                                            <option value="PLW">PALAOS</option>
                                            <option value="PSE">PALESTINE</option>
                                            <option value="PAN">PANAMA</option>
                                            <option value="PNG">PAPOUASIE-NOUVELLE-GUINEE</option>
                                            <option value="PRY">PARAGUAY</option>
                                            <option value="NLD">PAYS-BAS</option>
                                            <option value="PER">PEROU</option>
                                            <option value="PHL">PHILIPPINES</option>
                                            <option value="PCN">PITCAIRN</option>
                                            <option value="POL">POLOGNE</option>
                                            <option value="PYF">POLYNESIE FRANCAISE</option>
                                            <option value="PRI">PORTO RICO</option>
                                            <option value="PRT">PORTUGAL</option>
                                            <option value="QAT">QATAR</option>
                                            <option value="REU">REUNION</option>
                                            <option value="ROM">ROUMANIE</option>
                                            <option value="GBR">ROYAUME-UNI</option>
                                            <option value="RUS">RUSSIE FEDERATION DE</option>
                                            <option value="RWA">RWANDA</option>
                                            <option value="ESH">SAHARA OCCIDENTALE</option>
                                            <option value="SHN">SAINTE-HELENE</option>
                                            <option value="LCA">SAINTE-LUCIE</option>
                                            <option value="KNA">SAINT-KITTS-ET-NEVIS</option>
                                            <option value="SMR">SAINT-MARIN</option>
                                            <option value="SPM">SAINT-PIERRE-ET-MIQUELON</option>
                                            <option value="VAT">SAINT-SIEGE (ETAT DE LA CITE DU VATICAN)</option>
                                            <option value="VCT">SAINT-VINCENT-ET-LES GRENADINES</option>
                                            <option value="SLB">SALOMON ILES</option>
                                            <option value="WSM">SAMOA</option>
                                            <option value="ASM">SAMOA AMERICAINES</option>
                                            <option value="STP">SAO TOME-ET-PRINCIPE</option>
                                            <option value="SEN">SENEGAL</option>
                                            <option value="SYC">SEYCHELLES</option>
                                            <option value="SLE">SIERRA LEONE</option>
                                            <option value="SGP">SINGAPOUR</option>
                                            <option value="SVK">SLOVAQUIE</option>
                                            <option value="SVN">SLOVENIE</option>
                                            <option value="SOM">SOMALIE</option>
                                            <option value="SDN">SOUDAN</option>
                                            <option value="LKA">SRI LANKA</option>
                                            <option value="SWE">SUEDE</option>
                                            <option value="CHE">SUISSE</option>
                                            <option value="SUR">SURINAME</option>
                                            <option value="SJM">SVALBARD ET ILE JAN MAYEN</option>
                                            <option value="SWZ">SWAZILAND</option>
                                            <option value="SYR">SYRIENNE REPUBLIQUE ARABE</option>
                                            <option value="TJK">TADJIKISTAN</option>
                                            <option value="TWN">TAIWAN PROVINCE DE CHINE</option>
                                            <option value="TZA">TANZANIE REPUBLIQUE-UNIE DE</option>
                                            <option value="TCD">TCHAD</option>
                                            <option value="CZE">TCHEQUE REPUBLIQUE</option>
                                            <option value="ATF">TERRES AUSTRALES FRANCAISES</option>
                                            <option value="THA">THAILANDE</option>
                                            <option value="TGO">TOGO</option>
                                            <option value="TKL">TOKELAU</option>
                                            <option value="TON">TONGA</option>
                                            <option value="TTO">TRINITE-ET-TOBAGO</option>
                                            <option value="TUN">TUNISIE</option>
                                            <option value="TKM">TURKMENISTAN</option>
                                            <option value="TCA">TURKS AND CAIQUES ILES</option>
                                            <option value="TUR">TURQUIE</option>
                                            <option value="TUV">TUVALU</option>
                                            <option value="UKR">UKRAINE</option>
                                            <option value="URY">URUGUAY</option>
                                            <option value="VUT">VANUATU</option>
                                            <option value="VEN">VENEZUELA</option>
                                            <option value="VNM">VIET NAM</option>
                                            <option value="WLF">WALLIS ET FUTUNA</option>
                                            <option value="YEM">YEMEN</option>
                                            <option value="ZMB">ZAMBIE</option>
                                            <option value="ZWE">ZIMBABWE</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="checked mbl ptl">
                        <div class="form-group-head">
<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
<span class="ralewayBold margin-top-50">Mon foyer</span>
</div>
                        <div class="form-group form-group--margin">
                            <div class="row">
                                <div class="column small-12 medium-6 first">
                                    <div class="form-field">
                                        <div class="form-group-head">
                                            <span class="ralewayBold">
                                                <label for="client_situationFamiliale">Situation de famille :</label>
                                                <div class="has-error"></div>
                                            </span>
                                        </div>
                                        <select id="client_situationFamiliale" name="client[situationFamiliale]">
                                            <option value></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-field">
                                <label class="ralewayBold">Avez vous des enfants ?</label>
                                <div id="enfants">
                                    <div id="prototypeEnfants" data-index="3" data-prototype='&lt;div id="client_enfants___name__"&gt;&lt;div&gt; &lt;label for="client_enfants___name___prenom"&gt;Prenom&lt;/label&gt; &lt;input type="text" id="client_enfants___name___prenom" name="client[enfants][__name__][prenom]" maxlength="20" /&gt;&lt;/div&gt;&lt;div&gt; &lt;label for="client_enfants___name___dateNaissance"&gt;Date de naissance&lt;/label&gt; &lt;input type="text" id="client_enfants___name___dateNaissance" name="client[enfants][__name__][dateNaissance]" placeholder="JJ/MM/AAAA" /&gt;&lt;/div&gt;&lt;div&gt; &lt;label for="client_enfants___name___genre"&gt;Genre&lt;/label&gt; &lt;select id="client_enfants___name___genre" name="client[enfants][__name__][genre]"&gt;&lt;option value=""&gt;&lt;/option&gt;&lt;option value="G"&gt;Garçon&lt;/option&gt;&lt;option value="F"&gt;Fille&lt;/option&gt;&lt;/select&gt;&lt;/div&gt;&lt;/div&gt;'>
                                        <fieldset class="no_fieldset enfant-field">
                                            <div class="enfant-field-form">
                                                <div id="client_enfants_0">
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_0_prenom" class="required">Prenom</label>
                                                        <input type="text" id="client_enfants_0_prenom" name="client[enfants][0][prenom]" maxlength="20" value="Paul">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_0_dateNaissance" class="required">Date de naissance</label>
                                                        <input type="text" id="client_enfants_0_dateNaissance" name="client[enfants][0][dateNaissance]" placeholder="JJ/MM/AAAA" value="12/12/2015">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_0_genre" class="required">Genre</label>
                                                        <select id="client_enfants_0_genre" name="client[enfants][0][genre]">
                                                            <option value></option>
                                                            <option value="G" selected>Garçon</option>
                                                            <option value="F">Fille</option>
                                                        </select>
                                                    </div>
                                                    <div class="field field_align_label delete-btn">
                                                        <a href class="btn btn_cancel delete btn--light">
                                                            <span><i class="fa fa-times fa-2x"></i></span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                        <fieldset class="no_fieldset enfant-field">
                                            <div class="enfant-field-form">
                                                <div id="client_enfants_1">
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_1_prenom" class="required">Prenom</label>
                                                        <input type="text" id="client_enfants_1_prenom" name="client[enfants][1][prenom]" maxlength="20" value="gérard">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_1_dateNaissance" class="required">Date de naissance</label>
                                                        <input type="text" id="client_enfants_1_dateNaissance" name="client[enfants][1][dateNaissance]" placeholder="JJ/MM/AAAA" value="18/12/2015">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_1_genre" class="required">Genre</label>
                                                        <select id="client_enfants_1_genre" name="client[enfants][1][genre]">
                                                            <option value></option>
                                                            <option value="G" selected>Garçon</option>
                                                            <option value="F">Fille</option>
                                                        </select>
                                                    </div>
                                                    <div class="field field_align_label delete-btn">
                                                        <a href class="btn btn_cancel delete btn--light">
                                                            <span><i class="fa fa-times fa-2x"></i></span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                        <fieldset class="no_fieldset enfant-field">
                                            <div class="enfant-field-form">
                                                <div id="client_enfants_2">
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_2_prenom" class="required">Prenom</label>
                                                        <input type="text" id="client_enfants_2_prenom" name="client[enfants][2][prenom]" maxlength="20" value="emma">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_2_dateNaissance" class="required">Date de naissance</label>
                                                        <input type="text" id="client_enfants_2_dateNaissance" name="client[enfants][2][dateNaissance]" placeholder="JJ/MM/AAAA" value="12/12/2015">
                                                    </div>
                                                    <div class="field field_align_label">
                                                        <label for="client_enfants_2_genre" class="required">Genre</label>
                                                        <select id="client_enfants_2_genre" name="client[enfants][2][genre]">
                                                            <option value></option>
                                                            <option value="G">Garçon</option>
                                                            <option value="F" selected>Fille</option>
                                                        </select>
                                                    </div>
                                                    <div class="field field_align_label delete-btn">
                                                        <a href class="btn btn_cancel delete btn--light">
                                                            <span><i class="fa fa-times fa-2x"></i></span>
                                                        </a>
                                                    </div>
                                                    <div class="add-btn">
                                                        <a href="#" class="btn btn_add btn-default btn--light btnColor_1 addChild">
                                                            <span><i class="fa fa-plus fa-2x"></i></span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>
                            </div>
                            <div class="form-field">
                                <label class="ralewayBold">Possédez-vous un animal domestique ?</label>
                                <div>
                                    <div>
                                        <input type="checkbox" id="client_animals_0" name="client[animals][]" value="1">
                                        <label for="client_animals_0">CHIEN</label>
                                    </div>
                                    <div>
                                        <input type="checkbox" id="client_animals_1" name="client[animals][]" value="2" checked>
                                        <label for="client_animals_1">CHAT</label>
                                    </div>
                                    <div>
                                        <input type="checkbox" id="client_animals_2" name="client[animals][]" value="3">
                                        <label for="client_animals_2">NON</label>
                                    </div>
                                    <div>
                                        <input type="checkbox" id="client_animals_3" name="client[animals][]" value="5" checked>
                                        <label for="client_animals_3">AUTRES</label>
                                    </div>
                                </div>
                                <input type="text" id="client_autreAnimal" name="client[autreAnimal]" class="width-auto" value="furet">
                            </div>
                        </div>
                    </div>
                    <div class="checked mtm">
                        <div class="form-group-head">
<i class="fa fa-arrow-circle-right form-icon mrs" aria-hidden="true"></i>
<span class="ralewayBold margin-top-50">Mes centres d'intérêts </span>
</div>
                        <div class="form-group form-group--margin">
                            <span class="ralewayBold">Souhaiteriez-vous recevoir des informations sur les thèmes suivants : </span>

                            <div class="row">
                                <div class="column small-12 large-6"></div>
                            </div>
                        </div>
                    </div>
                    <p class="mtm avertissement-form">
En validant mes informations, je confirme que j'ai bien pris connaissance des <a href="/giropharm/images/cgu.pdf" target="_blank">conditions générales d'utilisation</a> de "Ma carte Giropharm" et je consens à la collecte et au traitement de mes données personnelles dans le cadre de ce dernier.
</p>
                    <div class="row submit centered">
                        <button type="submit" class="btn btn-default btn-valider-modif ralewayBold mlxl"><i class="fas fa-pen-square fa-lg mrs" aria-hidden="true"></i>Valider les modifications</button>
                    </div>
                    <p class="mentions-legales">
                        * mentions obligatoires 
                        <br>
                        Les informations recueillies à partir de ce formulaire et identifiées par un astérisque sont nécéssaires à la création et la gestion de votre compte-client. A défaut d'être renseignées, votre compte-client ne pourra pas être créé et vous ne pourrez pas participer au programme "Ma carte Giropharm" (le " Programme ") ni recevoir les offres commerciales associées. Les autres informations collectées sur le formulaire permettent de mieux vous connaître et d'améliorer les offres et services fournis dans le cadre du Programme. L'utilisation de la Carte entraîne la collecte des données afférentes à vos achats. Ces données sont utilisées exclusivement pour la gestion du Programme. Les données collectées sont conservées pendant une durée maximum de 3 ans suivant la fin de la relation commerciale, hors obligation légale d'archivage. Le responsable du traitement est la société Giropharm située sis 2 Place Gustave Eiffel Immeuble Dublin Silic, 94150 RUNGIS. Les données sont collectées sur la base de votre consentement conformément à l'article 6.1 a) du Règlement (UE) 2016/679 (Règlement Général sur la Protection des Données - RGPD). Ces données sont destinnées à toutes les pharmacies membre du réseau GIROPHARM qui participent au Programme, ainsi qu'aux prestataires et sous-traitants sélectionnées pour la gestion du Programme. Conformément aux dispositions du Règlement (UE) 2016/679 RGPD, vous disposez d'un droit d'accès à vos données, d'un droit de rectification ou d'effacement, d'un droit de limitation de leur traitement, du droit de retirer votre consentement à tout moment, d'un droit de portabilité ainsi qu'un droit d'opposition à la collecte de vos données. Vous pouvez exercer vos droits auprès du responsable de traitement en adressant une demande via le site internet www.giropharm.fr ou directement dans la pharmacie adhérente paricipant au Programme. Par email à l'adresse : <EMAIL>. Par courrier à l'adresse suivante (en justifiant de son identité) : Giropharm - Service Ma Carte - 2 Place Gustave Eiffel Immeuble Dublin Silic, 94528 RUNGIS Cedex. Vous disposez également un droit d'introduire une réclamation auprès de la Comission Nationale Informatique et Libertés (CNIL).

                    </p>
                </div>
                <input type="hidden" id="client__token" name="client[_token]" data-controller="csrf-protection" value="csrf-token">
            </form>
        </div>
    </div>
</div>