<?php

namespace Giropharm\Tests\Fixtures\WireMock;

use WireMock\Client\WireMock;

class MockLoader
{
    public static function configureMocks(WireMock $wireMock, string $projectDir): void
    {
        $enseigneDir = $projectDir.'/enseigne/'.$_ENV['ENSEIGNE'];
        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientStaticTablesSelect.php'))
            ->withQueryParam('programme', WireMock::equalTo('1G1ROPH4RM'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/tablestatic-select-successful.xml'))));

        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientClientSelect.php'))
            ->with<PERSON><PERSON><PERSON><PERSON>aram('CodeCarte', WireMock::equalTo('1001001000012'))
            ->with<PERSON><PERSON><PERSON><PERSON>aram('DateNaissance', WireMock::equalTo('25/10/1949'))
            ->withQueryParam('programme', WireMock::equalTo('1G1ROPH4RM'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/client-select-successful.xml'))));

        $wireMock->stubFor(WireMock::post(WireMock::urlPathEqualTo('/PhDClientClientUpdate.php'))
//            ->withQueryParam('CodeCarte', WireMock::equalTo('1001001000012'))
//            ->withQueryParam('DateNaissance', WireMock::equalTo('25/10/1949'))
//            ->withQueryParam('programme', WireMock::equalTo('1G1ROPH4RM'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/client-update-failure.xml'))));
    }
}
