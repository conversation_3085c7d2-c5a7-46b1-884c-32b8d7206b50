/*
 * Welcome to your app's main JavaScript file!
 *
 * We recommend including the built version of this JavaScript file
 * (and its CSS file) in your base layout (base.html.twig).
 */

// any CSS you import will output into a single css file (app.css in this case)
// import './styles/foundation.min.css';
// import './styles/leaflet.css';
// import './styles/fontawesome-all.min.css';
// import './styles/styles.css';
// import './sass/styles.scss';
import './css/foundation.min.css';
import './css/fontawesome-all.min.css';
import './css/styles.css';
import './css/leaflet.css';
// require jQuery normally
const $ = require('jquery');
import('./js/main.js');
import('./js/foundation.min.js');
import('./js/leaflet.js');
import('./js/streetmap.js');

// create global $ and jQuery variables
global.$ = global.jQuery = $;
