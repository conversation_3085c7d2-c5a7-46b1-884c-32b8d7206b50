@import 'mixins';

/******************************************************************
*                                                                 *
*             	              GLOBAL                              *
*                                                                 *
******************************************************************/
html {
	width: 100%;
	height: 100%;
}
body {
	width: 100%;
	min-height: 100%;
	position: relative;
	font-family:"Ralew<PERSON>", "Lucida sans", verdana, Helvetica, sans-serif; 
	font-size: 14px;
	line-height: 24px;
	font-weight: 400;
	letter-spacing: normal;
	text-rendering: optimizeLegibility;
	font-weight: 500;

}
p{
	line-height: 1.3;
	margin-bottom: 20px;
	
}
h1, h2, h3, h4, h5, h6 {
	font-family: "<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON>, Lucida Sans Unicode, "Helvetica Neue", Helvetica,Arial,sans-serif;
}
h1, h2, h3, h4, h5, h6 {
	margin-top: 0;
	margin-bottom: 10px;
}
h2 {
	font-weight: 700;
	margin-bottom: 20px;
}
h3 {
	line-height: 35px;
	margin-bottom: 20px;
}
h4 {
	font-size: 17px;
	font-weight: 700;
}
a {
	transition: all .2s ease-in-out;
	transition: all 150ms ease-in;
	&:visited{
		transition: all 150ms ease-in;
	}
	&:hover,&:focus{
		text-decoration: none;
		outline: none;
	}
}
ul {
	margin: 0;
	padding: 0;
	list-style: none;
}
ul li {
	line-height: 28px;
}
::-moz-selection, ::selection {
	text-shadow: none;
}
img::selection, img::-moz-selection {
	background: 0 0;
}
hr {
	margin-top: 40px;
	margin-bottom: 60px;
}
form {
	margin-bottom: 0;
}
input, select, .radio, .form-control{border-radius: 0;}

.asterisque{
    font-size: 1.4rem;
    font-family: Raleway, "Lucida sans", verdana;
}
.checked{
    .form-group-head{
        display: flex;
        align-items: baseline;
    }
}
span.checked{
	height: 24px;
	width: 24px;
	background-image: url(../../images/sprite.png);
	background-position: -72px -70px;
}
span.checked{
	height:24px;
	width:24px;
	background-image: url(../../images/sprite.png);
	background-position: -48px 0px;
	position:absolute;
	top:0px;
	left:0px;
}
div.checked{
	position: relative;
	margin-top:10px;
}
@media only screen and (min-width: 40em) {
	div.checked{
		padding-left:1.5em;
	}
}
.fa{
	vertical-align: middle;
}

/************************************************
*                                               *
*             	     Fonts                      *
*                                               *
*************************************************/

@font-face {
    font-family: 'Raleway';
    src: url('../../fonts/raleway-regular-webfont.eot');
    src: url('../../fonts/raleway-regular-webfont.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/raleway-regular-webfont.woff2') format('woff2'),
		url('../../fonts/raleway-regular-webfont.woff') format('woff'),
		url('../../fonts/raleway-regular-webfont.ttf') format('truetype'),
		url('../../fonts/raleway-regular-webfont.svg#ralewayregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Raleway-Bold';
    src: url('../../fonts/Raleway-Bold.eot');
    src: url('../../fonts/Raleway-Bold.eot?#iefix') format('embedded-opentype'),
		url('../../fonts/Raleway-Bold.woff') format('woff'),
		url('../../fonts/Raleway-Bold.ttf') format('truetype'),
		url('../../fonts/Raleway-Bold.svg#ralewayregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

/************************************************
*                                               *
*             	    Button                      *
*                                               *
*************************************************/
.btn{
	font-family: 'Raleway-Bold';
	border: none;
	padding: 15px 25px;
	&:hover{
		transition: all 150ms ease-in;
	}
}
.bloc-icon{
	border-radius: 5px;
    width: 80px;
    height: 80px;
    font-size: 30px;
    text-align: center;
	padding: 0px;
	display: flex;
	justify-content: center;
	align-items: center;
	i{
		text-align: center;
	}
}


/************************************************
*                                               *
*             	     Flex                       *
*                                               *
*************************************************/

.is-flex {
	display: flex;
}
.is-flex-column {
	flex-direction: column;
}
.centered{
    display: flex;
    justify-content: center;
}
.is-flex-wrap {
	display: flex;
	flex-wrap: wrap;
}

/************************************************
*                                               *
*             	 Les titres                     *
*                                               *
*************************************************/

h2{
	text-align:center;
	margin:40px auto 15px;
	font-weight: initial;
	font-size:36px;
}
	
footer .titre{
	text-align:left;
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 8px;
	display: block;
}

.sous-titre {
	text-align: center;
	margin-top: 1rem;
}

/************************************************
*                                               *
*             	 Les bulles                     *
*                                               *
*************************************************/
.bubble {
	position: relative;
	padding:20px 10px;
	margin-top: 30px;
	border-radius: 3px;
	&:after { 
		content: '';
		position: absolute;
		border-style: solid;
		border-width: 0 15px 15px;
		display: block;
		width: 0;
		z-index: 1;
		margin-left: -15px;
		top: -15px;
		left: 50%;
	}
	&:before {
		content: '';
		position: absolute;
		border-style: solid;
		border-width: 0 15px 15px;
		display: block;
		width: 0;
		z-index: 0;
		margin-left: -15px;
		top: -16px;
		left: 50%;
	}
	&.bubble--map {
		padding: 0;
	}
}


.bubble-wrapper {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-content: space-between;
	align-items: stretch;
	.bubble-container{
		display: flex;
		flex-direction: column;
		align-items: center;
		align-content: space-between;
		margin-top: 2rem;
		&.is-third, &.is-two-third, &.is-quarter, &.is-half{
			width: 100%
		}
		.bubble{
			flex: 2;
			text-align: center;
			margin-bottom: 40px;
		}
		button{
			img{
				margin: auto;
			}
		}
	}
	.titreBubble{
		position: unset;
		left: unset;
		right: unset;
		top: unset;
		margin: unset;
		width: unset;
		font-family: "Raleway-Bold";
		font-size: 1.1rem;
	}
	.bubble{
		width: 100%;
	}
}

@media screen and (min-width: 40rem) {
	.bubble-wrapper {
		.bubble-container{
			&.is-quarter{
				width: 22%;
			}
			&.is-half{
				width: 48%;
			}
			&.is-third{
				width: 31%;
			}
			&.is-two-third{
				width: 62%;
			}
		}
		.bubble{
			flex: 2;
			text-align: center;
		}
		button{
			img{
				margin: auto;
			}
		}
	}
}




/************************************************
*                                               *
*             Réseaux Sociaux                   *
*                                               *
*************************************************/
.reseauBloc {
    display: flex;
    align-items: center;
    i{
        padding: 14px;
    }
    span{
        font-size: 0.9rem;
    }
    .connected {
        .logoReseau {
			padding: 1em 0em;
		}
    }
    &:hover{
        opacity: 0.7;
    }
}

/************************************************
*                                               *
*             	    Footer                      *
*                                               *
*************************************************/

.footer{
    .footer-top{ 
        padding: 2em 0 0em 1em;
        margin-bottom: 1em;
        &>p{
            text-align: right;
        }
    }
    .footer-bottom-link{
        text-decoration: underline;
    }
}

// a revoir

.condition{
	font-size:0.9em;
	text-align: center;
	width: 75%;
	margin: 10px auto;
}





/************************************************
*                                               *
*             	 A REFAIRE                      *
*                                               *
*************************************************/

.highlight {
	padding: 20px;
	background: #F8F8F8;
	border-radius:3px;
}

.vertical {
	padding: 30px;
	h1 {
		margin:-30px;
		margin-bottom: 25px;
		padding: 30px 20px;
		font-size: 24px;
		font-weight: normal;
		text-align: center;
		text-transform: capitalize;
		padding: 20px;
		text-align: center;
		text-transform: none;
	}
	button { width: auto;
		padding: 15px 20px;
	}
}

#client_dateNaissance select {
    width: auto;
}
.fieldsetWith{
	padding:2em 1em;
	border-radius: 4px;
}
.fieldsetWith--padding{
	padding: 3em 0 1em 0;
}
@media only screen and (min-width: 40em) {
	.fieldsetWith{
		padding:2em 3em;
	}
	.fieldsetWith--padding{
		padding: 3em 3em 1em 3em;
	}
}
.margin-top-40{
	margin-top: 40px;
}
.logo{
	margin-top: 30px;margin-bottom: 30px;
}
.formatDate{
	display:none;
}
.formatDate {
    margin-top: -16px;
    margin-bottom: 15px;
}

.ralewayBold{
	font-family: Raleway-Bold;
}
.ralewayBold label {
	display: inline;
}

.col-center{
	display:block;
	margin-left: auto;
	margin-right: auto;
}
.centerElement {
	margin: 0 auto;
}
.strong, .bold{
	font-weight:bold;
}
.noMargin{
	margin:0px;
}

.textAlignCenter {
	text-align: center;
}
p.center{
	text-align:center;
}
.bloc p{
	line-height:1.2;
}
.code_barre{
	font-family:EAN13;
}
#ptsatteind{
	font-weight:bold;
	font-size: 20px;
}

.sr-only{
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}
.mlxl{
	margin-left: 3em;
}
.small-mbl{
	margin-bottom: 1.5em;
}
.container-fluid{
	max-width: 1200px;
	margin:0 auto;
}
#map {
  height: 300px;
  width: 100%;
  border-radius: 5px;
 }

.pl0 {
	padding-left: 0px;
}

.historique-points {
	padding-right: 3em;
}