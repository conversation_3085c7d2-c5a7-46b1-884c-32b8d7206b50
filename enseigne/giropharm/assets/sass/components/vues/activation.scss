/************************************************
*                                               *
*             	 VUE ACTIVATION                *
*                                               *
*************************************************/

.banniere-activation{
    background: url(../../../images/activation-banner.png) no-repeat;
    background-size: auto 100%;
    background-position: bottom;
    height: 120px;
    width: 100%;
}
.activation{
    .checked i{
        font-size: 1.05rem;
    }
    h1{
        padding-bottom: 14px;
        width: fit-content;
    }
    button {
        padding: 18px 20px;
    }
}
.cnilMessageInfoRecueillies{
	text-align: justify;
}

// a revoir
.activation-menuWrap {
	position: absolute;
	width: 100%;	
	top: 0;
	text-align: center;
}
.activation-puce {	
	width: 3.5em;	
	height: 3.5em;	
	border-radius: 50%;		
	text-align: center;	
	line-height: 3.5em;
	font-size: 1.2em;
	font-weight: bold;
}

.activation-puces {
	margin: 50px auto;
	display: flex;
	justify-content: center;
}

.activation-puce-separator {
	width: 15px;
	height: 2.2em;
}

.activation-line {
	width: 15em;
	margin: auto;
}

.activation-etape {
	text-align: center;
	padding-top: 20px;
	padding-bottom: 50px;
}

.activation input {
	outline: none;
	width: 100%;
	padding: 14px 20px;
	display: block;
	transition: all 0.2s ease-in-out;
	-moz-transition: all 0.2s ease-in-out;
	-webkit-transition: all 0.2s ease-in-out;
	-o-transition: all 0.2s ease-in-out;
	border-radius: 0;
	height: auto;
}

.activationForm1 label {
	text-align: right;
	line-height: 1.3em;
}

.activation-etape1-text {
	line-height: 1.2em;
	padding: 0 0 45px 0;
}

.activation-compte input {
	padding: 0.5rem;
}

.activation-compte input[type="checkbox"] {
	display: inline;
	width: inherit;
}

.activation-confirm-checkboxes {
	margin: 25px 0 0 15px;
}

.activation-button-confirmation {
	padding: 13px 25px;
}

.activation-compte-confirmation-label {
	text-indent: -27px;
	padding-left: 27px;
	line-height: 0.4em;
	cursor: pointer;	
}

.activation-compte-cgv-label {
	text-indent: -27px;
	padding-left: 27px;
	line-height: 0.4em;
	cursor: pointer;
}

.activation-compte button {
	width: initial;
	margin: 40px 0px;
}

.activation-etape3-text1 {
	padding: 0 0 15px 0;
	font-weight: bold;
	font-size: 1.2em;
}

.activation-etape3-text2 {
	padding: 0 0 20px 0;
}

@media only screen and (max-width: 40em) {
	.banniere-activation {
		height: 66px;
		margin: 0 0 50px 0;
	}

	.activation-menu {
		background: none;
	}

	#carteAdFidHome {
		width: 12em;
	}

	.activationForm1 label {
		text-align: left;
	}

	.activation-etape {
		padding-bottom: 20px;
	}

	.activation-compte .form-group--margin {
		margin-left: 0;
	}
}

@media only screen and (max-width: 64em) {
	.activation-menu {
		background: none;
	}
}

.activationForm1 {
	margin: auto;
}
.activation-ma-carte-exclusive {
	font-size: 2.5em;
}
.activation-carte-bandeau {
	height: 130px;
	background-image: url(../../../images/fond-banniere.jpg);
}

.activation-menu {
	background: url('../../../images/white.jpg') repeat-x;
	position: absolute;
	top: 0px;
	left: 0;
	padding-bottom: 0;
	margin-bottom: -9999px;
	overflow: hidden;
}

.activation h1 {
	font-size: 2.5em;
}

