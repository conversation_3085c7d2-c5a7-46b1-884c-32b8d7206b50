/******************************************************************
*                                                                 *
*             	              COLORS                              *
*                                                                 *
******************************************************************/

/****************************
*      	   Variables        *
*****************************/

/**** Variables globales *****/

$main-color: #004b95;
$secondary-color: #5186ba;

$main_text_color: #000;
$secondary_text_color: #7a7a7a;

/**** Variables Précises *****/

$body_bg_color: #FFF;

// menu
$menu_bg_color: $main-color;
$menu_secondary_color: $secondary-color;
$menu_color: #FFF;
$menu_hr_color: $secondary-color;

// sections
$section_bg_color1:#EFEFEF;
$section_bg_color2:#dfdfdf;

// form 
$form_color: $secondary_text_color;
$asterisque_color:red;
$form_icon_color: #01abe1;
$input_color:#000;
$input_border_color: #CFCFCF;

// btn
$btn_color: #FFF;
$btn_bg_color:$main-color;
$btn_bg_hover:$secondary-color;

// font
$titre_color:#000;
$soustitre_color: $secondary_text_color;
$titre_hr_color:#E5E5E5;
$p_color:#000;

// bubble
$bubble_bg_color:#E5E5E5;
$bubble_bg_color2: #FFF;
$bubble_border_color:#E5E5E5;

// donut
$donut_jauge_color: $main-color;
$donut_bg_color: #ccc;
$donut_border_color: #FFF;


/****************************
*      	    Helpers         *
*****************************/
// classes de derniers recours
// changeant la couleur d'un
// élément qui ne respecte pas
// la norme (ex: .isBlue,.isBgRed, ...)

.isGrey{
  color: $secondary_text_color;
}


/****************************
*      	    Global          *
*****************************/

body{
  background-color: $body_bg_color;
	color: $main_text_color;
}
p{
  color: $p_color;
}
h1,h2{
  color: $titre_color;
}
h4{
	color: #2d2d2d;
}
a {
	&:hover,&:focus{
		color: #007DA0;
	}
}
::-moz-selection, ::selection {
	background: #1F1F1F;
	color: #FFF;
}
hr {
	border-top: 1px dotted $titre_hr_color;
}


.sous-titre{
  color: $soustitre_color;
}
.asterisque{
  color: $asterisque_color;
}
.form-icon{
  color: $form_icon_color;
}
.checked{
  label,legend,p,span{
      color: $form_color;
  }
  .asterisque{
    color: $asterisque_color;
  }
}
.btn{
	background-color: $btn_bg_color;
	color: $btn_color;
	&:hover{
    background-color: $btn_bg_hover;
  }
  &:hover, &:focus, &:active{
    color: $btn_color;
  }
}

.bloc-icon{
	background-color: $main-color;
  color: #fff;
}
.bubble {
	border: 1px solid $bubble_border_color;
	background-color: $bubble_bg_color;
	&:after { 
		border-color: $bubble_border_color transparent;
	}
	&:before {
		border-color: $bubble_border_color transparent;
	}
}
.bubble--white{
  background-color: $bubble_bg_color2;
}


.reseauBloc {
  color:white;
  &:hover{
    color: white;
  }
  i{
      border-right: 1px solid #ffffff4d;
  }
  &.connected {
      border: 1px #aaaaaa solid;
      i{
          border-right: 1px solid #aaaaaa;
      }
  }
  &.facebook {
      background: #405d9a;
      &.connected {
          background: #fff;
          color: #405d9a;
      }
  }
  &.twitter {
      background: #1aaadf;
      &.connected {
          background: #fff;
          color: #1aaadf;
          .logoReseau {
            padding: 1em 0em;
          }
      }
  }
  &.google {
      background: #d51919;
      &.connected {
          background: #fff;
          color: #d51919;
      }
  }
}



.condition{
	color:#000;
  a{
    color: #000;
    border-bottom: solid 1px #524F4F;
  }
}

#ptsatteind{
  color: #87004D;
}

.fieldsetWith{
  background: #fff;
}

.blocBackground1{
	background-color: $section_bg_color1;
	border-radius: 3px;
}
.blocBackground2{
	background-color: $section_bg_color2;
	border-radius: 3px;
}
.black{
	color:black;
}

.color{
  color: #01abe1;
}

input[type="text"]{
  color:$input_color;
  border-color:$input_border_color;
  &::-webkit-input-placeholder,
  &:-moz-placeholder,
  &::-moz-placeholder,
  &:-ms-input-placeholder{
    color: $input_color;
  }
}

.mentions-legales{
  color: $secondary_text_color;
}

/****************************
*      	    Login           *
*****************************/

.banner-optin button{
  background-color: $main-color;
}
.vertical{
  background-color: #f3f3f3;
  h1{
    background: $main-color;
    color: #f5f5f5;
  }
  input[type="text"]{
    border-color:#b9b9b9;
    background: rgba(255,255,255,0.4);
    color: #000;
  }
  button:hover {
			background: #0396CF;
  }
  &.formLogin>.row>div:first-child{
    border-right: 1px solid #CFCFCF;
  }
}

#divBienvenue {
  hr{
    border-top: solid 1px #ddd;
  }
}
#bienvenue{
  border-bottom-color:rgba(0,0,0,0.1);
}




/****************************
*        	  Home            *
*****************************/
.pageClient .open{
  background-color:$main-color;
  color:#fff;
	border:solid 1px #fff;
}
#mesInformations .ralewayBold label,
.offreUtilisee, #ptsatteind {
  color: $main-color;
}
.code_barre {
  background-color: #fff;
  color: #000;
}

/* Menu */
.menu{
  background:$main-color;
  ul,li,a,p{
    color:$menu_color;
  }
  .highlight {
    background-color: $menu_secondary_color;
    color: $menu_color;
  }
  li {
    border-bottom: solid 1px $menu_hr_color;
  }
  .close{
    color:$main-color;
    background: #fff;
  }
};

/* Historique */

.historique-list li:nth-child(odd) {
	background: #efefef;
}
.historique-points{
  color: $main-color;
}

/* Donuts */
#donuts path{
  &:first-child{
    fill: $donut_jauge_color;
  }
  &:nth-child(3){
    fill: $donut_bg_color;
  }
  &:nth-child(2), &:nth-child(4){
    fill: $donut_border_color;
  }
}

#map {
  border: 1px solid $bubble_border_color;
  background-color: $bubble_bg_color;
}

/****************************
*        	Activation        *
*****************************/
.activation-etape{
  color: $secondary_text_color;
}
.activation{
  h1{
      color: #000;
      border-bottom: 1px solid #c5c3c3;
  }
  input {
    color: #333;
    background: #FFF;
    border:1px solid #cacaca;
  }
  .activation-puce-active {
    background-color: $main-color;
    color: white;
  }
}
.activation-puce {
  background-color: #e4e4e4;	
  color: black;
}
.activation-puce-separator {
  border-bottom: 2px solid #e4e4e4;
}
.activationForm1 label {
	color: black;
}
.activation-compte {
  border: 1px solid #efefef;
  p{
    color: $secondary_text_color;
  }
}

/****************************
*        	  Footer         *
*****************************/

.footer{
  background: #fff;
}
.footer p{
  color: $main-color;
}
.footer-top{
  border-bottom: 1px solid $main-color;
}
.footer-icon{
  background: $main-color;
  color: #fff;
}
a.footer-bottom-link, a.footer-bottom-link:focus, a.footer-bottom-link:hover{
  color: $main-color;
}