@charset "UTF-8";
/******************************************************************
*                                                                 *
*             	              GLOBAL                              *
*                                                                 *
******************************************************************/
html {
  width: 100%;
  height: 100%; }

body {
  width: 100%;
  min-height: 100%;
  position: relative;
  font-family: "<PERSON><PERSON><PERSON>", "Lucida sans", verdana, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  letter-spacing: normal;
  text-rendering: optimizeLegibility;
  font-weight: 500; }

p {
  line-height: 1.3;
  margin-bottom: 20px; }

h1, h2, h3, h4, h5, h6 {
  font-family: "<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON>, <PERSON><PERSON> Sans Unicode, "Helvetica Neue", Helvetica,Arial,sans-serif; }

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 10px; }

h2 {
  font-weight: 700;
  margin-bottom: 20px; }

h3 {
  line-height: 35px;
  margin-bottom: 20px; }

h4 {
  font-size: 17px;
  font-weight: 700; }

a {
  transition: all .2s ease-in-out;
  transition: all 150ms ease-in; }
  a:visited {
    transition: all 150ms ease-in; }
  a:hover, a:focus {
    text-decoration: none;
    outline: none; }

ul {
  margin: 0;
  padding: 0;
  list-style: none; }

ul li {
  line-height: 28px; }

::-moz-selection, ::selection {
  text-shadow: none; }

img::selection, img::-moz-selection {
  background: 0 0; }

hr {
  margin-top: 40px;
  margin-bottom: 60px; }

form {
  margin-bottom: 0; }

input, select, .radio, .form-control {
  border-radius: 0; }

.asterisque {
  font-size: 1.4rem;
  font-family: Raleway, "Lucida sans", verdana; }

.checked .form-group-head {
  display: flex;
  align-items: baseline; }

span.checked {
  height: 24px;
  width: 24px;
  background-image: url(./../images/sprite.png);
  background-position: -72px -70px; }

span.checked {
  height: 24px;
  width: 24px;
  background-image: url(../images/sprite.png);
  background-position: -48px 0px;
  position: absolute;
  top: 0px;
  left: 0px; }

div.checked {
  position: relative;
  margin-top: 10px; }

@media only screen and (min-width: 40em) {
  div.checked {
    padding-left: 1.5em; } }
.fa {
  vertical-align: middle; }

/************************************************
*                                               *
*             	     Fonts                      *
*                                               *
*************************************************/
@font-face {
  font-family: 'Raleway';
  src: url("../fonts/raleway-regular-webfont.eot");
  src: url("../fonts/raleway-regular-webfont.eot?#iefix") format("embedded-opentype"), url("../fonts/raleway-regular-webfont.woff2") format("woff2"), url("../fonts/raleway-regular-webfont.woff") format("woff"), url("../fonts/raleway-regular-webfont.ttf") format("truetype"), url("../fonts/raleway-regular-webfont.svg#ralewayregular") format("svg");
  font-weight: normal;
  font-style: normal; }
@font-face {
  font-family: 'Raleway-Bold';
  src: url("../fonts/Raleway-Bold.eot");
  src: url("../fonts/Raleway-Bold.eot?#iefix") format("embedded-opentype"), url("../fonts/Raleway-Bold.woff") format("woff"), url("../fonts/Raleway-Bold.ttf") format("truetype"), url("../fonts/Raleway-Bold.svg#ralewayregular") format("svg");
  font-weight: normal;
  font-style: normal; }
/************************************************
*                                               *
*             	    Button                      *
*                                               *
*************************************************/
.btn {
  font-family: 'Raleway-Bold';
  border: none;
  padding: 15px 25px; }
  .btn:hover {
    transition: all 150ms ease-in; }

.bloc-icon {
  border-radius: 5px;
  width: 80px;
  height: 80px;
  font-size: 30px;
  text-align: center;
  padding: 0px;
  display: flex;
  justify-content: center;
  align-items: center; }
  .bloc-icon i {
    text-align: center; }

/************************************************
*                                               *
*             	     Flex                       *
*                                               *
*************************************************/
.is-flex {
  display: flex; }

.is-flex-column {
  flex-direction: column; }

.centered {
  display: flex;
  justify-content: center; }

.is-flex-wrap {
  display: flex;
  flex-wrap: wrap; }

/************************************************
*                                               *
*             	 Les titres                     *
*                                               *
*************************************************/
h2 {
  text-align: center;
  margin: 40px auto 15px;
  font-weight: initial;
  font-size: 36px; }

footer .titre {
  text-align: left;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  display: block; }

.sous-titre {
  text-align: center;
  padding-top: 1rem;
  max-width: 28em;
  margin: auto;
  margin-bottom: 2rem; }

/************************************************
*                                               *
*             	 Les bulles                     *
*                                               *
*************************************************/
.bubble {
  position: relative;
  padding: 20px 10px;
  margin-top: 30px;
  border-radius: 3px; }
  .bubble:after {
    content: '';
    position: absolute;
    border-style: solid;
    border-width: 0 15px 15px;
    display: block;
    width: 0;
    z-index: 1;
    margin-left: -15px;
    top: -15px;
    left: 50%; }
  .bubble:before {
    content: '';
    position: absolute;
    border-style: solid;
    border-width: 0 15px 15px;
    display: block;
    width: 0;
    z-index: 0;
    margin-left: -15px;
    top: -16px;
    left: 50%; }

.bubble-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  align-items: stretch; }
  .bubble-wrapper .bubble-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: space-between;
    margin-top: 2rem; }
    .bubble-wrapper .bubble-container.is-third, .bubble-wrapper .bubble-container.is-two-third, .bubble-wrapper .bubble-container.is-quarter, .bubble-wrapper .bubble-container.is-half {
      width: 100%; }
    .bubble-wrapper .bubble-container .bubble {
      flex: 2;
      text-align: center;
      margin-bottom: 50px; }
    .bubble-wrapper .bubble-container button img {
      margin: auto; }
  .bubble-wrapper .titreBubble {
    position: unset;
    left: unset;
    right: unset;
    top: unset;
    margin: unset;
    width: unset;
    font-family: "Raleway-Bold";
    font-size: 1.1rem; }
  .bubble-wrapper .bubble {
    width: 100%; }
  .bubble-wrapper .magasinTitle {
    font-family: "Raleway-Bold";
    font-size: 1.1rem; }

@media screen and (min-width: 40rem) {
  .bubble-wrapper .bubble-container.is-quarter {
    width: 22%; }
  .bubble-wrapper .bubble-container.is-half {
    width: 48%; }
  .bubble-wrapper .bubble-container.is-third {
    width: 31%; }
  .bubble-wrapper .bubble-container.is-two-third {
    width: 62%; }
  .bubble-wrapper .bubble-container.monMagasin {
    margin-top: 0rem; }
  .bubble-wrapper .bubble {
    flex: 2;
    text-align: center; }
  .bubble-wrapper button img {
    margin: auto; } }
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto; }

/************************************************
*                                               *
*             Réseaux Sociaux                   *
*                                               *
*************************************************/
.reseauBloc {
  display: flex;
  align-items: center; }
  .reseauBloc i {
    padding: 14px; }
  .reseauBloc span {
    font-size: 0.9rem; }
  .reseauBloc .connected .logoReseau {
    padding: 1em 0em; }
  .reseauBloc:hover {
    opacity: 0.7; }

/************************************************
*                                               *
*             	    Footer                      *
*                                               *
*************************************************/
.footer .footer-top {
  padding: 2em 0 0em 1em;
  margin-bottom: 1em; }
  .footer .footer-top > p {
    text-align: right; }
.footer .footer-bottom-link {
  text-decoration: underline; }

.condition {
  font-size: 0.9em;
  text-align: center;
  width: 75%;
  margin: 10px auto; }

/************************************************
*                                               *
*             	 A REFAIRE                      *
*                                               *
*************************************************/
.highlight {
  padding: 20px;
  background: #F8F8F8;
  border-radius: 3px; }

.vertical {
  padding: 30px; }
  .vertical h1 {
    margin: -30px;
    margin-bottom: 25px;
    padding: 30px 20px;
    font-size: 24px;
    font-weight: normal;
    text-align: center;
    text-transform: capitalize;
    padding: 20px;
    text-align: center;
    text-transform: none; }
  .vertical button {
    width: auto;
    padding: 15px 20px; }

#client_dateNaissance select {
  width: auto; }

input.width-auto, select.width-auto {
  width: auto; }

@media screen and (min-width: 23rem) {
  #client_situationFamiliale {
    width: 92%; } }
.fieldsetWith {
  padding: 2em 0em;
  border-radius: 4px; }

@media only screen and (min-width: 40em) {
  .fieldsetWith {
    padding: 2em 0em; } }
.margin-top-40 {
  margin-top: 40px; }

.logo {
  margin-top: 30px;
  margin-bottom: 30px; }

.formatDate {
  display: none; }

.formatDate {
  margin-top: -16px;
  margin-bottom: 15px; }

.ralewayBold {
  font-family: Raleway-Bold; }

.ralewayBold label {
  display: inline; }

.col-center {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.centerElement {
  margin: 0 auto; }

.strong, .bold {
  font-weight: bold; }

.noMargin {
  margin: 0px; }

.textAlignCenter {
  text-align: center; }

p.center {
  text-align: center; }

.bloc p {
  line-height: 1.2; }

.code_barre {
  font-family: EAN13; }

#ptsatteind {
  font-weight: bold;
  font-size: 20px; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.mlxl {
  margin-left: 3em; }

.small-mbl {
  margin-bottom: 1.5em; }

.container-fluid {
  max-width: 1200px;
  margin: 0 auto; }

#map {
  height: 300px;
  width: 100%;
  border-radius: 5px;
  margin-bottom: 50px; }

.pl0 {
  padding-left: 0px; }

.historique-points {
  padding-right: 3em; }

@media screen and (max-width: 63.9375em) {
  #tarteaucitronRoot #tarteaucitronAlertBig button {
    display: block;
    margin: 10px auto; } }
/******************************************************************
*                                                                 *
*             	              COLORS                              *
*                                                                 *
******************************************************************/
/****************************
*      	   Variables        *
*****************************/
/**** Variables globales *****/
/**** Variables Précises *****/
/****************************
*      	    Helpers         *
*****************************/
.isGrey {
  color: #7a7a7a; }

.mainColor {
  color: #f7585d; }

/****************************
*      	    Global          *
*****************************/
body {
  background-color: #FFF;
  color: #000; }

p {
  color: #000; }

#monProgramme p {
  color: #7a7a7a; }

h1, h2 {
  color: #000; }

h4 {
  color: #2d2d2d; }

a:hover, a:focus {
  color: #007DA0; }

::-moz-selection, ::selection {
  background: #1F1F1F;
  color: #FFF; }

hr {
  border-top: 1px dotted #E5E5E5; }

.sous-titre {
  color: #7a7a7a; }

.asterisque {
  color: red; }

.form-icon {
  color: #01abe1; }

.checked label, .checked legend, .checked p, .checked span {
  color: #7a7a7a; }
.checked .asterisque {
  color: red; }

.btn {
  background-color: #4b3041;
  color: #FFF; }
  .btn:hover {
    background-color: #f98f93; }
  .btn:hover, .btn:focus, .btn:active {
    color: #FFF; }

.bloc-icon {
  background-color: #f7585d;
  color: #fff; }

.bubble {
  border: 1px solid #e5e2db;
  background-color: #ffd6c0; }
  .bubble:after {
    border-color: #ffd6c0 transparent; }
  .bubble:before {
    border-color: #e5e2db transparent; }
  .bubble .magasinTitle {
    color: #4b3041; }
  .bubble .site {
    color: #f7585d; }

.bubble--white {
  background-color: #FFF; }
  .bubble--white:after {
    border-color: #FFF transparent; }
  .bubble--white:before {
    border-color: #e5e2db transparent; }

.reseauBloc {
  color: white; }
  .reseauBloc:hover {
    color: white; }
  .reseauBloc i {
    border-right: 1px solid #ffffff4d; }
  .reseauBloc.connected {
    border: 1px #aaaaaa solid; }
    .reseauBloc.connected i {
      border-right: 1px solid #aaaaaa; }
  .reseauBloc.facebook {
    background: #405d9a; }
    .reseauBloc.facebook.connected {
      background: #fff;
      color: #405d9a; }
  .reseauBloc.twitter {
    background: #1aaadf; }
    .reseauBloc.twitter.connected {
      background: #fff;
      color: #1aaadf; }
      .reseauBloc.twitter.connected .logoReseau {
        padding: 1em 0em; }
  .reseauBloc.google {
    background: #d51919; }
    .reseauBloc.google.connected {
      background: #fff;
      color: #d51919; }

.condition {
  color: #000; }
  .condition a {
    color: #000;
    border-bottom: solid 1px #524F4F; }

#ptsatteind {
  color: #87004D; }

.fieldsetWith {
  background: #fff; }

.blocBackground1 {
  background-color: #EFEFEF;
  border-radius: 3px; }

.blocBackground2 {
  background-color: #ffd6c0;
  border-radius: 3px; }

.black {
  color: black; }

.color {
  color: #01abe1; }

input[type="text"] {
  color: #8c8b89;
  border-color: #CFCFCF; }
  input[type="text"]::placeholder {
    color: #8c8b89;
    opacity: 1;
    /* Firefox */ }

.avertissement-form, .mentions-legales {
  color: #7a7a7a; }

/****************************
*      	    Login           *
*****************************/
.vertical {
  background-color: #f7585d;
  color: white; }
  .vertical input[type="text"] {
    border-color: #b9b9b9;
    background: white;
    color: #000; }
    .vertical input[type="text"]::placeholder {
      color: #000;
      opacity: 1;
      /* Firefox */ }
  .vertical p, .vertical h1, .vertical label {
    color: white; }

#divBienvenue hr {
  border-top: solid 1px #ddd; }

#bienvenue {
  border-bottom-color: rgba(0, 0, 0, 0.1); }

/****************************
*        	  Home            *
*****************************/
.pageClient .open {
  background-color: #f7585d;
  color: #fff;
  border: solid 1px #fff; }

#mesInformations .ralewayBold label,
.offreUtilisee, #ptsatteind {
  color: #f7585d; }

.code_barre {
  background-color: #fff;
  color: #000; }

.fa-arrow-circle-right:before {
  color: #f7585d; }

/* Menu */
.menu {
  background: #f7585d; }
  .menu ul, .menu li, .menu a, .menu p {
    color: #FFF; }
  .menu .highlight {
    background-color: #f98f93;
    color: #FFF; }
  .menu li {
    border-bottom: solid 1px #f98f93; }
  .menu li:last-child {
    border: none; }
  .menu .close {
    color: #f7585d;
    background: #fff; }

/* Historique */
.historique-list li:nth-child(odd) {
  background: #efefef; }

.historique-points-color {
  color: #f7585d; }

/* Donuts */
#donuts path:first-child {
  fill: #f7585d; }
#donuts path:nth-child(3) {
  fill: #ccc; }
#donuts path:nth-child(2), #donuts path:nth-child(4) {
  fill: #FFF; }

#map {
  border: 1px solid #e5e2db;
  background-color: #ffd6c0; }

/****************************
*        	Activation        *
*****************************/
.activation-etape {
  color: #7a7a7a; }

.activation h1 {
  color: #000;
  border-bottom: 1px solid #c5c3c3; }
.activation input {
  color: #333;
  background: #FFF;
  border: 1px solid #cacaca; }
.activation .activation-puce-active {
  background-color: #f7585d;
  color: white; }

.activation-puce {
  background-color: #e4e4e4;
  color: black; }

.activation-puce-separator {
  border-bottom: 2px solid #e4e4e4; }

.activationForm1 label {
  color: black; }

.activation-compte {
  border: 1px solid #efefef; }
  .activation-compte p {
    color: #7a7a7a; }

/****************************
*        	  Footer         *
*****************************/
.footer {
  background: #fff; }

.footer p, .footer a {
  color: #f7585d; }

.footer-top {
  border-bottom: 1px solid #f7585d; }

.footer-icon {
  background: #f7585d;
  color: #fff; }

a.footer-bottom-link, a.footer-bottom-link:focus, a.footer-bottom-link:hover {
  color: #f7585d; }

#map {
  border: 1px solid #e5e2db;
  background-color: #ffd6c0; }

.has-error {
  color: #f34141; }

#prototypeEnfants .fa-times::before, #prototypeEnfants .fa-plus::before {
  color: white; }

/******************************************************************
*                                                                 *
*             	              TOOLS                               *
*                                                                 *
******************************************************************/
/* spacing helpers
p,m = padding,margin
a,t,r,b,l,h,v = all,top,right,bottom,left,horizontal,vertical
s,m,l,n = small(0.5em/8px),medium(1em/16px),large(1.5em/24px),none(0)
*/
.ptn, .pvn, .pan {
  padding-top: 0 !important; }

.pts, .pvs, .pas {
  padding-top: 0.5em; }

.ptm, .pvm, .pam {
  padding-top: 1em; }

.ptl, .pvl, .pal {
  padding-top: 1.5em; }

.prn, .phn, .pan {
  padding-right: 0 !important; }

.prs, .phs, .pas {
  padding-right: 0.5em; }

.prm, .phm, .pam {
  padding-right: 1em; }

.prl, .phl, .pal {
  padding-right: 1.5em; }

.pbn, .pvn, .pan {
  padding-bottom: 0 !important; }

.pbs, .pvs, .pas {
  padding-bottom: 0.5em; }

.pbm, .pvm, .pam {
  padding-bottom: 1em; }

.pbl, .pvl, .pal {
  padding-bottom: 1.5em; }

.pln, .phn, .pan {
  padding-left: 0 !important; }

.pls, .phs, .pas {
  padding-left: 0.5em; }

.plm, .phm, .pam {
  padding-left: 1em; }

.pll, .phl, .pal {
  padding-left: 1.5em; }

.mnt, .mvn, .man {
  margin-top: 0 !important; }

.mts, .mvs, .mas {
  margin-top: 0.5em; }

.mtm, .mvm, .mam {
  margin-top: 1em; }

.mtl, .mvl, .mal {
  margin-top: 1.5em; }

.mrn, .mhn, .man {
  margin-right: 0 !important; }

.mrs, .mhs, .mas {
  margin-right: 0.5em; }

.mrm, .mhm, .mam {
  margin-right: 1em; }

.mrl, .mhl, .mal {
  margin-right: 1.5em; }

.mbn, .mvn, .man {
  margin-bottom: 0 !important; }

.mbs, .mvs, .mas {
  margin-bottom: 0.5em; }

.mbm, .mvm, .mam {
  margin-bottom: 1em; }

.mbl, .mvl, .mal {
  margin-bottom: 1.5em; }

.mln, .mhn, .man {
  margin-left: 0 !important; }

.mls, .mhs, .mas {
  margin-left: 0.5em; }

.mlm, .mhm, .mam {
  margin-left: 1em; }

.mll, .mhl, .mal {
  margin-left: 1.5em; }

.mra, .mha {
  margin-right: auto; }

.mla, .mha {
  margin-left: auto; }

/************************************************
*                                               *
*                    VUE HOME                   *
*                                               *
*************************************************/
.pageClient .open {
  position: fixed;
  top: 10px;
  left: 0.325em;
  font-size: 2em;
  text-shadow: none;
  opacity: 1;
  font-weight: normal;
  padding: 0.375em 0.5em;
  border-radius: 5px;
  text-align: center;
  z-index: 99; }

/****************************
*                           *
*            Menu           *
*                           *
*****************************/
.menu .btn-deconnexion {
  width: 200px;
  margin-top: 15px; }

.menu p:first-child {
  line-height: 26px; }

.menu p {
  line-height: 18px;
  text-align: center; }

.menu ul li {
  padding: 5px;
  font-weight: bold; }

.menu .close {
  position: absolute;
  top: 0;
  right: 0.325em;
  font-size: 2em;
  margin: 10px;
  text-shadow: none;
  opacity: 1;
  font-weight: normal;
  border-radius: 5px;
  padding: 0.375em 0.5em; }

@media (min-width: 767px) and (max-width: 881px) {
  #menu .btn-deconnexion {
    margin-left: -18%; } }
.listeMenu {
  margin-top: 5em; }

.listeMenu .highlight {
  margin-top: 10px;
  width: 100%;
  padding: 10px; }

.listeMenu .highlight a {
  margin-top: 0px; }

@media only screen and (min-width: 40em) {
  .listeMenu {
    margin-top: 2rem; } }
#menuWrap {
  position: fixed;
  width: 100%;
  top: 0;
  left: -100%;
  text-align: center;
  z-index: 1000;
  -webkit-transition: left 0.5s linear;
  -moz-transition: left 0.5s linear;
  -ms-transition: left 0.5s linear;
  -o-transition: left 0.5s linear;
  transition: left 0.5s linear; }

#menuWrap:target {
  left: 0px; }

.menuWrapper {
  position: relative; }

.menu {
  position: absolute;
  top: 0px;
  left: 0;
  padding-bottom: 9999px;
  margin-bottom: -9999px;
  overflow: hidden; }

@media (min-width: 64em) {
  #menuWrap {
    left: 0;
    z-index: 0;
    -webkit-transition: none;
    -moz-transition: none;
    -ms-transition: none;
    -o-transition: none;
    transition: none; } }
/****************************
*                           *
*        Mon magasin        *
*                           *
*****************************/
/****************************
*                           *
*         Ma carte          *
*                           *
*****************************/
.maCarte .bubble, .passbook .bubble {
  display: flex;
  flex-direction: column; }
.maCarte .firstBlock, .passbook .firstBlock {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 5; }
.maCarte .secondBlock, .passbook .secondBlock {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex: 2; }

#maCarte .carte .bloc, #maCarte .points .bloc {
  height: 280px; }

#maCarte .carte .codeCarte {
  margin: 10px 0; }

.code-barre {
  margin: 0 auto;
  padding: 10px 3px 0px 3px;
  max-height: 80px;
  padding: 0 1em; }

#maCarte #donuts {
  margin: 10px 0; }

#maCarte .carte .bloc, #maCarte .points .bloc {
  height: 290px; }

#maCarte .bloc img {
  margin-top: 10px; }

#maCarte #donuts {
  margin: 0 0 10px 0; }

/****************************
*                           *
*        Mon compte         *
*                           *
*****************************/
#blocPoint {
  margin: 5px 0px; }

#adresse label {
  font-weight: normal; }

.mentions-legales {
  text-align: justify;
  margin: 35px 0px 20px 0px;
  font-style: italic; }

#monCompte {
  padding-bottom: 40px; }

/****************************
*                           *
*        Historique         *
*                           *
*****************************/
.historique {
  margin-top: 1em;
  margin-bottom: 1em; }

.historique-list {
  padding: 0px 0.5rem; }
  .historique-list li {
    padding: 0.25rem 0; }
    .historique-list li div:nth-child(1) {
      flex: 50;
      text-align: right; }
    .historique-list li div:nth-child(2) {
      text-align: left;
      flex: 20;
      padding-left: 0.125rem;
      justify-content: bottom; }
    .historique-list li div:nth-child(3) {
      flex: 30;
      text-align: left; }

.separator {
  border-bottom: solid #f7585d 2px;
  width: 11rem;
  margin: auto; }

#monCompte {
  padding-bottom: 50px; }
  #monCompte .submit {
    text-align: right; }
  #monCompte .cnilMessageInfoRecueillies {
    margin: 2rem 0em;
    font-size: 0.8rem; }
  #monCompte .form-group--margin {
    margin-left: 1.4rem; }

[id^='client_enfants_'] {
  display: flex;
  flex-wrap: wrap; }
  [id^='client_enfants_'] div {
    margin-right: 0.5rem; }
    [id^='client_enfants_'] div .btn_add {
      padding: unset;
      background-color: #f7585d; }
    [id^='client_enfants_'] div .delete span, [id^='client_enfants_'] div .btn_add span {
      display: inline-block;
      height: 2.4375rem;
      width: 2.4375rem;
      line-height: 2.4375rem;
      text-align: center; }
    [id^='client_enfants_'] div.delete-btn, [id^='client_enfants_'] div.add-btn {
      display: flex;
      align-items: center;
      padding-top: 0.6rem; }

#prototypeEnfants div {
  margin-right: 0.5rem; }
  #prototypeEnfants div .btn_add {
    padding: unset;
    background-color: #f7585d; }
  #prototypeEnfants div .btn_cancel {
    padding: unset;
    background-color: #4b3041; }
  #prototypeEnfants div .delete span, #prototypeEnfants div .btn_add span {
    display: inline-block;
    height: 2.4375rem;
    width: 2.4375rem;
    line-height: 2.4375rem;
    text-align: center; }
  #prototypeEnfants div.delete-btn, #prototypeEnfants div.add-btn {
    display: flex;
    align-items: center; }

/****************************
*                           *
*        Mon programme      *
*                           *
*****************************/
.monProgramme__bloc {
  font-size: 0.9em;
  line-height: 1.5;
  min-height: 110px;
  display: flex; }
  .monProgramme__bloc .monProgramme__content {
    padding: 0px 10px; }
  .monProgramme__bloc .monProgramme__titre {
    font-size: 1.3em; }
  .monProgramme__bloc .bloc-icon {
    min-width: 80px; }

/************************************************
*                                               *
*             	 VUE ACTIVATION                *
*                                               *
*************************************************/
.banniere-activation {
  background: url(../images/header.jpg) no-repeat;
  height: 120px;
  width: 100%; }

.activation .checked i {
  font-size: 1.05rem; }
.activation h1 {
  padding-bottom: 14px;
  width: fit-content; }
.activation button {
  padding: 18px 20px; }

.cnilMessageInfoRecueillies {
  text-align: justify; }

.activation-menuWrap {
  position: absolute;
  width: 100%;
  top: 0;
  text-align: center; }

.activation-puce {
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  text-align: center;
  line-height: 3.5em;
  font-size: 1.2em;
  font-weight: bold; }

.activation-puces {
  margin: 50px auto;
  display: flex;
  justify-content: center; }

.activation-puce-separator {
  width: 15px;
  height: 2.2em; }

.activation-line {
  width: 15em;
  margin: auto; }

.activation-etape {
  text-align: center;
  padding-top: 20px;
  padding-bottom: 50px; }

.activation input {
  outline: none;
  width: 100%;
  padding: 14px 20px;
  display: block;
  transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  border-radius: 0;
  height: auto; }

.activationForm1 label {
  text-align: right;
  line-height: 1.3em; }

.activation-etape1-text {
  line-height: 1.2em;
  padding: 0 0 45px 0; }

.activation-compte input {
  padding: 0.5rem; }

.activation-compte input[type="checkbox"] {
  display: inline;
  width: inherit; }

.activation-confirm-checkboxes {
  margin: 25px 0 0 15px; }

.activation-button-confirmation {
  padding: 13px 25px; }

.activation-compte-confirmation-label {
  text-indent: -27px;
  padding-left: 27px;
  line-height: 0.4em;
  cursor: pointer; }

.activation-compte-cgv-label {
  text-indent: -27px;
  padding-left: 27px;
  line-height: 0.4em;
  cursor: pointer; }

.activation-compte button {
  width: initial;
  margin: 40px 0px; }

.activation-etape3-text1 {
  padding: 0 0 15px 0;
  font-weight: bold;
  font-size: 1.2em; }

.activation-etape3-text2 {
  padding: 0 0 20px 0; }

@media only screen and (max-width: 40em) {
  .banniere-activation {
    background-size: auto 100%;
    height: 66px;
    margin: 0 0 50px 0; }

  .activation-menu {
    background: none; }

  #carteAdFidHome {
    width: 12em; }

  .activationForm1 label {
    text-align: left; }

  .activation-etape {
    padding-bottom: 20px; }

  .activation-compte .form-group--margin {
    margin-left: 0; } }
@media only screen and (max-width: 64em) {
  .activation-menu {
    background: none; } }
.activationForm1 {
  margin: auto; }

.activation-ma-carte-exclusive {
  font-size: 2.5em; }

.activation-carte-bandeau {
  height: 130px;
  background-image: url(../images/fond-banniere.jpg); }

.activation-menu {
  background: url("../images/white.jpg") repeat-x;
  position: absolute;
  top: 0px;
  left: 0;
  padding-bottom: 0;
  margin-bottom: -9999px;
  overflow: hidden; }

.activation h1 {
  font-size: 2.5em; }

/************************************************
*                                               *
*             	  VUE LOGIN                     *
*                                               *
*************************************************/
.label-captcha {
  display: block;
  min-width: 80px; }

.imgTop {
  border: none;
  height: 100%;
  padding-bottom: 40px; }

@media only screen and (min-width: 64em) {
  .imgTop {
    background: url(../images/fond.jpg) repeat;
    padding-top: 3em; }

  .logo-login {
    margin-top: 5rem; } }
.formLogin {
  margin-bottom: 20px; }
  .formLogin p {
    text-align: left; }
  .formLogin form p {
    font-size: 1.125em; }
  .formLogin input {
    height: 50px;
    padding: 20px;
    font-size: 0.9rem;
    box-shadow: unset; }

.moncompte {
  margin-top: 28px; }
  .moncompte .row {
    padding: 5px 0px 10px 0px; }
  .moncompte p {
    font-size: 0.8rem; }
  .moncompte .activation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    width: 100%; }
    .moncompte .activation img {
      width: 115px;
      margin: 0;
      position: relative;
      left: 15px;
      z-index: 2;
      transform: rotate(-15deg); }
    .moncompte .activation a {
      flex-shrink: 0; }
      .moncompte .activation a span {
        padding-left: 25px; }

#bienvenue {
  line-height: 1; }

#bienvenue hr {
  margin-top: 10px; }

#divBienvenue {
  padding: 0;
  margin: 0; }

#bienvenue {
  font-size: 3em;
  padding-bottom: 10px; }

#divBienvenue {
  padding-left: 5%; }

#bienvenue hr {
  margin: 30px 0px 0px 0px; }

.formLogin {
  font-size: 0.9em;
  line-height: 1.2;
  margin-bottom: 0; }

.formLogin p {
  line-height: 1.2; }

.captcha-img {
  width: 100%;
  margin-top: 0px;
  margin-bottom: 0px; }

#login_captcha {
  margin-bottom: 0px; }

.span-captcha {
  margin-bottom: 10px; }

.labelCaptcha {
  line-height: 50px;
  min-height: 50px; }

.label-captcha label {
  font-size: 1.75em; }

/*# sourceMappingURL=styles.css.map */
