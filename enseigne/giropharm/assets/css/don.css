h4{
    margin-top: 10px;
    color: #FFF;
    font-size: 20px;
    text-transform: uppercase;
}
p{
    font-style: italic;
}
body {
    position: relative;
    background-color: #97D700;
}
body:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 318px;
    height: 450px;
    background-image: url("../images/feuilles gauches.png");
    background-repeat: no-repeat;
    z-index: 1;
}
.container {
    z-index: 2;
    position: relative;
}
#don-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 50px;
    flex: 1;
    margin: 0 1rem;
}

#don-container .title{
    background-color: #4B3242;
    min-height: 59px;
    text-align: center;
    border-top-right-radius: 30px;
    border-top-left-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
#don-container > .row.content {
    background-color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    border-radius: 30px;
    text-align: center;
    padding-bottom: 1rem;
    margin: 40px 1rem 1rem;
    max-width: 600px;
    width: 100%;
}
#don-container .small-5 div{
    display: flex;
    flex-direction: row-reverse;
}
label[for="don_don"]{
    line-height: 1.2;
    margin-left: 5px;
}
label[for="don_don"] span{
    display: none;
}
form[name="don"] .parentButton{
    border-radius: 30px;
    margin-top: 20px;
    background-color: #4B3242;
    color: #FFF;
}
.flex{
    display: flex;
}
.justify-center{
    justify-content: center;
}
.parentImage {
    flex: 487px;
    display: none;
    flex-direction: column;
    justify-content: center;
    padding-left: 75px;
    padding-top: 75px;
    &.parentImage--parentImage2 {
        display: none;
        padding-top: 300px;
    }
}
#logoPageDon {
    margin-top: 40px;
    margin-bottom: 30px;
}

.img-logo-girogreen {
    max-height: 100px;
    width: auto;
}
@media screen and (min-width: 40em) {
    body:after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 364px;
        height: 540px;
        background-image: url("../images/feuilles droites.png");
        background-repeat: no-repeat;
        z-index: 1;
    }
}
@media screen and (min-width: 64em) {
    #don-container {
        padding-top: 124px;
        margin: 0;
    }
    #don-container > .row.title,
    #don-container > .row.content {
        width: 600px;
    }
    .parentImage {
        display: flex;
        &.parentImage--parentImage2 {
            display: flex;
        }
    }
    #don-container > .row.content {
        margin-top: 80px;
    }
    .img-logo-girogreen {
        max-height: inherit;
        max-width: 100%;
        height: auto;
        -ms-interpolation-mode: bicubic;
        display: inline-block;
        vertical-align: middle;
    }
}

.mtxl {
    margin-top: 3rem;
}

a {
    color: #4B3242;
    text-decoration: underline;
}
