<?php

namespace Giropharm\Service;

use App\Services\NewClientsServiceInterface;
use Giropharm\DTO\ClientUpdate;
use Giropharm\Entity\Client;

class NewClientsService extends \App\Services\NewClientsService implements NewClientsServiceInterface
{
    public function getDTOClass(): string
    {
        return Client::class;
    }

    public function getDTOUpdateClass(): string
    {
        return ClientUpdate::class;
    }
}
