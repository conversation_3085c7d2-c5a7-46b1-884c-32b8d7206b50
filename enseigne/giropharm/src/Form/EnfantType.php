<?php

namespace Giropharm\Form;

use App\Form\BaseType;
use Giropharm\Entity\Enfant;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EnfantType extends BaseType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'prenom',
                TextType::class,
                [
                    'required' => false,
                    'attr' => [
                        'maxlength' => 20,
                    ],
                ]
            )
            ->add(
                'dateNaissance',
                DateType::class,
                [
                    'required' => false,
                    'widget' => 'single_text',
                    'format' => 'dd/MM/yyyy',
                    'html5' => false,
                    'attr' => [
                        'placeholder' => 'JJ/MM/AAAA',
                    ],
                ]
            )
            ->add(
                'genre',
                ChoiceType::class,
                [
                    'choices' => $this->getFromGlobalDefinitions($options['globalDefinitions'], 'Genres', 'Id', 'Libelle'),
                    'required' => false,
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Enfant::class,
            'globalDefinitions' => false,
        ]);
    }

    public function getName(): string
    {
        return 'enfant';
    }

    public function getBlockPrefix(): string
    {
        return 'enfant';
    }
}
