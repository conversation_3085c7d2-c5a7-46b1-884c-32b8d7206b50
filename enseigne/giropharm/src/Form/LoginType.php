<?php

namespace Giropharm\Form;

use MeteoConcept\HCaptchaBundle\Form\HCaptchaType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class LoginType extends AbstractType
{
    private mixed $options;

    public function __construct($options = null)
    {
        $this->options = $options;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('_username', TextType::class, [
                'required' => true,
                // 'data' => $this->options['default_login'],
                'attr' => [
                    'placeholder' => 'login._username',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            // ->add('codePostal', TextType::class, array(
            //         'required' => true,
            //         'attr' => array(
            //             'placeholder' => 'login.codePostal',
            //         ),
            //         'label_attr' => array(
            //             'class'=>'sr-only',
            //         ),
            //         'constraints' => array(
            //                new NotBlank(),
            //        )

            // ))
            ->add('_password', TextType::class, [
                'required' => true,
                'attr' => [
                    'placeholder' => 'login.dateNaissance',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('captcha', HCaptchaType::class, [
                'label' => false,
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'login.submit',
                'attr' => [
                    'class' => 'btn',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            [
                'default_login' => null,
            ]
        );
    }

    public function getBlockPrefix(): string
    {
        return 'login';
    }
}
