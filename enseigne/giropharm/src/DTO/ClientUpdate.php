<?php

namespace Giropharm\DTO;

use App\DTO\ClientUpdateInterface;
use App\Entity\ClientInterface;
use Giropharm\Entity\Client;

class ClientUpdate extends \App\DTO\ClientUpdate
{
    public ?array $ANIMAL = [];
    public ?string $AUTREANIMAL = null;

    public static function fromClient(Client|ClientInterface $client): ClientUpdateInterface
    {
        $clientUpdate = parent::fromClient($client);
        $clientUpdate->AUTREANIMAL = $client->getAutreAnimal() ?? '';
        $clientUpdate->ANIMAL = $client->getAnimals()->map(fn ($animal) => $animal->getId())->toArray();

        return $clientUpdate;
    }
}
