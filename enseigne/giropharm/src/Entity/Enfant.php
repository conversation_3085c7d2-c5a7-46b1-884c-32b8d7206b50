<?php

namespace Giropharm\Entity;

use Symfony\Component\Validator\Context\ExecutionContextInterface;

class Enfant
{
    protected ?string $genre = null;

    protected ?string $prenom = null;

    protected ?string $nom = null;

    protected \DateTimeInterface $dateNaissance;

    /**
     * Validation Annee de naissance.
     */
    public function valideDateNaissance(ExecutionContextInterface $context)
    {
        if ($this->getDateNaissance() > new \DateTime()) {
            $context->buildViolation('client.errorDateNaissance')
                ->atPath('dateNaissance')
                ->addViolation();
        }
    }

    /**
     * Validation mail ou mobile existe.
     */
    public function fullOrEmpty(ExecutionContextInterface $context): void
    {
        if (
            (null != $this->getPrenom() && (null == $this->getDateNaissance() || null == $this->getGenre()))
            || (null != $this->getDateNaissance() && (null == $this->getPrenom() || null == $this->getGenre()))
            || (null != $this->getGenre() && (null == $this->getPrenom() || null == $this->getDateNaissance()))
        ) {
            $context->buildViolation('client.enfantMerciDeRemplirTousLesChamps')
                ->atPath('prenom')
                ->addViolation();
        }
    }

    public function getGenre(): ?string
    {
        return $this->genre;
    }

    public function setGenre(?string $genre): void
    {
        $this->genre = $genre;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(?string $prenom): void
    {
        $this->prenom = $prenom;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(?string $nom): void
    {
        $this->nom = $nom;
    }

    public function getDateNaissance(): \DateTimeInterface
    {
        return $this->dateNaissance;
    }

    public function setDateNaissance(\DateTimeInterface $dateNaissance): void
    {
        $this->dateNaissance = $dateNaissance;
    }
}
