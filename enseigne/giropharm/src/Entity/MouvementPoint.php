<?php

namespace Giropharm\Entity;

use App\Entity\MouvementPoint as DefaultMouvementPoint;

class MouvementPoint extends DefaultMouvementPoint
{
    public function isPositif(): bool
    {
        return (float) str_replace(',', '.', $this->nbPoints) > 0;
    }

    public function isBonusBienvenue(): bool
    {
        return 'BONUS BIENVENUE' == $this->operation;
    }

    public function isGenerationChequeFid(): bool
    {
        return 'Génération d\'un chèque fidélité' == $this->operation;
    }

    public function isUtilisationChequeFid(): bool
    {
        return 'Utilisation d\'un chèque fidélité' == $this->operation;
    }

    public function isUtilisationCoupon(): bool
    {
        preg_match('/Utilisation coupon promo/', $this->operation, $matches);

        return !empty($matches);
    }

    public function isAnnulationCheque(): bool
    {
        return 'Annulation d\'un chèque fidélité' == $this->operation;
    }
}
