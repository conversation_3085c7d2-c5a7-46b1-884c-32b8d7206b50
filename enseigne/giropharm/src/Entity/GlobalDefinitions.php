<?php

namespace Giropharm\Entity;

use Doctrine\Common\Collections\ArrayCollection;

class GlobalDefinitions extends \App\Entity\GlobalDefinitions
{
    /** @var ArrayCollection<Animal> */
    protected ArrayCollection $animals;

    public function __construct()
    {
        parent::__construct();
        $this->animals = new ArrayCollection();
    }

    public function addAnimal(Animal $animal): static
    {
        $this->animals->add($animal);

        return $this;
    }

    public function removeAnimal(Animal $animal): static
    {
        $this->animals->add($animal);

        return $this;
    }

    public function getAnimals(): ArrayCollection
    {
        return $this->animals;
    }
}
