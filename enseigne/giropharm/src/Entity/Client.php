<?php

namespace Giropharm\Entity;

use App\Entity\Client as DefaultClient;
use App\Entity\GlobalDefinitions;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class Client extends DefaultClient
{
    /**
     * @var ArrayCollection<MouvementPoint>
     */
    private readonly ArrayCollection $mouvementPoints;

    /**
     * @var ArrayCollection<Animal>
     */
    private Collection $animals;
    private ?string $autreAnimal = null;
    private ?array $categorieArticles = [];

    /**
     * @var ArrayCollection<Enfant>
     */
    protected Collection $enfants;

    public function __construct()
    {
        parent::__construct();
        //        $this->enfants = new ArrayCollection();
        $this->mouvementPoints = new ArrayCollection();
        $this->enfants = new ArrayCollection();
        $this->animals = new ArrayCollection();
        //        $this->isActive = true;
        $this->codePaysClient = GlobalDefinitions::CODE_PAYS_FR;
        $this->categorieArticles = [];
        $this->autreAnimal = '';
    }

    public function getMouvementPoints(): Collection
    {
        return $this->mouvementPoints;
    }

    public function addMouvementPoint(\App\Entity\MouvementPoint $mouvementPoint): void
    {
        $this->mouvementPoints->add($mouvementPoint);
    }

    public function removeMouvementPoint(\App\Entity\MouvementPoint $mouvementPoint): void
    {
        $this->mouvementPoints->removeElement($mouvementPoint);
    }

    public function getEnfants(): Collection
    {
        return $this->enfants;
    }

    public function addEnfant(Giropharm\Entity\Enfant $enfant): void
    {
        $this->enfants->add($enfant);
    }

    /**
     * @param Enfant $enfant
     */
    public function removeEnfant(Giropharm\Entity\Enfant $enfant): void
    {
        $this->enfants->removeElement($enfant);
    }

    public function getAnimals(): ArrayCollection
    {
        return $this->animals;
    }

    public function setAnimals(ArrayCollection $animals): void
    {
        $this->animals = $animals;
    }

    public function getAutreAnimal(): ?string
    {
        return $this->autreAnimal;
    }

    public function setAutreAnimal(?string $autreAnimal = null): void
    {
        $this->autreAnimal = $autreAnimal;
    }

    public function getCategorieArticles(): array
    {
        return $this->categorieArticles;
    }

    public function setCategorieArticles(?array $categorieArticles = []): void
    {
        $this->categorieArticles = $categorieArticles;
    }
}
