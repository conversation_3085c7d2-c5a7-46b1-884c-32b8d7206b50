App\Entity\Client:
    attributes:
        mouvementPoints:
            serialized_name: "collectionMouvement"
        nbrePoints:
            serialized_name: "@CAGNOTTE"
        envoiCourrierInterne:
            serialized_name: "@ENVOIECOURRIERINTERNE"
        envoiCourrierExterne:
            serialized_name: "@OTOTTOTOT"

Giropharm\Entity\Client:
    attributes:
        enfants:
            serialized_name: "collectionEnfant"
        animals:
            serialized_name: "collectionAnimal"
        autreAnimal:
            serialized_name: "@ANIMALAUTRE"
