<?php

declare(strict_types=1);

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use App\Form\ClientTypeInterface;
use App\Form\Login\LoginType;
use App\Security\HCaptchaAuthenticator;
use App\Services\GlobalDefinitionsServiceInterface;
use App\Services\NewClientsServiceInterface;
use Giropharm\Form\ClientType;
use Giropharm\Service\NewClientsService;
use Giropharm\Service\NewGlobalDefinitionsService;

return function (ContainerConfigurator $container): void {
    // default configuration for services in *this* file
    $services = $container->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
//        ->bind('$projectDir', '%kernel.project_dir%')
        ->bind('$webserviceStaticParams', '%webservice_static_params%')
    ;

    $container->parameters()->set('features', [
        'activation' => false,
        'don' => true,

        // BIENVENUE
        'remise.bienvenue' => true,
        'remise.bienvenue.telechargement' => false,

        // ANNIVERSAIRE
        'remise.anniversaire' => true,
        'remise.anniversaire.telechargement' => false,

        // FIDELITE
        'remise.fidelite' => true,
        'remise.fidelite.telechargement' => false,
    ]);

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Giropharm\\', '../src/')
        ->exclude('../src/{DependencyInjection,Entity,Kernel.php}');

    $services->alias(LoginType::class, \Giropharm\Form\LoginType::class)->public();
    $services->alias('enseigne_authenticator', HCaptchaAuthenticator::class)->public();
    $services->alias(GlobalDefinitionsServiceInterface::class, NewGlobalDefinitionsService::class)->public();
    $services->alias(NewClientsServiceInterface::class, NewClientsService::class)->public();
    $services->alias(ClientTypeInterface::class, ClientType::class)->public();
};
