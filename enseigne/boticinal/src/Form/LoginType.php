<?php

namespace Boticinal\Form;

use MeteoConcept\HCaptchaBundle\Form\HCaptchaType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class LoginType extends AbstractType
{
    /**
     * @var mixed|null
     */
    private mixed $options;

    public function __construct($options = null)
    {
        $this->options = $options;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('_username', 'Symfony\Component\Form\Extension\Core\Type\TextType', [
                'required' => true,
                // 'data' => $this->options['default_login'],
                'attr' => [
                    'placeholder' => 'login._username',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('_password', 'Symfony\Component\Form\Extension\Core\Type\TextType', [
                'required' => true,
                'attr' => [
                    'placeholder' => 'login._password',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('captcha', HCaptchaType::class, [
                'label' => false,
            ])
            ->add('submit', 'Symfony\Component\Form\Extension\Core\Type\SubmitType', [
                'label' => 'login.submit',
                'attr' => [
                    'class' => 'btn',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            [
                'default_login' => null,
            ]
        );
    }

    public function getBlockPrefix(): string
    {
        return 'login';
    }
}
