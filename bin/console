#!/usr/bin/env php
<?php

use App\Kernel;
use Symfony\Bundle\FrameworkBundle\Console\Application;

$base = dirname(__DIR__);

// 1) Lire ENSEIGNE / ENV_TIER depuis l'env réel
$enseigne = $_SERVER['ENSEIGNE'] ?? $_ENV['ENSEIGNE'] ?? '';
$tier = $_SERVER['ENV_TIER'] ?? $_ENV['ENV_TIER'] ?? 'dev';

// 2) Fallback dev: .enseigne.local
if ($enseigne === '' && is_file($base.'/.enseigne.local')) {
    foreach (file($base.'/.enseigne.local', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) as $line) {
        if ($line[0] === '#' || !str_contains($line, '=')) continue;
        [$k, $v] = explode('=', $line, 2);
        $k = trim($k); $v = trim($v, " \t\n\r\0\x0B\"'");
        if ($k === 'ENSEIGNE')  { $enseigne = $v; }
        if ($k === 'ENV_TIER')  { $tier = $v; }
    }
}

$extra = [];
if ($enseigne !== '') {
    foreach ([
         "enseigne/{$enseigne}/.env",
         "enseigne/{$enseigne}/.env.{$tier}",
         "enseigne/{$enseigne}/.env.local",
         "enseigne/{$enseigne}/.env.{$tier}.local",
    ] as $rel) {
         if (is_file($base.'/'.$rel)) {
            $extra[] = $rel;
         }
    }
}

$_SERVER['APP_RUNTIME_OPTIONS'] = [
    'dotenv_path' => '.env',
    'dotenv_extra_paths' => $extra,
    'dotenv_overload' => !in_array($tier, ['preprod', 'prod'], true),
];

if (!is_dir(dirname(__DIR__).'/vendor')) {
    throw new LogicException('Dependencies are missing. Try running "composer install".');
}

if (!is_file(dirname(__DIR__).'/vendor/autoload_runtime.php')) {
    throw new LogicException('Symfony Runtime is missing. Try running "composer require symfony/runtime".');
}

require_once dirname(__DIR__).'/vendor/autoload_runtime.php';

return function (array $context) use ($enseigne) {
    $kernel = new Kernel($context['APP_ENV'], (bool) $context['APP_DEBUG'], $enseigne);
    return new Application($kernel);
};
