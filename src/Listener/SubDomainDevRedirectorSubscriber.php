<?php

namespace App\Listener;

use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

readonly class SubDomainDevRedirectorSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly UrlGeneratorInterface $router,
        #[Autowire(env: 'ENSEIGNE')] private ?string $enseigne = null,
        #[Autowire(env: 'APP_ENV')] private ?string $environment = null,
    ) {
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if ('dev' !== $this->environment) {
            return;
        }

        if ('' === $this->enseigne || null === $this->enseigne) {
            return;
        }

        $host = $event->getRequest()->getHost();
        if (str_contains($host, $this->enseigne)) {
            return;
        }

        $baseUrlWithSubDomain = $this->enseigne.'.localhost';
        $route = $event->getRequest()->attributes->get('_route');
        $route = $this->router->generate($route, $event->getRequest()->attributes->get('_route_params'));
        $route = sprintf('%s%s%s', 'https://', $baseUrlWithSubDomain, $route);

        $event->setResponse(new RedirectResponse($route, Response::HTTP_FOUND));
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'kernel.request' => 'onKernelRequest',
        ];
    }
}
