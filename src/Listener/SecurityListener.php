<?php

namespace App\Listener;

use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;

class SecurityListener
{
    protected $em;

    protected $session;

    protected $tokenStorage;

    protected $flashMessage;

    public function __construct(TokenStorageInterface $tokenStorage, SessionInterface $session)
    {
        $this->tokenStorage = $tokenStorage;
        $this->session = $session;
    }

    public function onSecurityInteractiveLogin(InteractiveLoginEvent $event)
    {
        // if ($this->params->get('login.allowHwiOAuth', false)) {
        // 	$user = $this->tokenStorage->getToken()->getUser();
        // 	$em = $this->em->getManagerForClass(get_class($user));
        // 	$client = $em->getRepository(get_class($user))->findOneBy(array('codeCarte' => $user->getCodeCarte()));
        // 	$user->loadDataFromReseau($client);
        // 	$em->merge($user);
        // 	$em->flush();
        // }
    }
}
