<?php

namespace App\DataCollector;

use Symfony\Bundle\FrameworkBundle\DataCollector\AbstractDataCollector;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class EnseigneCollector extends AbstractDataCollector
{
    public function __construct(
        #[Autowire(env: 'ENSEIGNE')] private readonly string $enseigne,
    ) {
    }

    public function collect(Request $request, Response $response, ?\Throwable $exception = null): void
    {
        $this->data = [
            'enseigne' => $this->enseigne,
            'isEmpty' => '' === $this->enseigne,
        ];
    }

    public static function getTemplate(): ?string
    {
        return 'data_collector/enseigne.html.twig';
    }

    public function getEnseigne(): string
    {
        return $this->data['enseigne'];
    }

    public function isEmpty(): bool
    {
        return $this->data['isEmpty'];
    }
}
