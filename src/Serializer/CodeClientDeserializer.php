<?php

namespace App\Serializer;

use App\DTO\WebserviceError;
use App\DTO\WebserviceResponse;
use Symfony\Component\Serializer\SerializerInterface;

readonly class CodeClientDeserializer implements ResponseDeserializerInterface
{
    use ManageResponseErrorsTrait;

    public function __construct(
        private SerializerInterface $serializer,
    ) {
    }

    public function deserialize(string $response, string $class = ''): string|WebserviceError
    {
        $webserviceResponse = $this->serializer->deserialize($response, WebserviceResponse::class, 'xml');
        if ($webserviceResponse->isSuccess()) {
            $client = $webserviceResponse->resultat['client'] ?? [];
            $code = $client['CODECARTE']
                ?? $client['@CODECARTE']
                ?? null;

            if (null !== $code) {
                return $code;
            }

            return new WebserviceError('empty_result');
        }

        return $this->manageResponseErrors($webserviceResponse);
    }
}
