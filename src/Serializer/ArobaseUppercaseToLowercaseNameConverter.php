<?php

namespace App\Serializer;

use Symfony\Component\Serializer\NameConverter\NameConverterInterface;

class ArobaseUppercaseToLowercaseNameConverter implements NameConverterInterface
{
    public function normalize(string $propertyName, ?string $class = null, ?string $format = null, array $context = []): string
    {
        return $propertyName;
    }

    public function denormalize(string $propertyName, ?string $class = null, ?string $format = null, array $context = []): string
    {
        if (str_starts_with($propertyName, '@')) {
            return strtolower(substr($propertyName, 1));
        }

        return $propertyName;
    }
}
