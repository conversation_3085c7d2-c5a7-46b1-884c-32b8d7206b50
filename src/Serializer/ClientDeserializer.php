<?php

namespace App\Serializer;

use App\DTO\WebserviceError;
use App\DTO\WebserviceResponse;
use App\Entity\Client;
use App\Entity\ClientInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;

readonly class ClientDeserializer implements ResponseDeserializerInterface
{
    use ManageResponseErrorsTrait;

    public function __construct(
        private SerializerInterface $serializer,
        private NormalizerInterface $normalizer,
    ) {
    }

    public function deserialize(string $response, string $class = Client::class): WebserviceError|ClientInterface|true
    {
        $webserviceResponse = $this->serializer->deserialize($response, WebserviceResponse::class, 'xml');

        if ($webserviceResponse->isSuccess()) {
            if (isset($webserviceResponse->resultat['client'])) {
                return $this->normalizer->denormalize($webserviceResponse->resultat['client'], $class, 'xml');
            }

            return true;
        }

        return $this->manageResponseErrors($webserviceResponse);
    }
}
