<?php

namespace App\Serializer;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Serializer\Exception\InvalidArgumentException;
use Symfony\Component\Serializer\SerializerAwareInterface;
use Symfony\Component\Serializer\SerializerInterface;

class CollectionNormalizer implements SerializerAwareInterface, \Symfony\Component\Serializer\Normalizer\NormalizerInterface, \Symfony\Component\Serializer\Normalizer\DenormalizerInterface
{
    /**
     * @var SerializerInterface|\Symfony\Component\Serializer\Normalizer\NormalizerInterface|\Symfony\Component\Serializer\Normalizer\DenormalizerInterface
     */
    protected $serializer;

    /**
     * @throws InvalidArgumentException
     */
    #[\Override]
    public function setSerializer(SerializerInterface $serializer): void
    {
        if (!$serializer instanceof \Symfony\Component\Serializer\Normalizer\NormalizerInterface
            || !$serializer instanceof \Symfony\Component\Serializer\Normalizer\DenormalizerInterface) {
            throw new InvalidArgumentException(sprintf('Serializer must implement "%s" and "%s"', \Symfony\Component\Serializer\Normalizer\NormalizerInterface::class, \Symfony\Component\Serializer\Normalizer\DenormalizerInterface::class));
        }

        $this->serializer = $serializer;
    }

    /**
     * Returned normalized data.
     *
     * @param Collection $object object to normalize
     * @param mixed      $format
     *
     * @return array
     */
    #[\Override]
    public function normalize($object, ?string $format = null, array $context = []): array|bool|string|int|float|\ArrayObject|null
    {
        $result = [];

        foreach ($object as $item) {
            $serializedItem = $this->serializer->normalize($item, $format, $context);
            $result[] = $serializedItem;
        }

        return $result;
    }

    /**
     * Returns collection of denormalized data.
     *
     * @param mixed $format
     *
     * @return ArrayCollection
     */
    #[\Override]
    public function denormalize($data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (!is_array($data) || '' === $data[0]) {
            return new ArrayCollection();
        }

        $itemType = $this->getItemType($type);
        if (!$itemType) {
            return new ArrayCollection($data);
        }

        $result = new ArrayCollection();

        $nameObject = array_key_first($data[0]);
        $data = $this->fixXmlDeserializationOfSingleElementInCollection($data, $nameObject);
        foreach ($data[0][$nameObject] as $item) {
            $result->add($this->serializer->denormalize($item, $itemType, $format, $context));
        }

        return $result;
    }

    /**
     * @param string $class
     */
    protected function getItemType($class): ?string
    {
        $collectionRegexp = '/^(Doctrine\\\Common\\\Collections\\\ArrayCollection|ArrayCollection)(<([\w_<>\\\]+)>)$/';

        if (preg_match($collectionRegexp, $class, $matches)) {
            return $matches[3];
        }

        if (str_ends_with($class, '[]')) {
            return str_replace('[]', '', $class);
        }

        return null;
    }

    #[\Override]
    public function supportsNormalization($data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Collection;
    }

    #[\Override]
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        $preg_match = (bool) preg_match(
            '/^(Doctrine\\\Common\\\Collections\\\ArrayCollection|ArrayCollection)(<[\w_<>\\\]+>)?$/',
            $type);

        if (str_ends_with($type, '[]')) {
            return true;
        }

        return $preg_match;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            '*' => true,
        ];
    }

    /**
     * La désérialisation d'une collection en XML foire lorsqu'il n'y a qu'un objet dans la collection.
     * On convertit la collection en un tableau de tableau pour avoir le comportement commun à une collection de plusieurs objets.
     *
     * @return array<array>
     */
    public function fixXmlDeserializationOfSingleElementInCollection(array $data, string $nameObject): array
    {
        if (!is_array($data[0][$nameObject][array_key_first($data[0][$nameObject])])) {
            $data[0][$nameObject] = [$data[0][$nameObject]];
        }

        return $data;
    }
}
