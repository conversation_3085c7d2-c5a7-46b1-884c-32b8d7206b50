<?php

namespace App\DTO;

/**
 * @template Failure of mixed
 * @template Success of mixed
 */
class Result
{
    private readonly \Closure $onError;

    /**
     * @param Success|null                  $success
     * @param Failure|null                  $error
     * @param callable(\Throwable): Failure $onError
     */
    private function __construct(
        private readonly mixed $success = null,
        private readonly mixed $error = null,
        ?callable $onError = null,
    ) {
        $this->onError = $onError ?: fn ($e) => $e;
    }

    /**
     * @param Success                       $data
     * @param callable(\Throwable): Failure $onError
     *
     * @return self<Failure, Success>
     */
    public static function success($data, ?callable $onError = null): self
    {
        return new self(success: $data, onError: $onError);
    }

    /**
     * @param Failure $error
     *
     * @return self<Failure, Success>
     */
    public static function error(WebserviceError $error): self
    {
        return new self(error: $error);
    }

    /**
     * @param callable(): Success           $callable
     * @param callable(\Throwable): Failure $onError
     *
     * @return self<Failure, Success>
     */
    public static function try(callable $callable, callable $onError)
    {
        try {
            return self::success($callable());
        } catch (\Throwable $throwable) {
            return self::error($onError($throwable));
        }
    }

    public function isSuccess(): bool
    {
        return null !== $this->success;
    }

    public function isFailure(): bool
    {
        return null !== $this->error;
    }

    /**
     * @return Success
     */
    public function get()
    {
        if ($this->isSuccess()) {
            return $this->success;
        }

        throw new \RuntimeException('Pas success');
    }

    /**
     * @return Failure
     */
    public function getError()
    {
        if ($this->isFailure()) {
            return $this->error;
        }

        throw new \RuntimeException('Pas failure');
    }

    /**
     * @template TSuccess
     *
     * @param callable(Success): TSuccess $callable
     *
     * @return self<Failure, TSuccess>
     */
    public function map(callable $callable)
    {
        if (!$this->isSuccess()) {
            return $this;
        }

        return Result::try(
            fn () => $callable($this->success),
            $this->onError
        );
    }
}
