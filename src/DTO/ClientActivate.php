<?php

namespace App\DTO;

use App\Entity\ClientInterface;

class ClientActivate implements ClientActivateInterface
{
    public ?string $CODECARTE = null;

    public ?string $SITE = null;

    public ?string $MAGASIN = null;

    public ?string $CIVILITE = null;

    public ?string $NOM = null;

    public ?string $PRENOM = null;

    public ?string $NUMERO = null;

    public ?string $CODEPOSTAL = null;

    public ?string $ESCALIER = null;

    public ?string $BATIMENT = null;

    public ?string $VOIE = null;

    public ?string $LIEUDIT = null;

    public ?string $VILLE = null;

    public ?\DateTimeInterface $DATENAISSANCE = null;

    public ?string $TELEPHONEFIXE = null;

    public ?string $TELEPHONEMOBILE = null;

    public ?string $EMAIL = null;

    public ?string $CODEPAYSCLIENT = null;

    public ?string $ENVOIESMSINTERNE = null;

    public ?string $ENVOIEEMAILINTERNE = null;

    public ?string $ENVOIESMSEXTERNE = null;

    public ?string $ENVOIEEMAILEXTERNE = null;

    public ?string $ENVOIECOURRIERINTERNE = null;

    public ?string $ENVOIECOURRIEREXTERNE = null;

    public ?string $SITUATIONFAMILIALE = null;

    public ?string $X = null;

    public ?string $Y = null;

    public static function fromClient(ClientInterface $client): ClientActivateInterface
    {
        $clientActivate = new static();
        $clientActivate->CODECARTE = $client->getCodeCarte() ?? '';
        $clientActivate->SITE = $client->getSite() ?? '';
        $clientActivate->MAGASIN = $client->getMagasinLibelle() ?? '';
        $clientActivate->CIVILITE = $client->getCivilite() ?? '';
        $clientActivate->NOM = $client->getNom() ?? '';
        $clientActivate->PRENOM = $client->getPrenom() ?? '';
        $clientActivate->NUMERO = $client->getNumero() ?? '';
        $clientActivate->CODEPOSTAL = $client->getCodepostal() ?? '';
        $clientActivate->ESCALIER = $client->getEscalier() ?? '';
        $clientActivate->BATIMENT = $client->getBatiment() ?? '';
        $clientActivate->VOIE = $client->getVoie() ?? '';
        $clientActivate->LIEUDIT = $client->getLieudit() ?? '';
        $clientActivate->VILLE = $client->getVille() ?? '';
        $clientActivate->DATENAISSANCE = $client->getDateNaissance();
        $clientActivate->TELEPHONEFIXE = $client->getTelephoneFixe() ?? '';
        $clientActivate->TELEPHONEMOBILE = $client->getTelephoneMobile() ?? '';
        $clientActivate->EMAIL = $client->getEmail() ?? '';
        $clientActivate->CODEPAYSCLIENT = $client->getCodePaysClient() ?? '';
        $clientActivate->ENVOIESMSINTERNE = (string) (int) $client->getEnvoieSmsInterne() ?? '';
        $clientActivate->ENVOIEEMAILINTERNE = (string) (int) $client->getEnvoieEmailInterne() ?? '';
        $clientActivate->ENVOIESMSEXTERNE = (string) (int) $client->getEnvoieSmsExterne() ?? '';
        $clientActivate->ENVOIEEMAILEXTERNE = (string) (int) $client->getEnvoieEmailExterne() ?? '';
        $clientActivate->ENVOIECOURRIERINTERNE = (string) (int) $client->getEnvoiCourrierInterne() ?? '';
        $clientActivate->ENVOIECOURRIEREXTERNE = (string) (int) $client->getEnvoiCourrierExterne() ?? '';
        $clientActivate->SITUATIONFAMILIALE = $client->getSituationFamiliale() ?? '';
        $clientActivate->X = (string) $client->getX() ?? '';
        $clientActivate->Y = (string) $client->getY() ?? '';

        return $clientActivate;
    }
}
