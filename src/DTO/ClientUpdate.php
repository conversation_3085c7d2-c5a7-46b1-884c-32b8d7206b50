<?php

namespace App\DTO;

use App\Entity\ClientInterface;

class ClientUpdate implements ClientUpdateInterface
{
    public ?string $CODECARTE = null;

    public ?string $SITE = null;

    public ?string $MAGASIN = null;

    public ?string $CIVILITE = null;

    public ?string $NOM = null;

    public ?string $PRENOM = null;

    public ?string $NUMERO = null;

    public ?string $CODEPOSTAL = null;

    public ?string $ESCALIER = null;

    public ?string $BATIMENT = null;

    public ?string $VOIE = null;

    public ?string $LIEUDIT = null;

    public ?string $VILLE = null;

    public ?\DateTimeInterface $DATENAISSANCE = null;

    public ?string $TELEPHONEFIXE = null;

    public ?string $TELEPHONEMOBILE = null;

    public ?string $EMAIL = null;

    public ?string $CODEPAYSCLIENT = null;

    public ?string $ENVOIESMSINTERNE = null;

    public ?string $ENVOIEEMAILINTERNE = null;

    public ?string $ENVOIESMSEXTERNE = null;

    public ?string $ENVOIEEMAILEXTERNE = null;

    public ?string $ENVOIECOURRIERINTERNE = null;

    public ?string $ENVOIECOURRIEREXTERNE = null;

    public ?string $SITUATIONFAMILIALE = null;

    public ?string $X = null;

    public ?string $Y = null;

    public static function fromClient(ClientInterface $client): ClientUpdateInterface
    {
        $clientUpdate = new static();
        $clientUpdate->CODECARTE = $client->getCodeCarte() ?? '';
        $clientUpdate->SITE = $client->getMagasin()?->getSite() ?? '';
        $clientUpdate->MAGASIN = $client->getMagasin()?->getId() ?? '';
        $clientUpdate->CIVILITE = $client->getCivilite() ?? '';
        $clientUpdate->NOM = $client->getNom() ?? '';
        $clientUpdate->PRENOM = $client->getPrenom() ?? '';
        $clientUpdate->NUMERO = $client->getNumero() ?? '';
        $clientUpdate->CODEPOSTAL = $client->getCodepostal() ?? '';
        $clientUpdate->ESCALIER = $client->getEscalier() ?? '';
        $clientUpdate->BATIMENT = $client->getBatiment() ?? '';
        $clientUpdate->VOIE = $client->getVoie() ?? '';
        $clientUpdate->LIEUDIT = $client->getLieudit() ?? '';
        $clientUpdate->VILLE = $client->getVille() ?? '';
        $clientUpdate->DATENAISSANCE = $client->getDateNaissance();
        $clientUpdate->TELEPHONEFIXE = $client->getTelephoneFixe() ?? '';
        $clientUpdate->TELEPHONEMOBILE = $client->getTelephoneMobile() ?? '';
        $clientUpdate->EMAIL = $client->getEmail() ?? '';
        $clientUpdate->CODEPAYSCLIENT = $client->getCodePaysClient() ?? '';
        $clientUpdate->ENVOIESMSINTERNE = (string) (int) $client->getEnvoieSmsInterne() ?? '';
        $clientUpdate->ENVOIEEMAILINTERNE = (string) (int) $client->getEnvoieEmailInterne() ?? '';
        $clientUpdate->ENVOIESMSEXTERNE = (string) (int) $client->getEnvoieSmsExterne() ?? '';
        $clientUpdate->ENVOIEEMAILEXTERNE = (string) (int) $client->getEnvoieEmailExterne() ?? '';
        $clientUpdate->ENVOIECOURRIERINTERNE = (string) (int) $client->getEnvoiCourrierInterne() ?? '';
        $clientUpdate->ENVOIECOURRIEREXTERNE = (string) (int) $client->getEnvoiCourrierExterne() ?? '';
        $clientUpdate->SITUATIONFAMILIALE = $client->getSituationFamiliale() ?? '';
        $clientUpdate->X = (string) $client->getX() ?? '';
        $clientUpdate->Y = (string) $client->getY() ?? '';

        return $clientUpdate;
    }
}
