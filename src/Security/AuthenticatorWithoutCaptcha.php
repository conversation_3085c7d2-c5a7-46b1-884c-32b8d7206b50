<?php

namespace App\Security;

use App\Form\Login\LoginTypeInterface;
use App\Security\User\AquitemUserProvider;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

class AuthenticatorWithoutCaptcha extends AbstractLoginFormAuthenticator
{
    use PasswordFormaterTrait;
    public const string LOGIN_ROUTE = 'home_login';

    public const string HOME_LOG = 'home_vue';

    public function __construct(
        private readonly AquitemUserProvider $userProvider,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly RouterInterface $router,
        private readonly LoginTypeInterface $loginType,
    ) {
    }

    public function authenticate(Request $request): Passport
    {
        $credentials = [
            'login' => $request->request->all('login')['_username'],
            'password' => $this->formatPassword($request, $this->loginType),
            '_csrf_token' => $request->request->get('_token'),
        ];

        return new Passport(
            new UserBadge(
                $credentials['login'],
                fn ($userIdentifier) => $this->userProvider->loadUserByUsernameAndPassword($credentials['login'], $credentials['password'])
            ),
            new CustomCredentials(
                fn () => true,
                $credentials
            ),
            [new CsrfTokenBadge('authenticate', $credentials['_csrf_token'])]
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return new RedirectResponse($this->urlGenerator->generate(self::HOME_LOG));
    }

    #[\Override]
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        if ($exception instanceof BadCredentialsException) {
            $exception = new CustomUserMessageAuthenticationException('Identification incorrecte');
        }

        if ($request->getSession() instanceof SessionInterface) {
            $request->getSession()->set(SecurityRequestAttributes::AUTHENTICATION_ERROR, $exception);
        }

        return new RedirectResponse($this->router->generate('home_login_statut', ['statut' => 401]));
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->urlGenerator->generate(self::LOGIN_ROUTE);
    }
}
