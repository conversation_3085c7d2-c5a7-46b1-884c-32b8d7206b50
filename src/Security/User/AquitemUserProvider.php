<?php

namespace App\Security\User;

use App\Entity\Client;
use App\Services\ClientAuthenticatorInterface;
use App\Services\NewClientsServiceInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

class AquitemUserProvider implements UserProviderInterface
{
    public function __construct(
        private readonly NewClientsServiceInterface $clientService,
        private readonly ClientAuthenticatorInterface $authenticator,
        public ParameterBagInterface $params,
    ) {
    }

    public function loadUserByIdentifier(string $identifier): UserInterface
    {
        throw new UserNotFoundException(sprintf('Username "%s" does not exist.', $identifier));
    }

    /**
     * @param string $username
     */
    public function loadUserByUsername($username): Client
    {
        throw new UserNotFoundException(sprintf('Username "%s" does not exist.', $username));
    }

    public function loadUserByUsernameAndPassword($username, $password): Client
    {
        $this->clientService->setDisableError(true);
        $client = $this->authenticator->authenticate($username, $password);
        $this->clientService->setDisableError(false);

        if (!$client instanceof Client) {
            throw new UserNotFoundException(sprintf('Username "%s" does not exist.', $username));
        }

        return $client;
    }

    public function refreshUser(UserInterface $user): Client
    {
        if (!$user instanceof Client) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        return $this->loadUserByUsernameAndPassword($user->getUsername(), $user->getPassword());
    }

    public function supportsClass(string $class): bool
    {
        return Client::class === $class || is_subclass_of($class, Client::class);
    }
}
