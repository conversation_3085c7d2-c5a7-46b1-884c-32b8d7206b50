<?php

namespace App\Security;

use App\Form\Login\LoginTypeInterface;
use App\Form\Login\LoginWithDateType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

trait PasswordFormaterTrait
{
    protected function formatPassword(Request $request, LoginTypeInterface $loginType): string
    {
        if (!$loginType instanceof LoginWithDateType) {
            return $request->request->all('login')['_password'];
        }
        try {
            $dateString = $request->request->all('login')['_password'];
            if (str_contains($dateString, '/')) {
                $explodedDate = explode('/', $dateString);
                $dateString = $explodedDate[2].'-'.$explodedDate[1].'-'.$explodedDate[0];
            }
            $date = new \DateTime($dateString);
        } catch (\DateMalformedStringException $e) {
            throw new CustomUserMessageAuthenticationException('Date de naissance invalide');
        }

        return $date->format('d/m/Y');
    }
}
