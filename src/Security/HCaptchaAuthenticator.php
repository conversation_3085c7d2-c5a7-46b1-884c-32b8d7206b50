<?php

namespace App\Security;

use App\Form\Login\LoginTypeInterface;
use App\Security\User\AquitemUserProvider;
use MeteoConcept\HCaptchaBundle\Form\DataTransformer\HCaptchaValueFetcher;
use MeteoConcept\HCaptchaBundle\Service\HCaptchaVerifier;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

class HCaptchaAuthenticator extends AbstractLoginFormAuthenticator
{
    use PasswordFormaterTrait;
    private string $formatDateNaissanceInvalideMessage = 'La date de naissance est invalide.';

    public const string LOGIN_ROUTE = 'home_login';

    public const string HOME_LOG = 'home_vue';

    public function __construct(
        private readonly RouterInterface $router,
        private readonly UrlGeneratorInterface $urlGenerator,
        protected readonly AquitemUserProvider $userProvider,
        private readonly HCaptchaValueFetcher $hCaptchaValueFetcher,
        private readonly HCaptchaVerifier $verifier,
        private readonly LoginTypeInterface $loginType,
        #[Autowire(env: 'HCAPTCHA_SITE_KEY')]
        private readonly string $hCaptchaSiteKey,
    ) {
    }

    #[\Override]
    public function supports(Request $request): bool
    {
        return '/' === $request->getPathInfo() && $request->isMethod('POST');
    }

    #[\Override]
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        if ($exception instanceof BadCredentialsException) {
            $exception = new CustomUserMessageAuthenticationException('Identification incorrecte');
        }

        if ($request->getSession() instanceof SessionInterface) {
            $request->getSession()->set(SecurityRequestAttributes::AUTHENTICATION_ERROR, $exception);
        }

        return new RedirectResponse($this->router->generate('home_login_statut', ['statut' => 401]));
    }

    public function supportsRememberMe()
    {
        return false;
    }

    /**
     * Create a passport for the current request.
     *
     * The passport contains the user, credentials and any additional information
     * that has to be checked by the Symfony Security system. For example, a login
     * form authenticator will probably return a passport containing the user, the
     * presented password and the CSRF token value.
     *
     * You may throw any AuthenticationException in this method in case of error (e.g.
     * a UserNotFoundException when the user cannot be found).
     *
     * @throws AuthenticationException
     */
    public function authenticate(Request $request): Passport
    {
        $this->hCaptchaValueFetcher->setSiteKey($this->hCaptchaSiteKey);
        $credentials = [
            'login' => $request->request->all('login')['_username'],
            'password' => $this->formatPassword($request, $this->loginType),
            '_csrf_token' => $request->request->get('_token'),
            'captcha' => $request->get('h-captcha-response') ? $this->hCaptchaValueFetcher->reverseTransform('notuse') : null,
        ];

        $request->getSession()->set(
            SecurityRequestAttributes::LAST_USERNAME,
            $credentials['login']
        );

        if (!$credentials['login']) {
            throw new CustomUserMessageAuthenticationException('Numéro de carte obligatoire');
        }

        if (!$credentials['password']) {
            throw new CustomUserMessageAuthenticationException('Date de naissance obligatoire');
        }

        if (!\DateTime::createFromFormat('d/m/Y', $credentials['password'])) {
            throw new CustomUserMessageAuthenticationException($this->formatDateNaissanceInvalideMessage);
        }

        if (is_null($credentials['captcha'])) {
            throw new CustomUserMessageAuthenticationException('Captcha invalide');
        }

        try {
            if (!$this->verifier->verify($credentials['captcha'])) {
                throw new CustomUserMessageAuthenticationException('Captcha invalide');
            }
        } catch (\Exception) {
            throw new CustomUserMessageAuthenticationException('Captcha invalide');
        }

        return new Passport(
            new UserBadge(
                $credentials['login'],
                fn ($userIdentifier) => $this->userProvider->loadUserByUsernameAndPassword($credentials['login'], $credentials['password'])
            ),
            new CustomCredentials(
                fn () => true,
                $credentials
            ),
            [new CsrfTokenBadge('authenticate', $credentials['_csrf_token'])]
        );
    }

    /**
     * Called when authentication executed and was successful!
     *
     * This should return the Response sent back to the user, like a
     * RedirectResponse to the last page they visited.
     *
     * If you return null, the current request will continue, and the user
     * will be authenticated. This makes sense, for example, with an API.
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return new RedirectResponse($this->urlGenerator->generate(self::HOME_LOG));
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->urlGenerator->generate(self::LOGIN_ROUTE);
    }
}
