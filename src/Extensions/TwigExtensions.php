<?php

namespace App\Extensions;

use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteFormaterStrategyInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class TwigExtensions extends AbstractExtension
{
    public function __construct(
        private readonly ParameterBagInterface $params,
    ) {
    }

    public function getFunctions(): array
    {
        return array_merge(parent::getFunctions(), [
            'getParam' => new TwigFunction('getParam', $this->getParam(...)),
            'getColSize' => new TwigFunction('getColSize', $this->getColSize(...)),
            'getProgrammeSize' => new TwigFunction('getProgrammeSize', $this->getProgrammeSize(...)),
        ]);
    }

    public function getFilters(): array
    {
        return [
            new TwigFilter('cagnotte_format', $this->getFormat(...)),
        ];
    }

    public function getParam($param)
    {
        return $this->params->get($param);
    }

    public function getColSize($isMenuLateral)
    {
        $res = '';
        if ($isMenuLateral) {
            $res = 'column small-12 large-8 large-offset-4';
        }

        return $res;
    }

    public function getProgrammeSize($isMenuLateral)
    {
        $class = 'small-12 medium-6';
        if (!$isMenuLateral) {
            $class .= ' large-3';
        }

        return $class;
    }

    public function getName(): string
    {
        return 'zefid_twig_extension';
    }

    private function getFormat($flottant, CagnotteFormaterStrategyInterface $formater): string
    {
        return $formater->format($flottant);
    }
}
