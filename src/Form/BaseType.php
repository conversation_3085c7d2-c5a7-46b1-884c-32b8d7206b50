<?php

namespace App\Form;

use App\Entity\GlobalDefinitionsInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;

class BaseType extends AbstractType
{
    /**
     * Au moment de construire la vue, le label des champs est généré à l'aide du
     * nom du formulaire concaténé au nom du champs.
     *
     * (ex: utilisateur.login)
     */
    public function finishView(FormView $view, FormInterface $form, array $options): void
    {
        $children = $view->children;

        foreach ($children as $child) {
            if (null == $child->vars['label']) {
                $label = $this->getName().'.'.$child->vars['name'];
                $child->vars['label'] = $label;
            }
        }
    }

    public function getName(): string
    {
        return 'base';
    }

    /**
     * Obtenir les données d'un champ de type select à partir d'une entité GlobalDefinitions.
     *
     * @param GlobalDefinitions $globalDefinitions
     * @param string            $typeGet           le type de champ à récupérer
     * @param string            $keyGet
     * @param string            $valueGet
     */
    protected function getFromGlobalDefinitions(GlobalDefinitionsInterface $globalDefinitions, $typeGet, $keyGet, $valueGet): array
    {
        $ret = [];
        if (method_exists($globalDefinitions, 'get'.$typeGet) && $records = call_user_func([$globalDefinitions, 'get'.$typeGet])) {
            foreach ($records as $record) {
                if (method_exists($record, 'get'.$keyGet) && method_exists($record, 'get'.$valueGet)) {
                    $recordKey = call_user_func([$record, 'get'.$keyGet]);
                    $valueKey = call_user_func([$record, 'get'.$valueGet]);
                    $ret[$valueKey] = $recordKey;
                }
            }
        }

        return $ret;
    }
}
