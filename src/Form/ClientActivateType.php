<?php

namespace App\Form;

use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ClientActivateType extends BaseType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
        ->add(
            'codeCarte', TextType::class, [
                'required' => true,
                'label' => 'activation.codeCarte',
            ]
        )
        ->add(
            'codeAcces', TextType::class, [
                'required' => true,
                'label' => 'activation.codeAcces',
            ]
        )
        ->add(
            'captcha', TextType::class, [
                'required' => true,
            ]
        )
        ->add(
            'submit', SubmitType::class, [
                'label' => 'login.submit',
                'attr' => [
                    'class' => 'ralewayBold btn',
                ],
            ]
        );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            []
        );
    }

    #[\Override]
    public function getBlockPrefix(): string
    {
        return 'clientActivate';
    }
}
