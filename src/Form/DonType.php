<?php

namespace App\Form;

use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

class DonType extends BaseType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add(
            'don',
            CheckboxType::class,
            [
                'required' => true,
                'label' => 'don.faireDon',
            ]
        )
        ->add('submit', SubmitType::class, [
            'label' => 'Envoyer',
        ]);
    }
}
