<?php

namespace App\Form\Login;

use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;

class LoginWithDateType extends LoginType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);
        $builder
            ->add('_password', DateType::class, [
                'required' => true,
                'attr' => [
                    'placeholder' => 'login._password',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
        ;
    }
}
