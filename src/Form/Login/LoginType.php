<?php

namespace App\Form\Login;

use MeteoConcept\HCaptchaBundle\Form\HCaptchaType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class LoginType extends AbstractType implements LoginTypeInterface
{
    public function __construct(public $options = null)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('_username', TextType::class, [
                'required' => true,
                // 'data' => $this->options['default_login'],
                'attr' => [
                    'placeholder' => 'login._username',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('_password', TextType::class, [
                'required' => true,
                'attr' => [
                    'placeholder' => 'login._password',
                ],
                'label_attr' => [
                    'class' => 'sr-only',
                ],
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('captcha', HCaptchaType::class, [
                'label' => false,
                'attr' => [
                    'data-size' => 'compact',
                ],
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'login.submit',
                'attr' => [
                    'class' => 'btn',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            [
                'default_login' => null,
            ]
        );
    }

    #[\Override]
    public function getBlockPrefix(): string
    {
        return 'login';
    }
}
