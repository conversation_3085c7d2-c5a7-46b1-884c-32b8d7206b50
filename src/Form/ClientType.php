<?php

namespace App\Form;

use Symfony\Component\Form\Extension\Core\Type\BirthdayType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ClientType extends BaseType implements ClientTypeInterface
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('dateNaissance', BirthdayType::class, [
            'widget' => 'single_text',
            'required' => true,
        ]);

        $this->addAdresseFields($builder, $options);

        if ('create' == $options['validation_groups'][0]) {
            $listeMagasins = $options['globalDefinitions']->getMagasins()->toArray();

            $builder
                ->add(
                    'confirmation', CheckboxType::class, [
                        'required' => false,
                        'label' => 'client.confirmation',
                    ]
                )
                ->add(
                    'cgv', CheckboxType::class, [
                        'required' => false,
                        'label' => 'client.cgv',
                    ]
                )
                ->add(
                    'magasin', ChoiceType::class, [
                        'choices' => $listeMagasins,
                        'choice_label' => fn ($magasin, $key, $index) => $magasin->getLibelle(),
                        'choice_value' => function ($magasin) {
                            if ($magasin) {
                                return $magasin->getSite().'-'.$magasin->getId();
                            }

                            return null;
                        },
                        'expanded' => false,
                        'multiple' => false,
                        'required' => true,
                    ]
                )
                ->add(
                    'civilite', ChoiceType::class, [
                        'choices' => $this->getFromGlobalDefinitions($options['globalDefinitions'], 'Civilites', 'CiviliteEntier', 'Libelle'),
                        'placeholder' => '-',
                        'required' => true,
                    ]
                )
                ->add(
                    'nom', TextType::class, [
                        'required' => true,
                        'attr' => [
                            'maxlength' => 32,
                        ],
                    ]
                )
                ->add(
                    'prenom', TextType::class, [
                        'required' => true,
                        'attr' => [
                            'maxlength' => 20,
                        ],
                    ]
                )
            ;
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            [
                'globalDefinitions' => false,
                'create' => false,
                'fullForm' => true,
            ]
        );
    }

    #[\Override]
    public function getBlockPrefix(): string
    {
        return 'client';
    }

    #[\Override]
    public function getName(): string
    {
        return 'client';
    }

    protected function addAdresseFields(&$builder, $options)
    {
        $builder
            ->add(
                'numero', TextType::class, [
                    'required' => false,
                    'label' => 'client.adresseNumero',
                    'attr' => [
                        'maxlength' => 20,
                    ],
                ]
            )
            ->add(
                'voie', TextType::class, [
                    'required' => false,
                    'label' => 'client.voie',
                    'attr' => [
                        'maxlength' => 40,
                    ],
                ]
            )
            ->add(
                'escalier', TextType::class, [
                    'required' => false,
                    'label' => 'client.escalier',
                    'attr' => [
                        'maxlength' => 38,
                    ],
                ]
            )
            ->add(
                'batiment', TextType::class, [
                    'required' => false,
                    'label' => 'client.batiment',
                    'attr' => [
                        'maxlength' => 38,
                    ],
                ]
            )
            ->add(
                'lieuDit', TextType::class, [
                    'required' => false,
                    'label' => 'client.lieuDit',
                    'attr' => [
                        'maxlength' => 40,
                    ],
                ]
            )
            ->add(
                'telephoneMobile', TextType::class, [
                    'required' => true,
                    'label' => 'client.telephoneMobile',
                    'attr' => [
                        'maxlength' => 15,
                    ],
                ]
            )
            ->add(
                'email', EmailType::class, [
                    'required' => true,
                    'label' => 'client.email',
                    'attr' => [
                        'maxlength' => 90,
                    ],
                ]
            )
        ;
        $builder
            ->add(
                'codepostal', TextType::class, [
                    'required' => true,
                    'label' => 'client.codepostal',
                    'attr' => [
                        'class' => 'codepostal',
                        'maxlength' => 10,
                    ],
                ]
            )
            ->add(
                'ville', TextType::class, [
                    'required' => true,
                    'label' => 'client.ville',
                    'attr' => [
                        'maxlength' => 40,
                    ],
                ]
            )
        ;
        $pays = $this->getFromGlobalDefinitions($options['globalDefinitions'], 'PaysClients', 'code', 'libelle');
        $codePaysClientOptions = [
            'choice_translation_domain' => false,
            'choices' => $pays,
            'placeholder' => '-',
            'required' => true,
            'label' => 'client.pays',
            'attr' => [
                'class' => 'codepays',
            ],
        ];

        $builder->add('codePaysClient', ChoiceType::class, $codePaysClientOptions);
    }
}
