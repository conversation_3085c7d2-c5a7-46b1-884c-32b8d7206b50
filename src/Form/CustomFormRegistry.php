<?php

namespace App\Form;

use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\Attribute\AutowireDecorated;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Form\FormRegistryInterface;
use Symfony\Component\Form\FormTypeGuesserInterface;
use Symfony\Component\Form\ResolvedFormTypeInterface;

#[AsDecorator(decorates: 'form.registry')]
readonly class CustomFormRegistry implements FormRegistryInterface
{
    public function __construct(
        #[Autowire('@service_container')] private ContainerInterface $container,
        #[AutowireDecorated]
        private FormRegistryInterface $registry,
    ) {
    }

    public function getType(string $name): ResolvedFormTypeInterface
    {
        if ($this->container->has($name)) {
            $name = $this->container->get($name)::class;
        }

        return $this->registry->getType($name);
    }

    public function hasType(string $name): bool
    {
        return $this->registry->hasType($name);
    }

    public function getTypeGuesser(): ?FormTypeGuesserInterface
    {
        return $this->registry->getTypeGuesser();
    }

    public function getExtensions(): array
    {
        return $this->registry->getExtensions();
    }
}
