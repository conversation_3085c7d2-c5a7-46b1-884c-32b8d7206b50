<?php

namespace App\Controller;

use App\DTO\WebserviceError;
use App\Entity\ClientActivate;
use App\Entity\ClientActivate2;
use App\Entity\ClientActivateInterface;
use App\Entity\ClientInterface;
use App\Form\ClientActivate2Type;
use App\Form\ClientActivateType;
use App\Services\Captcha\CaptchaService;
use App\Services\ErrorManagementService;
use App\Services\GlobalDefinitionsServiceInterface;
use App\Services\NewClientActivateService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;

class ClientActivateController extends AbstractController
{
    public function __construct(
        protected readonly NewClientActivateService $clientActivateService,
        protected readonly ErrorManagementService $errorManagementService, private readonly NewClientActivateService $newClientActivateService,
    ) {
    }

    #[Route(
        path: '/activation',
        name: 'activate',
        condition: "service('featureChecker').hasActivation()",
    )]
    public function activate(Request $request, ParameterBagInterface $params, SessionInterface $session, CaptchaService $captchaService): Response
    {
        $statut = 200;
        if (!$params->get('activation.carte')) {
            throw $this->createAccessDeniedException('Accès interdit');
        }

        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('home_vue');
        }

        $clientActivate = new ClientActivate();
        $form = $this->createForm(ClientActivateType::class, $clientActivate);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);
            if ($captchaService->checkCaptcha($request->get('clientActivate')['captcha'])) {
                if ($form->isValid() && $this->createClient($clientActivate)) {
                    $session->set('codeCarteActivation', $clientActivate->getCodeCarte());

                    return $this->redirectToRoute('activate_client_infos');
                }
            } else {
                $this->addFlash('error', 'activation.errorMessage.captcha');
                $statut = 401;
            }
        }

        $label_captcha = $captchaService->generateCaptchaNumber();

        return new Response(
            $this->renderView(
                'client_activate/activate.html.twig',
                [
                    'label_captcha' => $label_captcha,
                    'form' => $form->createView(),
                ]
            ),
            $statut  // return code
        );
    }

    #[Route(
        path: '/activation/2',
        name: 'activate_client_infos',
        condition: "service('featureChecker').hasActivation()",
    )]
    public function activateClientInfos(Request $request, ParameterBagInterface $params, SessionInterface $session, GlobalDefinitionsServiceInterface $globalDefinitionsService): Response
    {
        if (!$params->get('activation.carte')) {
            throw $this->createAccessDeniedException('Accès interdit');
        }

        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('home_vue');
        }

        if (!$session->has('codeCarteActivation')) {
            return $this->redirectToRoute('activate');
        }

        $client = new ClientActivate2();
        $client->setId($session->get('codeCarteActivation'));
        $client->setCodeCarte($session->get('codeCarteActivation'));
        $globalDefinitions = $globalDefinitionsService->call();
        $form = $this->createForm(ClientActivate2Type::class, $client, [
            'globalDefinitions' => $globalDefinitions,
            'validation_groups' => ['create'],
        ]);

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);
            if ($form->isValid()) {
                $client->setSite($client->getMagasin()->getSite());
                if ($this->createClient($client)) {
                    $session->set('activationClientCreated', true);

                    return $this->redirectToRoute('activate_client_confirmation');
                }
            } else {
                $this->addFlash('error', 'activation.errorMessage.compte');
            }
        }

        return $this->render(
            'client_activate/activate_client_infos.html.twig',
            [
                'client' => $client,
                'form' => $form,
            ]
        );
    }

    #[Route(
        path: '/activation/3',
        name: 'activate_client_confirmation',
        condition: "service('featureChecker').hasActivation()",
    )]
    public function activateClientConfirmation(ParameterBagInterface $params, SessionInterface $session): Response
    {
        if (!$params->get('activation.carte')) {
            throw $this->createAccessDeniedException('Accès interdit');
        }

        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('home_vue');
        }

        if ($session->has('codeCarteActivation') && !$session->has('activationClientCreated')) {
            return $this->redirectToRoute('activate_client_infos');
        }

        if (!$session->has('codeCarteActivation')) {
            return $this->redirectToRoute('activate');
        }

        return $this->render('client_activate/activate_client_confirmation.html.twig');
    }

    public function createClient(ClientActivateInterface $clientActivate): bool|ClientInterface
    {
        $client = $this->newClientActivateService->create($clientActivate);
        if ($client instanceof WebserviceError) {
            $this->errorManagementService->addError($client->code);

            return false;
        }

        return $client;
    }
}
