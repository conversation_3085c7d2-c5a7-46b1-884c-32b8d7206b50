<?php

namespace App\Controller;

use App\Entity\Client;
use App\Entity\ClientInterface;
use App\Services\CartesGenerateur;
use App\Services\Cheque\ChequeSecurityFactoryInterface;
use App\Services\Cheque\ChequeType;
use App\Services\PDFCheque\ChequeDtoFactoryInterface;
use App\Services\PDFCheque\EChequeGenerateur;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class CartePDFController extends AbstractController
{
    #[Route(path: '/generateCarte', name: 'generateCarte')]
    public function generateCarte(CartesGenerateur $pdfHandling): Response
    {
        /** @var ClientInterface $client */
        $client = $this->getUser();

        $pdfHandling->genererPdf($client);

        $response = new Response();
        $response->headers->set('Content-Type', 'application/pdf');
        $response->headers->set('Content-Disposition', 'attachment; filename=carte-'.$client->getCodeCarte().'.pdf');

        $path = $pdfHandling->getPathCarte($client);
        $content = file_get_contents($path);
        $response->setContent($content);
        unlink($path);

        return $response;
    }

    #[Route(
        path: '/generateECheque/{type}',
        name: 'generateECheque',
        condition: "service('featureChecker').hasChequeFromString(params['type'])",
    )]
    public function generateECheque(
        EChequeGenerateur $pdfHandlingFid,
        ChequeType $type,
        ChequeSecurityFactoryInterface $chequeSecurityFactory,
        ChequeDtoFactoryInterface $chequeDtoFactory,
    ): Response {
        /** @var Client $client */
        $client = $this->getUser();

        $chequeSecurityChecker = $chequeSecurityFactory->build($type, $client);
        if (!$chequeSecurityChecker->isDownloadable()) {
            return $this->redirectToRoute('home_login');
        }

        $dto = $chequeDtoFactory->loadDTOForChequeFid($type, $client);
        $pdf = $pdfHandlingFid->genererPdf($dto);

        return new Response($pdf->Output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename='.$chequeSecurityChecker->getFilename(),
        ]);
    }
}
