<?php

namespace App\Controller;

use App\Form\Login\LoginTypeInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends AbstractController
{
    #[Route(path: '/', name: 'home_login')]
    public function login(LoginTypeInterface $form, AuthenticationUtils $authenticationUtils): Response
    {
        $form = $this->createForm($form::class);

        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('home_vue');
        }

        $errors = $authenticationUtils->getLastAuthenticationError();

        return $this->render(
            'security/login.html.twig',
            [
                'errors' => $errors,
                'form' => $form,
            ]
        );
    }

    #[Route(path: '/statut/{statut}', name: 'home_login_statut', defaults: ['statut' => 200])]
    public function loginStatut($statut = 200): Response
    {
        return new Response(
            $this->renderView('security/loginStatut.html.twig', []),
            $statut // return code
        );
    }

    #[Route(path: '/login_check')]
    public function login_check(): array
    {
        return [];
    }

    protected function generateCaptchaNumber($session): array
    {
        $first = random_int(1, 9);
        $second = random_int(1, 9);
        $session->set('captcha_first', $first);
        $session->set('captcha_second', $second);

        return [$first, $second];
    }
}
