<?php

namespace App\Controller;

use Alienor\RequeteurAdresseBundle\Services\AdresseApiConsumer;
use App\DTO\WebserviceError;
use App\Entity\Client;
use App\Entity\ClientInterface;
use App\Form\ClientTypeInterface;
use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteFormaterStrategyInterface;
use App\Services\Cagnotte\CagnotteStrategies\CagnotteStrategyInterface;
use App\Services\Cagnotte\CagnotteTypes\CagnotteTypeStrategyInterface;
use App\Services\Cheque\AllDownloadSecurityChecker;
use App\Services\ErrorManagementService;
use App\Services\GlobalDefinitionsServiceInterface;
use App\Services\NewClientsServiceInterface;
use App\Services\Wallet\WalletInterface;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;

class HomeController extends AbstractController
{
    public function __construct(
        protected readonly GlobalDefinitionsServiceInterface $globalDefinitionsService,
        protected readonly WalletInterface $wallet,
        protected readonly AdresseApiConsumer $addressApiConsumer,
        protected readonly ParameterBagInterface $params,
        protected readonly NewClientsServiceInterface $clientsService,
        protected readonly CagnotteStrategyInterface $cagnotteStrategy,
        protected readonly CagnotteTypeStrategyInterface $cagnotteType,
        protected readonly CagnotteFormaterStrategyInterface $cagnotteFormater,
        protected readonly ErrorManagementService $errorManagementService,
        protected readonly AllDownloadSecurityChecker $allDownloadSecurityChecker,
    ) {
    }

    #[Route(path: '/vue/', name: 'home_vue')]
    public function vue(Request $request): Response
    {
        // TODO refacto et faire en sorte de construire un tableau pour le front issue de multiples appels
        // à des services dédiés à chaque fonctionnalités avec array_merge
        $globalDefinitions = $this->globalDefinitionsService->call();

        $client = $this->getUser();

        $formCompte = $this->createForm(
            ClientTypeInterface::class,
            $client,
            [
                'globalDefinitions' => $globalDefinitions,
                'validation_groups' => ['validationEPC'],
            ]
        );

        /* @var Client $client */
        $client->getMagasin()->mergeMagasin($globalDefinitions->loadMagasin($client->getMagasin()));

        if ($request->isMethod('POST')) {
            $this->handleFormSubmission($client, $formCompte, $request);
        }

        $generator = new BarcodeGeneratorPNG();

        return $this->render(
            'home/vue.html.twig',
            [
                'client' => $client,
                'code_barre_img_content' => base64_encode($generator->getBarcode($client->getId(), $generator::TYPE_EAN_13, 2, 90)),
                'formCompte' => $formCompte->createView(),
                'pkpass' => $this->wallet->generateUrl($client),
                'globalDefinition' => $globalDefinitions,
                ...$this->cagnotteType->getCagnotteType()->toArray(),
                ...$this->cagnotteStrategy->getCagnotteData($client)->toArray(),
                'cagnotteFormater' => $this->cagnotteFormater,
                'allDownloadSecurityChecker' => $this->allDownloadSecurityChecker->listDownloadSecurityChecker(),
            ]
        );
    }

    protected function updateClient(ClientInterface $client): RedirectResponse|false
    {
        $response = $this->clientsService->update($client);
        if ($response instanceof WebserviceError) {
            $this->addFlash('error', 'client.update.error');
            $this->errorManagementService->addError($response->code);

            return false;
        }

        $this->addFlash('success', 'client.update.success');

        return $this->redirectToRoute('home_vue');
    }

    public function handleFormSubmission(?UserInterface $client, FormInterface $formCompte, Request $request): void
    {
        $prevClient = clone $client;
        $formCompte->handleRequest($request);
        if ($formCompte->isValid()) {
            // ON GEOLOCALISE L'ADRESSE DU CLIENT
            if ($this->params->get('adresse.geolocalisation') && $client->needLoadXYAddress($prevClient)) {
                $newAdresse = $this->addressApiConsumer->getAdresse(
                    [
                        'numero' => $client->getNumero(),
                        'voie' => $client->getVoie(),
                        'codePostal' => $client->getCodepostal(),
                        'ville' => $client->getVille(),
                    ]
                );
                if (isset($newAdresse['adresse'][0])) {
                    $client->setXY($newAdresse['adresse'][0]);
                }
            }

            $this->updateClient($client);
        } else {
            $this->addFlash('error', 'client.update.errorForm');
        }
    }
}
