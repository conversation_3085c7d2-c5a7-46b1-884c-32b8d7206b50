<?php

namespace App\Controller;

use App\Form\DonType;
use App\Services\DonChequeService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class DonController extends AbstractController
{
    #[Route(
        path: '/don/{magasin}/{codecarte}/{numerocheque}/{token}',
        name: 'don',
        condition: "service('featureChecker').hasDon()",
    )]
    public function don(Request $request, ParameterBagInterface $params, DonChequeService $donChequeService, string $magasin, string $codecarte, string $numerocheque, string $token): \Symfony\Component\HttpFoundation\Response
    {
        $alienor_token = $donChequeService->generateToken(
            [
                'codecarte' => $codecarte,
                'numerocheque' => $numerocheque,
                'magasin' => $magasin,
            ],
            $params->get('aquitem.doncheque.token.alienor')
        );
        if ($alienor_token !== $token) {
            // > pour test
            // return $this->redirectToRoute('don', ['magasin' => $magasin, 'codecarte' => $codecarte, 'numerocheque' => $numerocheque, 'token' => $alienor_token,]);
            // < pour test
            throw $this->createAccessDeniedException();
        }

        $formFaireDon = $this->createForm(DonType::class);
        if ($request->isMethod('POST')) {
            $formFaireDon->handleRequest($request);
            if ($formFaireDon->isValid()) {
                $donCheque = $donChequeService->create(
                    null,
                    $magasin,
                    $codecarte,
                    $numerocheque
                );
                try {
                    if ('OK' == $donCheque->etat) {
                        return $this->render('don/don_final.html.twig', [
                            'alreadyBox' => false,
                        ]);
                    }

                    if ('-20305' == $donCheque->exception['finalExceptionCode']) {
                        return $this->render('don/don_final.html.twig', [
                            'alreadyBox' => true,
                        ]);
                    }

                    return $this->render('don/don_error.html.twig', []);
                } catch (\Exception) {
                    return $this->render('don/don_error.html.twig', []);
                }
            }
        }

        return $this->render('don/don_confirmation.html.twig', [
            'form' => $formFaireDon,
        ]);
    }
}
