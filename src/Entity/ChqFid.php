<?php

namespace App\Entity;

/**
 * Description of ChqFid.
 *
 * <AUTHOR>
 */
class ChqFid
{
    /**
     * @var \DateTime
     */
    private $dateEmis;

    /**
     * @var float
     */
    private $montant;

    /**
     * @var int
     */
    private $codePrepa;

    /**
     * @var int
     */
    private $numChq;

    /**
     * @var \DateTime
     */
    private $dateFinValidite;

    /**
     * @var \DateTime
     */
    private $dateEncais;

    /**
     * @var int
     */
    private $encaisSite;

    /**
     * @var string
     */
    private $encaisMagasin;

    /**
     * @var int
     */
    private $encaisCodeClient;

    /**
     * @var float
     */
    private $encaisMontant;

    /**
     * @var string
     */
    private $xType;

    public function getDateEmis()
    {
        return $this->dateEmis;
    }

    public function getMontant()
    {
        return $this->montant;
    }

    public function getCodePrepa()
    {
        return $this->codePrepa;
    }

    public function getNumChq()
    {
        return $this->numChq;
    }

    public function getDateFinValidite()
    {
        return $this->dateFinValidite;
    }

    public function getDateEncais()
    {
        return $this->dateEncais;
    }

    public function getEncaisSite()
    {
        return $this->encaisSite;
    }

    public function getEncaisMagasin()
    {
        return $this->encaisMagasin;
    }

    public function getEncaisCodeClient()
    {
        return $this->encaisCodeClient;
    }

    public function getEncaisMontant()
    {
        return $this->encaisMontant;
    }

    public function getXType()
    {
        return $this->xType;
    }

    public function setDateEmis($dateEmis)
    {
        $this->dateEmis = $dateEmis;

        return $this;
    }

    public function setMontant($montant)
    {
        $this->montant = $montant;

        return $this;
    }

    public function setCodePrepa($codePrepa)
    {
        $this->codePrepa = $codePrepa;

        return $this;
    }

    public function setNumChq($numChq)
    {
        $this->numChq = $numChq;

        return $this;
    }

    public function setDateFinValidite($dateFinValidite)
    {
        $this->dateFinValidite = $dateFinValidite;

        return $this;
    }

    public function setDateEncais($dateEncais)
    {
        $this->dateEncais = $dateEncais;

        return $this;
    }

    public function setEncaisSite($encaisSite)
    {
        $this->encaisSite = $encaisSite;

        return $this;
    }

    public function setEncaisMagasin($encaisMagasin)
    {
        $this->encaisMagasin = $encaisMagasin;

        return $this;
    }

    public function setEncaisCodeClient($encaisCodeClient)
    {
        $this->encaisCodeClient = $encaisCodeClient;

        return $this;
    }

    public function setEncaisMontant($encaisMontant)
    {
        $this->encaisMontant = $encaisMontant;

        return $this;
    }

    public function setXType($xType)
    {
        $this->xType = $xType;

        return $this;
    }
}
