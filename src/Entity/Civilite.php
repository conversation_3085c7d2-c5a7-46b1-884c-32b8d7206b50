<?php

namespace App\Entity;

class Civilite
{
    private ?string $id = null;

    private ?string $libelle = null;

    private ?string $civiliteEntier = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): void
    {
        $this->libelle = $libelle;
    }

    public function getCiviliteEntier(): ?string
    {
        return $this->civiliteEntier;
    }

    public function setCiviliteEntier(?string $civiliteEntier): void
    {
        $this->civiliteEntier = $civiliteEntier;
    }
}
