<?php

namespace App\Entity;

/**
 * JournalPoint.
 */
class JournalPoint
{
    /**
     * @var string
     */
    private $numeroTicket;

    /*
     * @var \Datetime
     */
    private $dateVente;

    /*
     * @var \Datetime
     */
    private $dateOperation;

    /*
     * @var string
     */
    private $operation;

    /*
     * @var string
     */
    private $libelleOperation;

    /*
     * @var float
     */
    private $nbPoints;

    /*
     * @var float
     */
    private $montant;

    /*
     * @var string
     */
    private $siteDemande;

    /*
    * @var string
    */
    private $magasinDemande;

    public function getNumeroTicket()
    {
        return $this->numeroTicket;
    }

    public function setNumeroTicket($numeroTicket)
    {
        $this->numeroTicket = $numeroTicket;

        return $this;
    }

    public function setDateOperation($dateOperation)
    {
        $this->dateOperation = $dateOperation;

        return $this;
    }

    public function getDateOperation()
    {
        return $this->dateOperation;
    }

    public function setDateVente($dateVente)
    {
        $this->dateVente = $dateVente;

        return $this;
    }

    public function getDateVente()
    {
        return $this->dateVente;
    }

    public function setLibelleOperation($libelleOperation)
    {
        $this->libelleOperation = $libelleOperation;

        return $this;
    }

    public function getLibelleOperation()
    {
        return $this->libelleOperation;
    }

    public function setMagasinDemande($magasinDemande)
    {
        $this->magasinDemande = $magasinDemande;

        return $this;
    }

    public function getMagasinDemande()
    {
        return $this->magasinDemande;
    }

    public function setMontant($montant)
    {
        $this->montant = $montant;

        return $this;
    }

    public function getMontant()
    {
        return $this->montant;
    }

    public function setNbPoints($nbPoints)
    {
        $this->nbPoints = $nbPoints;

        return $this;
    }

    public function getNbPoints()
    {
        return $this->nbPoints;
    }

    public function setOperation($operation)
    {
        $this->operation = $operation;

        return $this;
    }

    public function getOperation()
    {
        return $this->operation;
    }

    public function setSiteDemande($siteDemande)
    {
        $this->siteDemande = $siteDemande;

        return $this;
    }

    public function getSiteDemande()
    {
        return $this->siteDemande;
    }
}
