<?php

namespace App\Entity;

/**
 * ClientActivate.
 */
class ClientActivate implements ClientActivateInterface
{
    /**
     * @var string
     */
    protected $codeCarte;

    /**
     * @var string
     */
    protected $codeAcces;

    /**
     * @var string
     */
    protected $programme;

    /**
     * @var int
     */
    protected $captcha;

    /**
     * Get codeCarte.
     *
     * @return string
     */
    public function getCodeCarte()
    {
        return $this->codeCarte;
    }

    /**
     * Set codeCarte.
     *
     * @param string $codeCarte
     *
     * @return ClientActivate
     */
    public function setCodeCarte($codeCarte)
    {
        $this->codeCarte = $codeCarte;

        return $this;
    }

    /**
     * Get codeAcces.
     *
     * @return string
     */
    public function getCodeAcces()
    {
        return $this->codeAcces;
    }

    /**
     * Set codeAcces.
     *
     * @param string $codeAcces
     *
     * @return ClientActivate
     */
    public function setCodeAcces($codeAcces)
    {
        $this->codeAcces = $codeAcces;

        return $this;
    }

    /**
     * Get programme.
     *
     * @return string
     */
    public function getProgramme()
    {
        return $this->programme;
    }

    /**
     * Set programme.
     *
     * @param string $programme
     *
     * @return ClientActivate
     */
    public function setProgramme($programme)
    {
        $this->programme = $programme;

        return $this;
    }

    /**
     * Get captcha.
     *
     * @return string
     */
    public function getCaptcha()
    {
        return $this->captcha;
    }

    /**
     * Set captcha.
     *
     * @param string $captcha
     *
     * @return ClientActivate
     */
    public function setCaptcha($captcha)
    {
        $this->captcha = $captcha;

        return $this;
    }
}
