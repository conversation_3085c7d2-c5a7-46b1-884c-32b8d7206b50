<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class GlobalDefinitions implements GlobalDefinitionsInterface
{
    public const CODE_PAYS_FR = 'FRA';
    public const LIBELLE_PAYS_FR = 'FRANCE';

    /** @var ArrayCollection<Magasin> */
    protected ArrayCollection $magasins;

    /** @var ArrayCollection<Genre> */
    private ArrayCollection $genres;

    /** @var ArrayCollection<Civilite> */
    private ArrayCollection $civilites;

    /** @var ArrayCollection<PaysClient> */
    private ArrayCollection $paysClients;

    /** @var ArrayCollection<SituationFamiliale> */
    private ArrayCollection $situationsFamiliales;

    /** @var ArrayCollection<CategorieSocioPro> */
    private ArrayCollection $categoriesSocioPros;

    /** @var ArrayCollection<Param> */
    private ArrayCollection $params;

    public function __construct()
    {
        $this->magasins = new ArrayCollection();
        $this->genres = new ArrayCollection();
        $this->civilites = new ArrayCollection();
        $this->paysClients = new ArrayCollection();
        $this->situationsFamiliales = new ArrayCollection();
        $this->categoriesSocioPros = new ArrayCollection();
        $this->params = new ArrayCollection();
    }

    public function getParamById($id): ?Param
    {
        foreach ($this->getParams() as $param) {
            if ($param->getId() == $id) {
                return $param;
            }
        }

        return null;
    }

    public function addMagasin(Magasin $magasin): void
    {
        $this->magasins->add($magasin);
    }

    public function removeMagasin(Magasin $magasin): void
    {
        $this->magasins->removeElement($magasin);
    }

    /**
     * @return ArrayCollection<Magasin>
     */
    public function getMagasins(): Collection
    {
        return $this->magasins;
    }

    public function loadMagasin($magasin): ?Magasin
    {
        foreach ($this->magasins as $mag) {
            if ($mag->getId() == $magasin->getId() && $mag->getSite() == $magasin->getSite()) {
                return $mag;
            }
        }

        return null;
    }

    public function addGenre(Genre $genre): void
    {
        $this->genres->add($genre);
    }

    public function removeGenre(Genre $genre)
    {
        $this->genres->removeElement($genre);
    }

    /**
     * Get genres.
     *
     * @return ArrayCollection<Genre>
     */
    public function getGenres()
    {
        return $this->genres;
    }

    public function addCivilite(Civilite $civilite): void
    {
        $this->civilites->add($civilite);
    }

    public function removeCivilite(Civilite $civilite): void
    {
        $this->civilites->removeElement($civilite);
    }

    /**
     * @return ArrayCollection<Civilite>
     */
    public function getCivilites(): Collection
    {
        return $this->civilites;
    }

    public function addPaysClient(PaysClient $paysClient): void
    {
        $this->paysClients->add($paysClient);
    }

    public function removePaysClient(PaysClient $paysClient): void
    {
        $this->paysClients->removeElement($paysClient);
    }

    /**
     * @return ArrayCollection<PaysClient>
     */
    public function getPaysClients(): Collection
    {
        return $this->paysClients;
    }

    public function addSituationFamiliale(SituationFamiliale $situationFamiliale): void
    {
        $this->situationsFamiliales->add($situationFamiliale);
    }

    /**
     * @return ArrayCollection<SituationFamiliale>
     */
    public function getSituationsFamiliales(): Collection
    {
        return $this->situationsFamiliales;
    }

    public function addCategorieSocioPro(CategorieSocioPro $categorieSocioPro): void
    {
        $this->categoriesSocioPros->add($categorieSocioPro);
    }

    /**
     * @return ArrayCollection<CategorieSocioPro>
     */
    public function getCategoriesSocioPros(): Collection
    {
        return $this->categoriesSocioPros;
    }

    public function getCategorieSocioProById($id): ?CategorieSocioPro
    {
        if ($this->categoriesSocioPros->count() > 0) {
            foreach ($this->categoriesSocioPros as $categorieSocioPro) {
                if ($categorieSocioPro->getId() == $id) {
                    return $categorieSocioPro;
                }
            }
        }

        return null;
    }

    public function addParam(Param $param): void
    {
        $this->params->add($param);
    }

    public function removeParam(Param $param): void
    {
        $this->params->removeElement($param);
    }

    /**
     * @return ArrayCollection<Param>
     */
    public function getParams(): Collection
    {
        return $this->params;
    }
}
