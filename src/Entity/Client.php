<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class Client implements UserInterface, PasswordAuthenticatedUserInterface, ClientInterface
{
    protected ?string $codeCarte = null;

    protected ?int $codeCivilite = null;

    protected ?\DateTime $dateCreation = null;

    protected string $codePaysClient = GlobalDefinitions::CODE_PAYS_FR;

    protected $envoieSmsInterne = false;

    protected $envoieEmailInterne = false;

    protected $envoieSmsExterne = false;

    protected $envoieEmailExterne = false;

    protected $envoiCourrierInterne = false;

    protected $envoiCourrierExterne = false;

    protected ?string $codeLangue = null;

    protected ?float $nbrePoints = null;

    protected ?\DateTime $dateSuppression = null;

    protected ?Magasin $magasin = null;

    protected ?string $magasinLibelle = null;

    protected ?float $montantCarte = null;

    /**
     * @var ArrayCollection<MouvementPoint>
     */
    private readonly ArrayCollection $mouvementPoints;

    private readonly bool $isActive;

    /* --------------------------------------------------------------- */

    protected $id;

    protected ?string $civilite = null;

    protected ?string $nom = null;

    protected ?string $prenom = null;

    protected ?\DateTime $dateNaissance;

    protected ?string $voie = null;
    protected ?string $lieuDit = null;
    protected ?string $batiment = null;
    protected ?string $escalier = null;
    protected string|int|null $numero = null;
    protected ?string $ville = null;
    protected ?string $codepostal = null;

    protected ?string $paysLibelle = null;

    protected ?float $x = null;
    protected ?float $y = null;

    protected ?bool $npai = null;

    protected ?bool $npaiEmail = null;

    protected ?bool $npaiSms = null;

    protected ?string $telephoneFixe = null;
    protected ?string $telephoneMobile = null;
    protected ?string $indicatifFixe = null;
    protected ?string $indicatifMobile = null;

    protected ?string $email = null;

    protected ?string $situationFamiliale = null;

    protected ?string $csp = null;

    protected ?int $nbreEnfants = null;

    /***
     * Cheque
     */
    protected ?float $nbrePtsRestantCheque = null;
    protected ?\DateTime $dateDernierCheque = null;
    protected ?float $montantDernierCheque = null;
    protected ?float $pourcentageDernierCheque = null;

    protected ?string $numDernierCheque = null;
    protected ?\DateTime $dateFinValiditeDernierCheque = null;
    protected ?\DateTime $dateEncaissementDernierCheque = null;

    /***
     * Cheque Anniversaire
     */
    protected ?\DateTime $dateDernierChequeAnniv = null;
    protected ?float $montantDernierChequeAnniv = null;
    protected ?float $pourcentageDernierChequeAnniv = null;
    protected ?string $numDernierChequeAnniv = null;
    protected ?\DateTime $dateFinValiditeDernierChequeAnniv = null;
    protected ?\DateTime $dateEncaissementDernierChequeAnniv = null;

    /***
     * Cheque Bienvenue
     */
    protected ?\DateTime $dateDernierChequeBvnu = null;
    protected ?float $montantDernierChequeBvnu = null;
    protected ?float $pourcentageDernierChequeBvnu = null;
    protected ?string $numDernierChequeBvnu = null;
    protected ?\DateTime $dateFinValiditeDernierChequeBvnu = null;
    protected ?\DateTime $dateDebutValiditeDernierChequeBvnu = null;
    protected ?\DateTime $dateEncasDernierChequeBvnu = null;

    protected ?string $magasinId = null;

    /**
     * @Assert\Valid
     *
     * @var Collection<Enfant>
     */
    protected Collection $enfants;

    protected Collection $dernierChequeFidelites;
    private ?string $site = null;

    public function __construct()
    {
        $this->mouvementPoints = new ArrayCollection();
        $this->isActive = true;

        $this->dernierChequeFidelites = new ArrayCollection();
        $this->enfants = new ArrayCollection();
        $this->dateCreation = new \DateTime();
        $this->envoiCourrierInterne = true;
    }

    public function getMouvementPoints(): Collection
    {
        return $this->mouvementPoints;
    }

    public function addMouvementPoint(MouvementPoint $mouvementPoint): void
    {
        $this->mouvementPoints->add($mouvementPoint);
    }

    public function removeMouvementPoint(MouvementPoint $mouvementPoint): void
    {
        $this->mouvementPoints->removeElement($mouvementPoint);
    }

    public function setMagasin(Magasin $magasin): void
    {
        $this->magasin = $magasin;
    }

    public function getMagasin(): Magasin
    {
        return $this->magasin;
    }

    public function setDateSuppression(\DateTime $dateSuppression): void
    {
        $this->dateSuppression = $dateSuppression;
    }

    public function getDateSuppression(): ?\DateTime
    {
        return $this->dateSuppression;
    }

    public function setDateDernierChequeBvnu(\DateTime $dateDernierChequeBvnu): void
    {
        $this->dateDernierChequeBvnu = $dateDernierChequeBvnu;
    }

    public function getDateDernierChequeBvnu(): ?\DateTime
    {
        return $this->dateDernierChequeBvnu;
    }

    public function setMontantDernierChequeBvnu(string|float $montantDernierChequeBvnu): void
    {
        $this->montantDernierChequeBvnu = $this->parseFloat($montantDernierChequeBvnu);
    }

    public function getMontantDernierChequeBvnu(): ?int
    {
        return $this->montantDernierChequeBvnu;
    }

    public function setPourcentageDernierChequeBvnu(string|float $pourcentageDernierChequeBvnu): void
    {
        $this->pourcentageDernierChequeBvnu = $this->parseFloat($pourcentageDernierChequeBvnu);
    }

    public function getPourcentageDernierChequeBvnu(): ?int
    {
        return $this->pourcentageDernierChequeBvnu;
    }

    public function setNumDernierChequeBvnu(?string $numDernierChequeBvnu): void
    {
        $this->numDernierChequeBvnu = $numDernierChequeBvnu;
    }

    public function getNumDernierChequeBvnu(): ?string
    {
        return $this->numDernierChequeBvnu;
    }

    public function setDateFinValiditeDernierChequeBvnu(\DateTime $dateFinValiditeDernierChequeBvnu): void
    {
        $this->dateFinValiditeDernierChequeBvnu = $dateFinValiditeDernierChequeBvnu;
    }

    public function getDateDebutValiditeDernierChequeBvnu(): ?\DateTime
    {
        return $this->dateDebutValiditeDernierChequeBvnu;
    }

    public function setDateDebutValiditeDernierChequeBvnu(\DateTime $dateDebutValiditeDernierChequeBvnu): void
    {
        $this->dateDebutValiditeDernierChequeBvnu = $dateDebutValiditeDernierChequeBvnu;
    }

    public function getDateFinValiditeDernierChequeBvnu(): ?\DateTime
    {
        return $this->dateFinValiditeDernierChequeBvnu;
    }

    public function setDateEncasDernierChequeBvnu(\DateTime $dateEncasDernierChequeBvnu): void
    {
        $this->dateEncasDernierChequeBvnu = $dateEncasDernierChequeBvnu;
    }

    public function getDateEncasDernierChequeBvnu(): ?\DateTime
    {
        return $this->dateEncasDernierChequeBvnu;
    }

    public function getSexe(): bool
    {
        return 3 === $this->codeCivilite;
    }

    public function getMagasinLibelle(): ?string
    {
        return $this->magasinLibelle;
    }

    public function getCodeCivilite(): ?int
    {
        return $this->codeCivilite;
    }

    public function getIndicatifFixe(): ?string
    {
        return $this->indicatifFixe;
    }

    public function getIndicatifMobile(): ?string
    {
        return $this->indicatifMobile;
    }

    public function getDateCreation(): ?\DateTime
    {
        return $this->dateCreation;
    }

    public function getCodePaysClient(): ?string
    {
        return $this->codePaysClient;
    }

    public function getEnvoieSmsInterne(): bool
    {
        return $this->envoieSmsInterne;
    }

    public function getEnvoieEmailInterne(): bool
    {
        return $this->envoieEmailInterne;
    }

    public function getEnvoieSmsExterne(): bool
    {
        return $this->envoieSmsExterne;
    }

    public function getEnvoieEmailExterne(): bool
    {
        return $this->envoieEmailExterne;
    }

    public function getEnvoiCourrierInterne(): bool
    {
        return $this->envoiCourrierInterne;
    }

    public function getEnvoiCourrierExterne(): bool
    {
        return $this->envoiCourrierExterne;
    }

    public function getCodeLangue(): ?int
    {
        return $this->codeLangue;
    }

    public function getPaysLibelle(): ?string
    {
        return $this->paysLibelle;
    }

    public function setMagasinLibelle(string $magasinLibelle): void
    {
        $this->magasinLibelle = $magasinLibelle;
    }

    public function setCodeCivilite(int $codeCivilite): void
    {
        $this->codeCivilite = $codeCivilite;
    }

    public function setIndicatifFixe(string $indicatifFixe): void
    {
        $this->indicatifFixe = $indicatifFixe;
    }

    public function setIndicatifMobile(string $indicatifMobile): void
    {
        $this->indicatifMobile = $indicatifMobile;
    }

    public function setDateCreation(\DateTime $dateCreation): void
    {
        $this->dateCreation = $dateCreation;
    }

    public function setCodePaysClient(string $codePaysClient): void
    {
        $this->codePaysClient = $codePaysClient;
    }

    public function setEnvoieSmsInterne(int|string|bool $envoieSmsInterne): void
    {
        $this->envoieSmsInterne = (bool) $envoieSmsInterne;
    }

    public function setEnvoieEmailInterne(int|string|bool $envoieEmailInterne): void
    {
        $this->envoieEmailInterne = (bool) $envoieEmailInterne;
    }

    public function setEnvoieSmsExterne(int|string|bool $envoieSmsExterne): void
    {
        $this->envoieSmsExterne = (bool) $envoieSmsExterne;
    }

    public function setEnvoieEmailExterne(int|string|bool $envoieEmailExterne): void
    {
        $this->envoieEmailExterne = (bool) $envoieEmailExterne;
    }

    public function setEnvoiCourrierInterne(int|string|bool $envoiCourrierInterne): void
    {
        $this->envoiCourrierInterne = (bool) $envoiCourrierInterne;
    }

    public function setEnvoiCourrierExterne(int|string|bool $envoiCourrierExterne): void
    {
        $this->envoiCourrierExterne = (bool) $envoiCourrierExterne;
    }

    public function setCodeLangue(string $codeLangue): void
    {
        $this->codeLangue = $codeLangue;
    }

    public function setPaysLibelle(string $paysLibelle): void
    {
        $this->paysLibelle = $paysLibelle;
    }

    public function getX(): ?float
    {
        return $this->x;
    }

    public function setX(string|float $x): void
    {
        $this->x = $this->parseFloat($x);
    }

    public function getY(): ?float
    {
        return $this->y;
    }

    public function setY(string|float $y): void
    {
        $this->y = $this->parseFloat($y);
    }

    public function setXY(array $adresse): void
    {
        if ($adresse['coordonnees']) {
            $coord = explode(',', (string) $adresse['coordonnees']);
            $this->y = $coord[0];
            $this->x = $coord[1];
        }
    }

    public function setNbrePoints(float|string $nbrePoints): void
    {
        $this->nbrePoints = $this->parseFloat($nbrePoints);
    }

    public function getNbrePoints(): ?float
    {
        return $this->nbrePoints;
    }

    public function setNbrePtsRestantCheque(string|float $nbrePtsRestantCheque): void
    {
        $this->nbrePtsRestantCheque = $this->parseFloat($nbrePtsRestantCheque);
    }

    public function getNbrePtsRestantCheque(): ?float
    {
        return $this->nbrePtsRestantCheque;
    }

    public function getId(): ?string
    {
        return $this->id ?? $this->codeCarte;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getUserIdentifier(): string
    {
        return $this->codeCarte;
    }

    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getSalt(): ?string
    {
        // you *may* need a real salt depending on your encoder
        // see section on salt below
        return null;
    }

    public function getPassword(): ?string
    {
        if ($this->dateNaissance) {
            return $this->dateNaissance->format('d/m/Y');
        }

        return $this->codepostal;
    }

    public function getRoles(): array
    {
        return ['ROLE_USER'];
    }

    public function eraseCredentials(): void
    {
    }

    /**
     * Validation mail ou mobile existe.
     */
    public function valideMailOrMobile(ExecutionContextInterface $context): void
    {
        if (($this->getEnvoieEmailInterne() && '' == $this->getEmail()) || ($this->getEnvoieEmailExterne() && '' == $this->getEmail())) {
            $context->buildViolation('client.errorClientsEmail')->atPath('email')->addViolation();
        }

        if (($this->getEnvoieSmsInterne() && '' == $this->getTelephoneMobile()) || ($this->getEnvoieSmsExterne() && '' == $this->getTelephoneMobile())) {
            $context->buildViolation('client.errorClientsSms')->atPath('telephoneMobile')->addViolation();
        }

        //        if ($this->getTelephoneMobile() == '' && $this->getEmail() == '') {
        //            $context->buildViolation('client.errorClientsMailOrSMS')->atPath('telephoneMobile')->addViolation();
        //        }
    }

    public function needLoadXYAddress($prevClient): bool
    {
        if (GlobalDefinitions::CODE_PAYS_FR != $this->getCodePaysClient()) {
            $this->setX(null);
            $this->setY(null);

            return false;
        }

        if ($prevClient->getCodepostal() != $this->getCodepostal()
            || $prevClient->getVoie() != $this->getVoie()
            || $prevClient->getNumero() != $this->getNumero()
            || $prevClient->getVille() != $this->getVille()
        ) {
            $this->setX(null);
            $this->setY(null);

            return true;
        }

        return false;
    }

    public function isEqualTo(UserInterface $client): bool
    {
        return $client->getCodeCarte() == $this->getCodeCarte();
    }

    /** ------------------------------------------------------------------------------------- */
    public function getDernierChequeFidelites(): Collection
    {
        return $this->dernierChequeFidelites;
    }

    public function addDernierChequeFidelite(DernierChequeFidelite $dernierChequeFidelite): void
    {
        $this->dernierChequeFidelites->add($dernierChequeFidelite);
    }

    public function setDernierChequeFidelites(ArrayCollection $dernierChequeFidelites): void
    {
        $this->dernierChequeFidelites = $dernierChequeFidelites;
    }

    public function setCodeCarte(?string $codeCarte = null): void
    {
        $this->codeCarte = $codeCarte;
    }

    public function getCodeCarte(): ?string
    {
        return $this->codeCarte;
    }

    public function setSite(string $site): void
    {
        $this->site = $site;
    }

    public function getSite(): ?string
    {
        return $this->site;
    }

    public function setCivilite(string $civilite): void
    {
        $this->civilite = $civilite;
    }

    public function getCivilite(): ?string
    {
        return $this->civilite;
    }

    public function setNom(string $nom): void
    {
        $this->nom = $nom;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setPrenom(string $prenom): void
    {
        $this->prenom = $prenom;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setCodepostal(string $codepostal): void
    {
        $this->codepostal = $codepostal;
    }

    public function getCodepostal(): ?string
    {
        return $this->codepostal;
    }

    public function setVoie(string $voie): void
    {
        $this->voie = $voie;
    }

    public function getVoie(): ?string
    {
        return $this->voie;
    }

    public function setLieuDit(string $lieuDit): void
    {
        $this->lieuDit = $lieuDit;
    }

    public function getLieuDit(): ?string
    {
        return $this->lieuDit;
    }

    public function setBatiment(string $batiment): void
    {
        $this->batiment = $batiment;
    }

    public function getBatiment(): ?string
    {
        return $this->batiment;
    }

    public function setEscalier(string $escalier): void
    {
        $this->escalier = $escalier;
    }

    public function getEscalier(): ?string
    {
        return $this->escalier;
    }

    public function setNumero(string $numero): void
    {
        $this->numero = $numero;
    }

    public function getNumero(): ?string
    {
        return $this->numero;
    }

    public function setVille(string $ville): void
    {
        $this->ville = $ville;
    }

    public function getVille(): ?string
    {
        return $this->ville;
    }

    public function setDateNaissance(\DateTime $dateNaissance): void
    {
        $this->dateNaissance = $dateNaissance;
    }

    public function getDateNaissance(): ?\DateTime
    {
        return $this->dateNaissance;
    }

    public function setNpai(bool $npai): void
    {
        $this->npai = $npai;
    }

    public function getNpai(): ?bool
    {
        return $this->npai;
    }

    public function setNpaiEmail(bool $npaiEmail): void
    {
        $this->npaiEmail = $npaiEmail;
    }

    public function getNpaiEmail(): ?bool
    {
        return $this->npaiEmail;
    }

    public function setNpaiSms(bool $npaiSms): void
    {
        $this->npaiSms = $npaiSms;
    }

    public function getNpaiSms(): ?bool
    {
        return $this->npaiSms;
    }

    public function setTelephoneFixe(string $telephoneFixe): void
    {
        $this->telephoneFixe = $telephoneFixe;
    }

    public function getTelephoneFixe(): ?string
    {
        return $this->telephoneFixe;
    }

    public function setTelephoneMobile(string $telephoneMobile): void
    {
        $this->telephoneMobile = $telephoneMobile;
    }

    public function getTelephoneMobile(): ?string
    {
        return $this->telephoneMobile;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setSituationFamiliale(string $situationFamiliale): void
    {
        $this->situationFamiliale = $situationFamiliale;
    }

    public function getSituationFamiliale(): ?string
    {
        return $this->situationFamiliale;
    }

    public function setCsp(string $csp): void
    {
        $this->csp = $csp;
    }

    public function getCsp(): ?string
    {
        return $this->csp;
    }

    public function setNbreEnfants(int $nbreEnfants): void
    {
        $this->nbreEnfants = $nbreEnfants;
    }

    public function getNbreEnfants(): ?int
    {
        if (!$this->getEnfants()) {
            return null;
        }

        return $this->getEnfants()->count();
    }

    public function setDateDernierCheque(\DateTime $dateDernierCheque): void
    {
        $this->dateDernierCheque = $dateDernierCheque;
    }

    public function getDateDernierCheque(): ?\DateTime
    {
        return $this->dateDernierCheque;
    }

    public function setMontantDernierCheque(float|string $montantDernierCheque): void
    {
        $this->montantDernierCheque = $this->parseFloat($montantDernierCheque);
    }

    public function getMontantDernierCheque(): ?float
    {
        return $this->montantDernierCheque;
    }

    public function getPourcentageDernierCheque(): ?float
    {
        return $this->pourcentageDernierCheque;
    }

    public function setPourcentageDernierCheque(float|string $pourcentageDernierCheque): void
    {
        $this->pourcentageDernierCheque = $this->parseFloat($pourcentageDernierCheque);
    }

    public function getNumDernierCheque(): ?string
    {
        return $this->numDernierCheque;
    }

    public function setNumDernierCheque(?string $numDernierCheque): void
    {
        $this->numDernierCheque = $numDernierCheque;
    }

    public function setDateFinValiditeDernierCheque(\DateTime $dateFinValiditeDernierCheque): void
    {
        $this->dateFinValiditeDernierCheque = $dateFinValiditeDernierCheque;
    }

    public function getDateFinValiditeDernierCheque(): ?\DateTime
    {
        return $this->dateFinValiditeDernierCheque;
    }

    public function getDateEncaissementDernierCheque(): ?\DateTime
    {
        return $this->dateEncaissementDernierCheque;
    }

    public function setDateEncaissementDernierCheque(\DateTime $dateEncaissementDernierCheque): void
    {
        $this->dateEncaissementDernierCheque = $dateEncaissementDernierCheque;
    }

    public function setDateDernierChequeAnniv(\DateTime $dateDernierChequeAnniv): void
    {
        $this->dateDernierChequeAnniv = $dateDernierChequeAnniv;
    }

    public function getDateDernierChequeAnniv(): ?\DateTime
    {
        return $this->dateDernierChequeAnniv;
    }

    public function setMontantDernierChequeAnniv(float|string $montantDernierChequeAnniv): void
    {
        $this->montantDernierChequeAnniv = $this->parseFloat($montantDernierChequeAnniv);
    }

    public function getMontantDernierChequeAnniv(): ?float
    {
        return $this->montantDernierChequeAnniv;
    }

    public function setPourcentageDernierChequeAnniv(float $pourcentageDernierChequeAnniv): void
    {
        $this->pourcentageDernierChequeAnniv = $pourcentageDernierChequeAnniv;
    }

    public function getPourcentageDernierChequeAnniv(): ?float
    {
        return $this->pourcentageDernierChequeAnniv;
    }

    public function setNumDernierChequeAnniv(string $numDernierChequeAnniv): void
    {
        $this->numDernierChequeAnniv = $numDernierChequeAnniv;
    }

    public function getNumDernierChequeAnniv(): ?string
    {
        return $this->numDernierChequeAnniv;
    }

    public function setDateFinValiditeDernierChequeAnniv(\DateTime $dateFinValiditeDernierChequeAnniv): void
    {
        $this->dateFinValiditeDernierChequeAnniv = $dateFinValiditeDernierChequeAnniv;
    }

    public function getDateFinValiditeDernierChequeAnniv(): ?\DateTime
    {
        return $this->dateFinValiditeDernierChequeAnniv;
    }

    public function setDateEncaissementDernierChequeAnniv(\DateTime $dateEncaissementDernierChequeAnniv): void
    {
        $this->dateEncaissementDernierChequeAnniv = $dateEncaissementDernierChequeAnniv;
    }

    public function getDateEncaissementDernierChequeAnniv(): ?\DateTime
    {
        return $this->dateEncaissementDernierChequeAnniv;
    }

    public function setMagasinId(string $magasinId): void
    {
        $this->magasinId = $magasinId;
    }

    public function getMagasinId(): ?string
    {
        return $this->magasinId;
    }

    public function addEnfant(Enfant $enfant): void
    {
        $this->enfants->add($enfant);
    }

    public function getEnfants(): Collection
    {
        return $this->enfants;
    }

    public function isZZZ(): bool
    {
        return 'ZZZ' == $this->nom;
    }

    public function isDelete(): bool
    {
        return null !== $this->dateSuppression;
    }

    public function isNpai(): bool
    {
        return 0 != $this->getNpai();
    }

    public function toArray($showAddress = false): array
    {
        $client = [
            'id' => $this->id,
            'nom' => $this->nom,
            'prenom' => $this->prenom,
            'magasin' => $this->magasin,
            'isZZZ' => $this->isZZZ(),
            'isDelete' => $this->isDelete(),
            'isNpai' => $this->isNpai(),
            'showAddress' => $showAddress,
        ];
        $address = [];
        if (true === $showAddress) {
            $address = [
                'numero' => $this->numero,
                'voie' => $this->voie,
                'escalier' => $this->escalier,
                'batiment' => $this->batiment,
                'lieuDit' => $this->lieuDit,
                'codepostal' => $this->codepostal,
                'ville' => $this->ville,
            ];
        }

        return array_merge($client, $address);
    }

    /**
     * La date de naissance ne peut être supérieure à la date du jour.
     */
    public function valideDateNaissance(ExecutionContextInterface $context): void
    {
        $current = new \DateTime();
        if ($this->getDateNaissance() > $current) {
            $context->buildViolation('client.errorDateNaissance')->atPath('dateNaissance')->addViolation();
        }
    }

    /**
     * Validation du format du code postal pour la france.
     */
    public function valideCodePostalFr(ExecutionContextInterface $context): void
    {
        if (GlobalDefinitions::CODE_PAYS_FR == $this->getCodePaysClient() && 1 !== preg_match('/^[\d]{5}$/', $this->getCodepostal())) {
            $context->buildViolation('client.errorCodePostalFr')->atPath('codepostal')->addViolation();
        }
    }

    public function getMontantCarte(): mixed
    {
        return $this->montantCarte;
    }

    public function setMontantCarte(string|float $montantCarte): void
    {
        $this->montantCarte = $this->parseFloat($montantCarte);
    }

    private function parseFloat(float|string $value): float
    {
        if (is_string($value)) {
            return floatval(str_replace([' ', ','], ['', '.'], $value));
        }

        return $value;
    }
}
