<?php

namespace App\Entity;

class Magasin
{
    protected ?string $id = null;

    protected ?string $libelle = null;

    protected ?string $localisation = null;

    protected ?string $telephone = null;

    protected ?string $email = null;

    protected ?string $siteWebMag = null;

    protected ?string $code = null;

    private ?string $adresse1 = null;

    private ?string $adresse2 = null;

    private ?string $codepostal = null;

    private ?string $ville = null;

    protected ?string $longitude = null;

    protected ?string $latitude = null;

    protected ?string $site = null;

    public function getLocalisation(): ?string
    {
        if (null !== $this->getLongitude() && null !== $this->getLatitude()) {
            return $this->getLatitude().','.$this->getLongitude();
        }

        return null;
    }

    public function setLocalisation($localisation): static
    {
        $this->localisation = $localisation;

        return $this;
    }

    public function getId(): ?string
    {
        return $this->getCode();
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code = null): void
    {
        $this->code = $code;
    }

    public function getLatitude(): array|string|null
    {
        if ($this->latitude) {
            return str_replace(',', '.', $this->latitude);
        }

        return null;
    }

    public function setLatitude($latitude): void
    {
        $this->latitude = $latitude;
    }

    public function getLongitude(): array|string|null
    {
        if ($this->longitude) {
            return str_replace(',', '.', $this->longitude);
        }

        return null;
    }

    public function setLongitude($longitude): void
    {
        $this->longitude = $longitude;
    }

    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail($email): void
    {
        $this->email = $email;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone($telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getSiteWebMag(): ?string
    {
        if ($this->siteWebMag) {
            $re = '/^http/i';

            preg_match_all($re, $this->siteWebMag, $matches, PREG_SET_ORDER, 0);
            if ([] === $matches) {
                return 'http://'.$this->siteWebMag;
            }
        }

        return $this->siteWebMag;
    }

    public function setSiteWebMag($siteWebMag): void
    {
        $this->siteWebMag = $siteWebMag;
    }

    public function getSite(): ?string
    {
        return $this->site;
    }

    public function setSite($site): void
    {
        $this->site = $site;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): void
    {
        $this->libelle = $libelle;
    }

    public function getAdresse1(): ?string
    {
        return $this->adresse1;
    }

    public function setAdresse1(?string $adresse1): void
    {
        $this->adresse1 = $adresse1;
    }

    public function getAdresse2(): ?string
    {
        return $this->adresse2;
    }

    public function setAdresse2(?string $adresse2): void
    {
        $this->adresse2 = $adresse2;
    }

    public function getCodepostal(): ?string
    {
        return $this->codepostal;
    }

    public function setCodepostal(?string $codepostal): void
    {
        $this->codepostal = $codepostal;
    }

    public function getVille(): ?string
    {
        return $this->ville;
    }

    public function setVille(?string $ville): void
    {
        $this->ville = $ville;
    }

    public function mergeMagasin(?Magasin $loadMagasin = null): void
    {
        if (!$loadMagasin) {
            return;
        }
        $this->id ??= $loadMagasin->getId();
        $this->libelle ??= $loadMagasin->getLibelle();
        $this->email ??= $loadMagasin->getEmail();
        $this->telephone ??= $loadMagasin->getTelephone();
        $this->siteWebMag ??= $loadMagasin->getSiteWebMag();
        $this->code ??= $loadMagasin->getCode();
        $this->longitude ??= $loadMagasin->getLongitude();
        $this->latitude ??= $loadMagasin->getLatitude();
        $this->site ??= $loadMagasin->getSite();
    }

    public function __toString()
    {
        return $this->libelle;
    }
}
