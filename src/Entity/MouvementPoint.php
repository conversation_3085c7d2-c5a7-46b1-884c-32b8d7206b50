<?php

namespace App\Entity;

/**
 * MouvementPoint.
 */
class MouvementPoint
{
    /**
     * @var \Datetime
     */
    private $dateOperation;

    /**
     * @var string
     */
    protected $operation;

    /**
     * @var string
     */
    private $libelle;

    protected ?float $nbPoints = null;

    /**
     * @param \Datetime $dateOperation
     */
    public function setDateOperation($dateOperation): static
    {
        $this->dateOperation = $dateOperation;

        return $this;
    }

    public function getDateOperation(): ?\DateTime
    {
        return $this->dateOperation;
    }

    public function setLibelle($libelle)
    {
        $this->libelle = $libelle;

        return $this;
    }

    public function getLibelle()
    {
        return $this->libelle;
    }

    public function setNbPoints(float|string $nbPoints): void
    {
        $this->nbPoints = $this->parseFloat($nbPoints);
    }

    public function getNbPoints(): ?float
    {
        return $this->nbPoints;
    }

    public function setOperation($operation)
    {
        $this->operation = $operation;

        return $this;
    }

    public function getOperation()
    {
        return $this->operation;
    }

    public function isPositif(): bool
    {
        return $this->nbPoints >= 0;
    }

    private function parseFloat(float|string $value): float
    {
        if (is_string($value)) {
            return floatval(str_replace([' ', ','], ['', '.'], $value));
        }

        return $value;
    }
}
