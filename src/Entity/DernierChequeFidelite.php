<?php

namespace App\Entity;

/**
 * Cheque.
 */
class DernierChequeFidelite
{
    private ?int $id = null;

    private ?string $numeroCheque = null;

    private ?float $montant = null;

    private ?\DateTime $dateFinValidite = null;

    private ?\DateTime $dateEmis = null;

    private ?string $codePrepa = null;

    private ?\DateTime $dateEncaissement = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getNumeroCheque(): ?string
    {
        return $this->numeroCheque;
    }

    public function setNumeroCheque(?string $numeroCheque): void
    {
        $this->numeroCheque = $numeroCheque;
    }

    public function getMontant(): ?float
    {
        return $this->montant;
    }

    public function setMontant(?float $montant): void
    {
        $this->montant = $montant;
    }

    public function getDateFinValidite(): ?\DateTime
    {
        return $this->dateFinValidite;
    }

    public function setDateFinValidite(?\DateTime $dateFinValidite): void
    {
        $this->dateFinValidite = $dateFinValidite;
    }

    public function getDateEmis(): ?\DateTime
    {
        return $this->dateEmis;
    }

    public function setDateEmis(?\DateTime $dateEmis): void
    {
        $this->dateEmis = $dateEmis;
    }

    public function getCodePrepa(): ?string
    {
        return $this->codePrepa;
    }

    public function setCodePrepa(?string $codePrepa): void
    {
        $this->codePrepa = $codePrepa;
    }

    public function getDateEncaissement(): ?\DateTime
    {
        return $this->dateEncaissement;
    }

    public function setDateEncaissement(?\DateTime $dateEncaissement): void
    {
        $this->dateEncaissement = $dateEncaissement;
    }
}
