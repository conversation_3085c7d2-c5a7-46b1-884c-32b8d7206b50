<?php

namespace App\Entity;

class Param
{
    private ?string $id = null;

    private ?string $libelle = null;

    private ?string $valeur = null;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): void
    {
        $this->libelle = $libelle;
    }

    public function getValeur(): ?string
    {
        return $this->valeur;
    }

    public function setValeur(?string $valeur): void
    {
        $this->valeur = $valeur;
    }
}
