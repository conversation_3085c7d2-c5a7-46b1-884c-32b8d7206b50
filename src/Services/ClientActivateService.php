<?php

namespace App\Services;

use Alienor\LoggerWebserviceBundle\Logger\WsLogger;
use App\Entity\ClientActivate;
use Symfony\Bundle\SecurityBundle\Security;

class ClientActivateService
{
    public $mapping = 'ClientActivate';

    public function __construct(WsLogger $wsLogger, ErrorManagementService $errorManagementService, array $params, Security $security)
    {
    }

    public function create($entity): ClientActivate
    {
        throw new \LogicException('TODO');
    }
}
