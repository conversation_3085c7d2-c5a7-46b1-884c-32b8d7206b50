<?php

namespace App\Services\Captcha;

use Symfony\Component\HttpFoundation\RequestStack;

class CaptchaService
{
    private $session;

    public function __construct(RequestStack $requestStack)
    {
        if ($requestStack->getCurrentRequest() instanceof \Symfony\Component\HttpFoundation\Request) {
            $this->session = $requestStack->getCurrentRequest()->getSession();
        }
    }

    /**
     * @return string
     */
    public function generateCaptchaNumber()
    {
        $first = random_int(1, 9);
        $second = random_int(1, 9);
        $this->session->set('captcha_first', $first);
        $this->session->set('captcha_second', $second);

        return $first.' + '.$second.' =';
    }

    /**
     * Check if provided result is egal to expected one.
     *
     * @return bool
     */
    public function checkCaptcha($captcha)
    {
        $expected = $this->session->get('captcha_first') + $this->session->get('captcha_second');

        return md5($expected) === md5((string) $captcha);
    }
}
