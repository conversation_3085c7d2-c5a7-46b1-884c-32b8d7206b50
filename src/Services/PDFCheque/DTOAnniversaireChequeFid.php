<?php

namespace App\Services\PDFCheque;

use App\Entity\Client;

readonly class DTOAnniversaireChequeFid implements DTOClientChequeInterface
{
    public function __construct(
        private Client $client,
    ) {
    }

    public function getNumCheque(): string
    {
        return $this->client->getNumDernierChequeAnniv();
    }

    public function getMontantCheque(): string
    {
        return $this->client->getMontantDernierChequeAnniv() ?? 0;
    }

    public function getPourcentageCheque(): string
    {
        return $this->client->getPourcentageDernierChequeAnniv() ?? 0;
    }

    public function getDateFinValiditeCheque(): string
    {
        return $this->client->getDateFinValiditeDernierChequeAnniv() ? $this->client->getDateFinValiditeDernierChequeAnniv()->format('d/m/Y') : '';
    }

    public function getCivilite(): string
    {
        return $this->client->getCivilite() ?? '';
    }

    public function getPrenom(): string
    {
        return $this->client->getPrenom() ?? '';
    }

    public function getNom(): string
    {
        return $this->client->getNom() ?? '';
    }

    public function getId(): string
    {
        return $this->client->getId() ?? '';
    }

    public function getTextModalite(): string
    {
        return 'pdf.annivmodalite';
    }

    public function getBackgroundImage(): string
    {
        return 'e-cheque-fid.png';
    }
}
