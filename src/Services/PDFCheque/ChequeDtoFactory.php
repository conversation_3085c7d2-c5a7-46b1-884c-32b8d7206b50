<?php

namespace App\Services\PDFCheque;

use App\Entity\Client;
use App\Services\Cheque\ChequeType;

class ChequeDtoFactory implements ChequeDtoFactoryInterface
{
    public function loadDTOForChequeFid(ChequeType $type, Client $client): DTOClientChequeInterface
    {
        return match ($type) {
            ChequeType::Bienvenue => new DTOBienvenueChequeFid($client),
            ChequeType::Anniversaire => new DTOAnniversaireChequeFid($client),
            ChequeType::Fidelite => new DTOClientChequeFid($client),
        };
    }
}
