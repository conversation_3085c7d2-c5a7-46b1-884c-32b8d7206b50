<?php

namespace App\Services\PDFCheque;

use Alienor\PDFBundle\Services\PDF;
use Alienor\PDFBundle\Services\PDFHandling;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Gestion de la génération des chèques.
 */
class EChequeGenerateur
{
    /**
     * Paramètrage des zones.
     */
    protected $zones = [
        // En tête
        'echeque' => [
            'x' => 10, 'y' => 19,
            'w' => 62, 'h' => 4,
        ],
        'fidelite' => [
            'x' => 10, 'y' => 30,
            'w' => 62, 'h' => 5,
        ],
        'montant' => [
            'x' => 80, 'y' => 24,
            'w' => 30, 'h' => 30,
        ],
        'beneficiaire' => [
            'x' => 10, 'y' => 43,
            'w' => 62, 'h' => 5,
        ],
        'beneficiaire-user' => [
            'x' => 10, 'y' => 48,
            'w' => 62, 'h' => 5,
        ],
        'num_carte' => [
            'x' => 10, 'y' => 56,
            'w' => 30, 'h' => 5,
        ],
        'carte_num' => [
            'x' => 36, 'y' => 56,
            'w' => 30, 'h' => 5,
        ],
        'valable' => [
            'x' => 77, 'y' => 63,
            'w' => 30, 'h' => 5,
        ],
        'modalite' => [
            'x' => 55, 'y' => 72,
            'w' => 69, 'h' => 10,
        ],
        'cheque_num' => [
            'x' => 10, 'y' => 85,
            'w' => 50, 'h' => 5,
        ],
        'background-image' => [
            'x' => 5, 'y' => 5,
            'w' => 200, 'h' => 0,
        ],
        'image_code_barre' => [
            'x' => 8, 'y' => 65,
            'w' => 0, 'h' => 18,
        ],
    ];

    protected string $folderImage;

    public function __construct(
        protected ParameterBagInterface $params,
        protected PDFHandling $pdfHandling,
        protected TranslatorInterface $translator,
    ) {
        $this->folderImage = sprintf('%s/enseigne/%s/assets/images/', $params->get('kernel.project_dir'), $params->get('enseigne'));
    }

    public function genererPdf(DTOClientChequeInterface $client): PDF
    {
        $pdf = $this->createPdf();
        $pdf->AddPage();
        $pdf->SetFont('Arial', 'B', 16);
        $this->addBackgroundImage($pdf, $this->getImageBackground($client));
        $this->addModalite($pdf);
        $this->addBeneficiare($client->getCivilite().' '.$client->getPrenom().' '.$client->getNom(), $pdf);
        $this->addNumCarte($client->getId(), $pdf);
        $this->addMontant($client, $pdf);
        $this->addValable($pdf, $this->getDateFinValiditeCheque($client));
        $this->addChequeNum($this->getNumCheque($client), $pdf);
        $this->addCodeBarre($this->getNumCheque($client), $pdf);

        return $pdf;
    }

    public function getImageBackground(DTOClientChequeInterface $client): string
    {
        return $this->folderImage.$client->getBackgroundImage();
    }

    public function getNumCheque(DTOClientChequeInterface $client): string
    {
        return $client->getNumCheque();
    }

    public function getMontantCheque(DTOClientChequeInterface $client): mixed
    {
        return $client->getMontantCheque();
    }

    public function getPourcentageCheque(DTOClientChequeInterface $client): mixed
    {
        return $client->getPourcentageCheque();
    }

    public function getDateFinValiditeCheque(DTOClientChequeInterface $client): mixed
    {
        return $client->getDateFinValiditeCheque();
    }

    protected function addFidelite($pdf, $param): void
    {
        $pdf->setFont('arial', null, 25);

        $pdf->setXY($this->zones['fidelite']['x'], $this->zones['fidelite']['y']);
        $pdf->cell($this->zones['fidelite']['y'], 5, strtoupper((string) $this->trans('pdf.fidelite')), 0, 2);
    }

    protected function addMontant(DTOClientChequeInterface $client, PDF $pdf): void
    {
        $isPercent = 0 == $this->getMontantCheque($client);

        $montant = $isPercent ? $this->getPourcentageCheque($client) : $this->getMontantCheque($client);
        $montant = mb_convert_encoding((string) $montant, 'ISO-8859-1');

        $pdf->setFont('arial', 'B', 60);

        $pdf->setXY($this->zones['montant']['x'], $this->zones['montant']['y']);
        define('EURO', chr(128));
        if (!$isPercent) {
            $pdf->cell($this->zones['montant']['y'], 5, $montant.EURO, 0, 2);
        } else {
            $pdf->setFont('arial', 'B', 50);
            $pdf->cell($this->zones['montant']['y'], 5, '-'.$montant.'%', 0, 2);
        }
    }

    protected function addModalite(PDF $pdf): void
    {
        $modalite = $this->trans('pdf.modalite');
        $pdf->setFont('arial', null, 7);
        $pdf->setXY($this->zones['modalite']['x'], $this->zones['modalite']['y']);
        $pdf->MultiCell($this->zones['modalite']['w'], 3, $modalite, 0, 'J');
    }

    /**
     * Traduit une chaîne.
     */
    protected function trans($label, array $parameters = [])
    {
        return mb_convert_encoding((string) $this->translator->trans($label, $parameters), 'ISO-8859-1');
    }

    protected function createPdf(): PDF
    {
        $pdf = $this->pdfHandling->createPdf();
        $pdf->setAutoPageBreak(false);
        $pdf->setTextColor(0, 0, 0);

        return $pdf;
    }

    protected function addECheque($pdf): void
    {
        $pdf->setFont('arial', 'B', 30);

        $pdf->setXY($this->zones['echeque']['x'], $this->zones['echeque']['y']);

        $pdf->cell($this->zones['echeque']['y'], 5, strtoupper((string) $this->trans('pdf.e-cheque')), 0, 2);
    }

    protected function addBeneficiare($user, $pdf): void
    {
        if (null == $user) {
            return;
        }

        $user = mb_convert_encoding((string) $user, 'ISO-8859-1');

        $pdf->setFont('arial', null, 9);

        $pdf->setXY($this->zones['beneficiaire']['x'], $this->zones['beneficiaire']['y']);

        $pdf->cell($this->zones['beneficiaire']['y'], 5, $this->trans('pdf.beneficiaire').' :', 0, 2);

        $pdf->setFont('arial', null, 10);
        $pdf->setXY($this->zones['beneficiaire-user']['x'], $this->zones['beneficiaire-user']['y']);

        $pdf->cell($this->zones['beneficiaire-user']['y'], 5, $user, 0, 2);
    }

    protected function addNumCarte($numCarte, $pdf): void
    {
        $numCarte = mb_convert_encoding((string) $numCarte, 'ISO-8859-1');

        $pdf->setFont('arial', null, 9);

        $pdf->setXY($this->zones['num_carte']['x'], $this->zones['num_carte']['y']);

        $pdf->cell($this->zones['num_carte']['y'], 5, $this->trans('pdf.carte_num').':', 0, 2);

        $pdf->setFont('arial', null, 10);
        $pdf->setXY($this->zones['carte_num']['x'], $this->zones['carte_num']['y']);

        $pdf->cell($this->zones['carte_num']['y'], 5, $numCarte, 0, 2);
    }

    protected function addValable(PDF $pdf, ?string $valable = null): void
    {
        if (null == $valable) {
            return;
        }

        $valable = mb_convert_encoding($valable, 'ISO-8859-1');
        $valable = $this->trans('pdf.valable').' '.$valable;

        $pdf->setFont('arial', null, 10);

        $pdf->setXY($this->zones['valable']['x'], $this->zones['valable']['y']);

        $pdf->cell($this->zones['valable']['y'], 5, $valable, 0, 2);
    }

    protected function addChequeNum($chequeNum, $pdf): void
    {
        if (null == $chequeNum) {
            return;
        }

        $chequeNum = $this->trans('pdf.cheque_num').' '.mb_convert_encoding((string) $chequeNum, 'ISO-8859-1');
        $pdf->setFont('arial', null, 7);

        $pdf->setXY($this->zones['cheque_num']['x'], $this->zones['cheque_num']['y']);

        $pdf->cell($this->zones['cheque_num']['y'], 5, $chequeNum, 0, 2);
    }

    protected function addBackgroundImage(PDF $pdf, ?string $imageBackground = null): void
    {
        if (null === $imageBackground) {
            return;
        }

        $pdf->Image($imageBackground, $this->zones['background-image']['x'], $this->zones['background-image']['y'],
            $this->zones['background-image']['w'], $this->zones['background-image']['h']);
    }

    protected function addCodeBarre($code, PDF $pdf): void
    {
        $generator = new BarcodeGeneratorPNG();
        $path = 'code_barre/'.$code.'.png';
        file_put_contents($path, $generator->getBarcode($code, $generator::TYPE_EAN_13, 2, 90));
        $pdf->Image($path, $this->zones['image_code_barre']['x'], $this->zones['image_code_barre']['y'],
            $this->zones['image_code_barre']['w'], $this->zones['image_code_barre']['h']);
        unlink($path);
    }
}
