<?php

namespace App\Services;

use App\DTO\ClientUpdate;
use App\DTO\WebserviceError;
use App\Entity\Client;
use App\Entity\ClientInterface;
use App\Serializer\ClientDeserializer;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class NewClientsService implements NewClientsServiceInterface, ClientAuthenticatorInterface
{
    use StaticParamsTrait;

    public const string CLIENT_SELECT_ROUTE = 'PhDClientClientSelect.php';
    public const string CLIENT_UPDATE_ROUTE = 'PhDClientClientUpdate.php';
    private ?UserInterface $user = null;

    public function __construct(
        #[Autowire(service: 'ws1')] private readonly HttpClientInterface $aquitemClient,
        private readonly NormalizerInterface $normalizer,
        private readonly ClientDeserializer $clientInterfaceDeserializer,
        private readonly Security $security,
        private readonly array $webserviceStaticParams,
    ) {
        $this->setSecurity($security);
    }

    public function checkCodeClient($code, $ajax = false): WebserviceError|ClientInterface|true
    {
        $response = $this->aquitemClient->request('GET', $this->getClientSelectRoute(), [
            'query' => [
                'codeclient' => $code,
            ],
        ])->getContent();

        return $this->clientInterfaceDeserializer->deserialize($response, $this->getDTOClass());
    }

    public function authenticate(string $username, string $password): WebserviceError|ClientInterface|true
    {
        $data = [
            'CodeCarte' => $username,
            'DateNaissance' => $password,
        ];
        $response = $this->aquitemClient->request('GET', $this->getClientSelectRoute(), [
            'query' => $this->addStaticParams($data),
        ])->getContent();

        return $this->clientInterfaceDeserializer->deserialize($response, $this->getDTOClass());
    }

    public function setDisableError(bool $disableError): void
    {
    }

    public function update(ClientInterface $client): WebserviceError|ClientInterface|true
    {
        $data = $this->normalizer->normalize($this->getDTOUpdateClass()::fromClient($client));
        $requestData = $this->addStaticParams($data);

        ksort($requestData);

        $response = $this->aquitemClient->request('POST', $this->getClientUpdateRoute(), [
            'body' => $requestData,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
        ])->getContent();

        return $this->clientInterfaceDeserializer->deserialize($response, $this->getDTOClass());
    }

    public function getClientSelectRoute(): string
    {
        return self::CLIENT_SELECT_ROUTE;
    }

    public function getClientUpdateRoute(): string
    {
        return self::CLIENT_UPDATE_ROUTE;
    }

    public function getDTOClass(): string
    {
        return Client::class;
    }

    public function getDTOUpdateClass(): string
    {
        return ClientUpdate::class;
    }
}
