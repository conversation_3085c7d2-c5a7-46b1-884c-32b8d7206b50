<?php

namespace App\Services\FeatureFlipping;

use App\Services\Cheque\ChequeType;

class InMemoryFeatureChecker implements FeatureCheckerInterface
{
    public const string DON = 'don';
    public const string ACTIVATION = 'activation';
    public const string CHEQUE_FIDELITE = 'remise.fidelite';

    public const string BIENVENUE = 'remise.bienvenue';
    public const string ANNIVERSAIRE = 'remise.anniversaire';
    private array $features;

    public function __construct(
        array $features = [],
    ) {
        $this->features = array_merge(
            [
                self::DON => false,
                self::ACTIVATION => false,
                self::CHEQUE_FIDELITE => false,
                self::BIENVENUE => false,
                self::ANNIVERSAIRE => false,
            ],
            $features
        );
    }

    private function isEnabled(string $feature): bool
    {
        return $this->features[$feature] ?? false;
    }

    public function hasActivation(): bool
    {
        return $this->isEnabled(self::ACTIVATION);
    }

    public function hasDon(): bool
    {
        return $this->isEnabled(self::DON);
    }

    public function hasChequeFromString(string $type): bool
    {
        return $this->hasCheque(ChequeType::from($type));
    }

    private function hasCheque(ChequeType $type): bool
    {
        return match ($type) {
            ChequeType::Bienvenue => $this->isEnabled(self::BIENVENUE),
            ChequeType::Anniversaire => $this->isEnabled(self::ANNIVERSAIRE),
            ChequeType::Fidelite => $this->isEnabled(self::CHEQUE_FIDELITE),
        };
    }

    public function hasChequeBienvenue(): bool
    {
        return $this->hasCheque(ChequeType::Bienvenue);
    }

    public function hasChequeAnniversaire(): bool
    {
        return $this->hasCheque(ChequeType::Anniversaire);
    }

    public function hasChequeFidelite(): bool
    {
        return $this->hasCheque(ChequeType::Fidelite);
    }
}
