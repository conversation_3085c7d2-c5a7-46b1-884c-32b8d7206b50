<?php

namespace App\Services\FeatureFlipping;

use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: 'featureChecker')]
interface FeatureCheckerInterface
{
    public function hasActivation(): bool;

    public function hasDon(): bool;

    public function hasChequeBienvenue(): bool;

    public function hasChequeAnniversaire(): bool;

    public function hasChequeFidelite(): bool;
}
