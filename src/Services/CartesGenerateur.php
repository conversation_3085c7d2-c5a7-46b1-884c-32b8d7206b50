<?php

namespace App\Services;

use Alienor\PDFBundle\Services\PDF;
use Alienor\PDFBundle\Services\PDFHandling;
use App\Entity\ClientInterface;
use <PERSON><PERSON>qer\Barcode\BarcodeGeneratorPNG;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Gestion de la génération des chèques.
 */
class CartesGenerateur
{
    public const string CARTES_GENEREES_DIR = '%s/enseigne/%s/cartes/%s.pdf';

    /**
     * Paramètrage des zones.
     */
    protected static $zones = [
        // En tête
        'background-image' => [
            'x' => 5, 'y' => 5,
            'w' => 200, 'h' => 0,
        ],
        'num_carte' => [
            'x' => 25, 'y' => 34,
            'w' => 20, 'h' => 5,
        ],
        'code_barre' => [
            'x' => 34, 'y' => 42,
            'w' => 0, 'h' => 20,
        ],
        'texte_code_barre' => [
            'x' => 46, 'y' => 62,
            'w' => 0, 'h' => 5,
        ],
    ];

    protected ?string $enseigne = null;

    public function __construct(
        protected ParameterBagInterface $params,
        protected PDFHandling $pdfHandling,
        protected TranslatorInterface $translator,
    ) {
        $this->enseigne = $params->get('EnseigneId');
    }

    protected function trans($label, array $parameters = []): string
    {
        return $this->translator->trans($label, $parameters);
    }

    public function genererPdf($client): void
    {
        $pdf = $this->createPdf();
        $pdf->AddPage();
        if ($this->enseigne) {
            $kernelProjectDir = $this->params->get('kernel.project_dir');
            $this->addBackgroundImage(sprintf('%s/enseigne/%s/assets/images/FOND_carte.png', $kernelProjectDir, $this->enseigne), $pdf);
        }
        $this->addNumCarte($client->getId(), $pdf);
        $this->addCodeBarre($client->getCodeCarte(), $pdf);
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->output('I', $this->getPathCarte($client));
    }

    protected function createPdf(): PDF
    {
        $pdf = $this->pdfHandling->createPdf();
        $pdf->setAutoPageBreak(false);
        $pdf->setTextColor(0, 0, 0);

        return $pdf;
    }

    protected function addBackgroundImage(string $imageBackground, PDF $pdf): void
    {
        $pdf->Image($imageBackground, self::$zones['background-image']['x'], self::$zones['background-image']['y'], self::$zones['background-image']['w'], self::$zones['background-image']['h']);
    }

    protected function addNumCarte(string $numCarte, PDF $pdf): void
    {
        $numCarte = mb_convert_encoding($this->trans('pdf.carte_num').' : '.$numCarte, 'ISO-8859-1');

        $pdf->setFont('arial', 'b', 10);
        $pdf->setXY(self::$zones['num_carte']['x'], self::$zones['num_carte']['y']);
        $pdf->cell(self::$zones['num_carte']['y'], 5, $numCarte, 0, 2);
    }

    protected function addCodeBarre(string $code, PDF $pdf): void
    {
        $generator = new BarcodeGeneratorPNG();
        $path = 'code_barre/'.$code.'.png';
        file_put_contents($path, $generator->getBarcode($code, $generator::TYPE_EAN_13, 2, 90));
        $pdf->Image($path, self::$zones['code_barre']['x'], self::$zones['code_barre']['y'], self::$zones['code_barre']['w'], self::$zones['code_barre']['h']);
        $pdf->setFont('arial', null, 7);
        $pdf->setXY(self::$zones['texte_code_barre']['x'], self::$zones['texte_code_barre']['y']);
        $pdf->cell(self::$zones['texte_code_barre']['y'], 5, $code, 0, 2);
        unlink($path);
    }

    public function getPathCarte(ClientInterface $client): string
    {
        $kernelProjectDir = $this->params->get('kernel.project_dir');

        return sprintf(self::CARTES_GENEREES_DIR, $kernelProjectDir, $this->enseigne, $client->getId());
    }
}
