<?php

namespace App\Services;

use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\EnvVarLoaderInterface;
use Symfony\Component\Dotenv\Dotenv;

readonly class DotEnvEnseigneLoader implements EnvVarLoaderInterface
{
    public function __construct(
        #[Autowire('%env(ENSEIGNE)%')] private string $enseigne,
        #[Autowire('%kernel.project_dir%')] private string $projectDir,
    ) {
    }

    public function loadEnvVars(): array
    {
        $result = [];
        $dotEnvLoader = new Dotenv();
        if ('' !== $this->enseigne) {
            $enseigneDir = $this->projectDir.'/enseigne/'.$this->enseigne;
            $dotEnvFile = $enseigneDir.'/.env';
            $dotEnvLocalFile = $enseigneDir.'/.env.local';
            if (file_exists($dotEnvFile)) {
                $result = $dotEnvLoader->parse(
                    file_get_contents($dotEnvFile)
                );
            }
            if (file_exists($dotEnvLocalFile)) {
                $result = array_merge($result, $dotEnvLoader->parse(
                    file_get_contents($dotEnvLocalFile)
                ));
            }
        }

        return $result;
    }
}
