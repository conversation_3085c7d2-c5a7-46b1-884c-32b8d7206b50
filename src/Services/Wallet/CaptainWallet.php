<?php

namespace App\Services\Wallet;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class CaptainWallet implements WalletInterface
{
    private readonly string $url;

    private readonly string $enseigne;

    private readonly string $salt;

    public function __construct(private readonly ParameterBagInterface $params)
    {
        $this->url = $this->params->get('passbook.url');
        $this->enseigne = $this->params->get('passbook.enseigneId');
        $this->salt = $this->params->get('passbook.saltClient');
    }

    public function generateUrl($client): ?string
    {
        $token = $this->generateToken($client);

        return sprintf($this->url, $client->getCodeCarte(), $client->getMagasin()->getId(), $token);
    }

    private function generateToken($client): string
    {
        $codeCarte = $client->getCodeCarte();

        return hash('sha256', $this->enseigne.'-'.$codeCarte.'-'.$this->salt);
    }
}
