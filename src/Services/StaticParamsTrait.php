<?php

namespace App\Services;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Contracts\Service\Attribute\Required;

trait StaticParamsTrait
{
    protected ?Security $securityTrait = null;

    #[Required]
    public function setSecurity(Security $security): void
    {
        $this->securityTrait = $security;
    }

    public function addStaticParams(?array $params = []): array
    {
        if (property_exists(self::class, 'user')) {
            $user = $this->user;
        }
        if (!isset($user) || null === $user) {
            $user = $this->securityTrait->getUser();
        }

        // todo à gérer de manière plus propre
        if ($user && is_object($user) && $user->getId()) {
            $params['from_idsession'] = $user->getId();
        }

        if ($this->webserviceStaticParams && [] !== $this->webserviceStaticParams) {
            $tempParams = [];
            foreach ($this->webserviceStaticParams as $param) {
                $tempParams[$param['key']] = $param['value'];
            }

            $params = array_merge($params, $tempParams);
        }

        return $params;
    }
}
