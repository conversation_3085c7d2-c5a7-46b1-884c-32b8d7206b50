<?php

namespace App\Services;

use App\DTO\ClientActivate;
use App\DTO\WebserviceError;
use App\Entity\Client;
use App\Entity\ClientActivateInterface;
use App\Entity\ClientInterface;
use App\Serializer\ClientDeserializer;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class NewClientActivateService implements ClientActivateServiceInterface
{
    use StaticParamsTrait;

    public function __construct(
        #[Autowire(service: 'ws1')] private readonly HttpClientInterface $aquitemClient,
        private readonly NormalizerInterface $normalizer,
        private readonly ClientDeserializer $clientInterfaceDeserializer,
        private readonly array $webserviceStaticParams,
    ) {
    }

    public function create(ClientActivateInterface $entity): WebserviceError|ClientInterface
    {
        $data = $this->normalizer->normalize($this->getDTOCreateClass()::fromClient($entity));
        $requestData = $this->addStaticParams($data);

        ksort($requestData);
        $response = $this->aquitemClient->request('POST', 'PhDClientClientCreate.php', [
            'body' => $requestData,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
        ])->getContent();

        return $this->clientInterfaceDeserializer->deserialize($response, $this->getDTOClass());
    }

    public function getDTOClass(): string
    {
        return Client::class;
    }

    private function getDTOCreateClass(): string
    {
        return ClientActivate::class;
    }
}
