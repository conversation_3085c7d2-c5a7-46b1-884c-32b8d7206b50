<?php

namespace App\Services\Cagnotte\CagnotteStrategies;

use App\Entity\Client;
use App\Services\Cagnotte\CagnotteData;

class CagnotteFromClient implements CagnotteStrategyInterface
{
    public function getCagnotteData(Client $client): CagnotteData
    {
        $nbrePtsRestantCheque = floatval(str_replace([' ', ','], ['', '.'], $client->getNbrePtsRestantCheque()));
        $cagnotte = floatval(str_replace([' ', ','], ['', '.'], $client->getNbrePoints()));
        $seuilUtilisationCagnotte = $cagnotte + $nbrePtsRestantCheque;

        return new CagnotteData($cagnotte, $nbrePtsRestantCheque, $seuilUtilisationCagnotte);
    }
}
