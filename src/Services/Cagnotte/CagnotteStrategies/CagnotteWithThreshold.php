<?php

namespace App\Services\Cagnotte\CagnotteStrategies;

use App\Entity\Client;
use App\Entity\GlobalDefinitions;
use App\Services\Cagnotte\CagnotteData;
use App\Services\NewGlobalDefinitionsService;

class CagnotteWithThreshold implements CagnotteStrategyInterface
{
    public function __construct(
        private readonly NewGlobalDefinitionsService $globalDefinitionsService,
    ) {
    }

    public function getCagnotteData(Client $client): CagnotteData
    {
        /** @var GlobalDefinitions $globalDefinitions */
        $globalDefinitions = $this->globalDefinitionsService->call();
        $seuilUtilisationCagnotteObj = $globalDefinitions->getParamById('SEUIL_UTILISATION_CAGNOTTE');
        $cagnotte = floatval(str_replace([' ', ','], ['', '.'], $client->getNbrePoints()));
        if (null === $seuilUtilisationCagnotteObj->getValeur()) {
            throw new \LogicException('Le seuil utilisation cagnotte n\'est pas renseigné par le webservice des données statiques');
        }
        $seuilUtilisationCagnotte = floatval(str_replace([' ', ','], ['', '.'], $seuilUtilisationCagnotteObj->getValeur()));

        $cagnotteReste = $cagnotte < $seuilUtilisationCagnotte ? $seuilUtilisationCagnotte - $cagnotte : 0;

        return new CagnotteData($cagnotte, $cagnotteReste, $seuilUtilisationCagnotte);
    }
}
