<?php

namespace App\Services;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ErrorManagementService
{
    public const C_ERR_UNKNOWN = -20000;
    public const C_ERR_CASCADE = -20001;
    public const C_ERR_VALUE_ERROR = -20002;
    public const C_ERR_DUP_VAL_ON_INDEX = -20003;
    public const C_ERR_ANCIENNE_CARTE_RATTACH = -20004;
    public const C_ERR_ARGUMENT_OBLIGATOIRE = -20005;
    public const C_ERR_ART_DECOUVERTE_UTILISE = -20006;
    public const C_ERR_ART_DECOUVERTE_INEXIST = -20007;
    public const C_ERR_COMPTEFB_INCONNU = -20008;
    public const C_ERR_FLAGSUPPRESSION = -20009;
    public const C_ERR_CARTE_DEJA_FUSIONNEE = -20010;
    public const C_ERR_MAJ_ASSOC_CAMPAGNE = -20011;
    public const C_ERR_IDFB_DEJA_ASSOCIE = -20012;
    public const C_ERR_CREAT_ASSOC_CAMPAGNE = -20013;
    public const C_ERR_CHAMP_INVALIDE = -20014;
    public const C_ERR_CHAMP_OBLIGATOIRE = -20015;
    public const C_ERR_CHAMPS_OBLIGATOIRE = -20016;
    public const C_ERR_CIVILITE_OBLIGATOIRE = -20017;
    public const C_ERR_CLIENT_DEJA_ASSOCIE = -20018;
    public const C_ERR_CLIENT_NON_ENCARTE = -20019;
    public const C_ERR_NO_RESULTS = -20020;
    public const C_ERR_CODE_ARTICLE_INVALIDE = -20021;
    public const C_ERR_CODE_ARTICLE_OBLIGATOIRE = -20022;
    public const C_ERR_CODECARTE_INVALIDE = -20023;
    public const C_ERR_CODE_CLIENT_INCONNU = -20024;
    public const C_ERR_CP_INVALIDE = -20025;
    public const C_ERR_CLIENT_EXISTANT = -20026;
    public const C_ERR_CODECLIENT_INVALIDE = -20027;
    public const C_ERR_CSP_INVALIDE = -20028;
    public const C_ERR_DN_INVALIDE = -20029;
    public const C_ERR_DATEVENTE_INVALIDE = -20030;
    public const C_ERR_DATE_DEBUT_CAMPAGNE = -20031;
    public const C_ERR_DATE_ENCAIS_OBLIGATOIRE = -20032;
    public const C_ERR_DATE_ENCAIS_INVALIDE = -20033;
    public const C_ERR_DATE = -20034;
    public const C_ERR_DATE_FIN_CAMPAGNE = -20035;
    public const C_ERR_CREATION_CLIENT = -20036;
    public const C_ERR_EMAIL_INVALIDE = -20037;
    public const C_ERR_EMAIL_OBLIGATOIRE = -20038;
    public const C_ERR_EMAILFACEBOOK_INVALIDE = -20039;
    public const C_ERR_MAGASIN = -20040;
    public const C_ERR_IDENTIFICATION = -20041;
    public const C_ERR_CODEPOSTAL = -20042;
    public const C_ERR_DATENAISSANCE = -20043;
    public const C_ERR_CIVILITE = -20044;
    public const C_ERR_CIVILITELANGUE = -20045;
    public const C_ERR_LANGUE = -20046;
    public const C_ERR_LONGUEURBATEAU = -20047;
    public const C_ERR_NOM_TROPLONG = -20048;
    public const C_ERR_NOM_VIDE = -20049;
    public const C_ERR_PRENOM_TROPLONG = -20050;
    public const C_ERR_PROPRIETAIRE = -20051;
    public const C_ERR_TYPEBATEAU = -20052;
    public const C_ERR_TYPEBATEAUMOTEUR = -20053;
    public const C_ERR_ENVOIECOURRIERINTERNE = -20054;
    public const C_ERR_ENVOIEEMAILEXTERNE = -20055;
    public const C_ERR_ENVOIESMSINTERNE = -20056;
    public const C_ERR_DONNEE_ENFANT = -20057;
    public const C_ERR_FROM_MAG_OBLIGATOIRE = -20058;
    public const C_ERR_FUSION_INTERDITE = -20059;
    public const C_ERR_HEURE = -20060;
    public const C_ERR_ID_ARTICLE_INVALIDE = -20061;
    public const C_ERR_ID_ARTICLE_OBLIGATOIRE = -20062;
    public const C_ERR_IDACTION_INCONNU = -20063;
    public const C_ERR_IDACTION_OBLIGATOIRE = -20064;
    public const C_ERR_IDANIMAL_OBLIGATOIRE = -20065;
    public const C_ERR_IDASSOCIATION_INCONNU = -20066;
    public const C_ERR_IDASSO_OBLIGATOIRE = -20067;
    public const C_ERR_IDCLIENTANIMAL_OBLIG = -20068;
    public const C_ERR_ID_CAMPAGNE_INVALIDE = -20069;
    public const C_ERR_IDFB_CODE_OBLIG = -20070;
    public const C_ERR_IDFACEBOOK_OBLIG = -20071;
    public const C_ERR_IDMODE_OBLIG = -20072;
    public const C_ERR_IDSESSION_INVALIDE = -20073;
    public const C_ERR_IDSTATUT_OBLIG = -20074;
    public const C_ERR_CONTRAINTE = -20075;
    public const C_ERR_INDICATEUR_INVALIDE = -20076;
    public const C_ERR_INDICATIFFIXE = -20077;
    public const C_ERR_INDICATIFMOBILE = -20078;
    public const C_ERR_NOM_COMPLET = -20079;
    public const C_ERR_JSON_OBLIG = -20080;
    public const C_ERR_DATE_DEBUT_FIN = -20081;
    public const C_ERR_VENTE_DATE_INCORRECT = -20082;
    public const C_ERR_CHEQUE_CLIENT = -20083;
    public const C_ERR_CLIENT_CARTE_INCOHERENT = -20084;
    public const C_ERR_MONTANT_SUP_CHQ = -20085;
    public const C_ERR_TOKEN_OBLIGATOIRE = -20086;
    public const C_ERR_LIBELLE_ASSOC_OBLIG = -20087;
    public const C_ERR_MAG_DIFFERENT = -20088;
    public const C_ERR_MAGASIN_NOTFOUND = -20089;
    public const C_ERR_MAGASIN_OBLIGATOIRE = -20090;
    public const C_ERR_MASK_ENSEIGNE = -20091;
    public const C_ERR_PARAMETRE_OBLIGATOIRE = -20092;
    public const C_ERR_MODE_INCORRECT = -20093;
    public const C_ERR_MONTANT_INVALIDE = -20094;
    public const C_ERR_MONTANT_OBLIGATOIRE = -20095;
    public const C_ERR_MOTIF_OBLIGATOIRE = -20096;
    public const C_ERR_FOURCHETTE_NBPOINTS = -20097;
    public const C_ERR_NOEPC_NONATIONAL_OBLIG = -20098;
    public const C_ERR_NOM_CLIENT_OBLIG = -20099;
    public const C_ERR_NOM_CLIENT = -20100;
    public const C_ERR_NBRACHATS_INCORRECT = -20101;
    public const C_ERR_NBRACHATS_OBLIGATOIRE = -20102;
    public const C_ERR_NBPOINTS_INVALIDE = -20103;
    public const C_ERR_NBPOINTS_NEGATIF = -20104;
    public const C_ERR_NBPOINTS_OBLIGATOIRE = -20105;
    public const C_ERR_NUM_CHEQUE_INVALIDE = -20106;
    public const C_ERR_NUM_CHEQUE_OBLIGATOIRE = -20107;
    public const C_ERR_CODECLIENT_OBLIGATOIRE = -20108;
    public const C_ERR_CODECLIENT_CARTE_OBLIGA = -20109;
    public const C_ERR_PORTAMARRAGE_TROPLONG = -20110;
    public const C_ERR_PRENOM_CLIENT_OBLIG = -20111;
    public const C_ERR_PRENOM_CLIENT = -20112;
    public const C_ERR_ART_DECOUVERTE_QTESUP = -20113;
    public const C_ERR_RECH_MAGASIN_ERROR = -20114;
    public const C_ERR_MAGRES_DIFFERENT = -20115;
    public const C_ERR_SITE_OBLIGATOIRE = -20116;
    public const C_ERR_SITFAM_INVALIDE = -20117;
    public const C_ERR_POINTS_INSUFFISANTS = -20118;
    public const C_ERR_ARGUMENT_ERROR = -20119;
    public const C_ERR_CELL_INVALIDE = -20120;
    public const C_ERR_TEL_FIXE_INVALIDE = -20121;
    public const C_ERR_TEL_FIXE_OBLIGATOIRE = -20122;
    public const C_ERR_TEL_MOBILE_INVALIDE = -20123;
    public const C_ERR_TEL_MOBILE_OBLIGATOIRE = -20124;
    public const C_ERR_TITRE_INVALIDE = -20125;
    public const C_ERR_TITRE_CAMPAGNE_OBLIG = -20126;
    public const C_ERR_TOKEN_INVALIDE = -20127;
    public const C_ERR_TOKEN = -20128;
    public const C_ERR_VENTE_OBLIGATOIRE = -20129;
    public const C_ERR_DOUBLEMENT_VENTE = -20130;
    public const C_ERR_IDPARENTINCONNU = -20131;
    public const C_ERR_ENFANT_MAJEUR = -20132;
    public const C_ERR_NEWSLETTER_OBLIGATOIRE = -20133;
    public const C_ERR_CODEPOSTAL_OBLIGATOIRE = -20134;
    public const C_ERR_VILLE_OBLIGATOIRE = -20135;
    public const C_ERR_CHAMPS_OBLIGATOIRE_BIS = -20136;
    public const C_ERR_TYPEDEGROSSESSE_INVALIDE = -20137;
    public const C_ERR_SITE_INVALIDE = -20153;
    public const C_ERR_MAGASIN_INVALIDE = -20154;
    public const C_ERR_LOGIN_OBLIGATOIRE = -20155;
    public const C_ERR_PASSWORD_OBLIGATOIRE = -20156;
    public const C_ERR_CODEENSEIGNE_OBLIGATOIRE = -20157;
    public const C_ERR_CP_DTNAIS_OBLIGATOIRE = -20158;
    public const C_ERR_DATENAISSANCE_OBLIG = -20159;
    public const C_ERR_CLIENT_MINEUR = -20160;
    public const C_ERR_VENTELIGNES_OBLIGATOIRE = -20161;
    public const C_ERR_CAGNOTTE_INVALIDE = -20162;
    public const C_ERR_CAGNOTTE_INF_SEUIL = -20163;
    public const C_ERR_CONSTANTE_PARAM = -20164;
    public const C_ERR_STRUCTURE_VENTE_INVALIDE = -20165;
    public const C_ERR_PRIX_INVALIDE = -20166;
    public const C_ERR_QUANTITE_INVALIDE = -20167;
    public const C_ERR_PAYS_INVALIDE = -20170;
    public const C_ERR_CODECARTE_EXISTENT = -20171;
    public const C_ERR_ENQUETE_INCONNUE = -20172;
    public const C_ERR_ENQUETE_DEJAREPONDU = -20173;
    public const C_ERR_QUESTION_NONREPONDU = -20174;
    public const C_ERR_NUMEROVOIE = -20175;
    public const C_ERR_BATESCLIEUDIT = -20176;
    public const C_ERR_ENQUETE_NONREPONDUE = -20177;
    public const C_ERR_CODESECOND_INVALIDE = -20193;
    public const C_ERR_CODESECOND_OBLIGATOIRE = -20194;
    public const C_ERR_FUSION_ZZZ = -20195;
    public const C_ERR_CODECARTE_HORSTRANCHE = -20196;
    public const C_ERR_GROUPSALON = -20213;
    public const C_ERR_LOGICIELCAISSE = -20214;
    public const C_ERR_NUMEROCOMPTE_INVALIDE = -20233;
    public const C_ERR_CODEFILLEUL_OBLIGATOIRE = -20253;
    public const C_ERR_CODEFILLEUL_INVALIDE = -20254;
    public const C_ERR_VOIE_OBLIGATOIRE = -20273;
    public const C_ERR_NUMERO_OBLIGATOIRE = -20274;
    public const C_ERR_CODEPAYS_OBLIGATOIRE = -20275;
    public const C_ERR_DATEVENTE_OBLIGATOIRE = -20276;
    public const C_ERR_FROM_IDSESSION_OBLIG = -20277;
    public const C_ERR_CODEPAYSCLIENT_OBLIG = -20278;
    public const C_ERR_CIVILITE_INCONNU = -20279;
    public const C_ERR_CODEPAYSCLIENT_INCONNU = -20280;
    public const C_ERR_HABITATION_INCONNU = -20281;
    public const C_ERR_ANIMAL_INCONNU = -20282;
    public const C_ERR_GENRE_INCONNU = -20283;
    public const C_ERR_CODEPARRAIN_INVALIDE = -20284;
    public const C_ERR_CODEPARRAIN_OBLIGATOIRE = -20285;
    public const C_ERR_FILLEUL_EXISTANT = -20286;
    public const C_ERR_PARRAINAGE_EXISTANT = -20287;
    public const C_ERR_PARRAINAGE_INVALIDE = -20288;
    public const C_ERR_NUMEROTICKET_OBLIGATOIRE = -20289;
    public const C_ERR_DATEUTILISATION_OBLIGAT = -20293;
    public const C_ERR_DATEUTILISATION_INVALIDE = -20294;
    public const C_ERR_CAGNOTTEUTILISEE_OBLIGAT = -20295;
    public const C_ERR_CAGNOTTEUTILISEE_INVALID = -20296;
    public const C_ERR_NUMEROTICKET_INVALIDE = -20297;
    public const C_ERR_CLIENT_EXISTANT_ACTIF = -20298;
    public const C_ERR_CLIENT_EXISTANT_INACTIF = -20299;
    public const C_ERR_FAMILLE_OBLIGATOIRE = -20300;
    public const C_ERR_SOUSFAMILLE_OBLIGATOIRE = -20301;
    public const C_ERR_TTC_OBLIGATOIRE = -20302;
    public const C_ERR_ACHAT_NEGATIF = -20304;
    public const C_ERR_CHEQUE_ENCAISSE = -20305;
    public const C_ERR_CHEQUE_NONTROUVE = -20306;
    public const C_ERR_TTC_INVALIDE = -20307;
    public const C_ERR_ENCAISSE_CHQ_INVALIDE = -20308;
    public const C_ERR_REGLEMENT_OBLIGATOIRE = -20309;
    public const C_ERR_MODEREGL_OBLIGATOIRE = -20310;
    public const C_ERR_CODECARTE_OBLIGATOIRE = -20311;
    public const C_ERR_INTERET_SSINT_INVALIDE = -20313;
    public const C_ERR_CATEGORIE_ART_INVALIDE = -20314;
    public const C_ERR_CARTESECOND_OBLIGATOIRE = -20315;
    public const C_ERR_CARTESECOND_INEXISTANT = -20316;
    public const C_ERR_CODECARTE_IDENTIQUE = -20317;
    public const C_ERR_CARTESECOND_INVALIDE = -20318;
    public const C_ERR_COMPTEUSER_INADAPTE = -20319;
    public const C_ERR_CODECARTE_NON_ATTRIBUE = -20320;
    public const C_ERR_CARTE_TRANCHE_INVALIDE = -20321;
    public const C_ERR_MODEREGL_TTC_OBLIGATOIRE = -20322;
    public const C_ERR_COLLABORATEUR_INVALIDE = -20323;
    public const C_ERR_PERIODE_INVALIDE = -20324;
    public const C_ERR_MOBILE_OU_EMAIL_OBLIG = -20333;
    public const C_ERR_CODEVENDEUR_OBLIGATOIRE = -20334;
    public const C_ERR_IDVENTE_OBLIGATOIRE = -20353;
    public const C_ERR_CARTESECOND_DEJASECOND = -20375;
    public const C_ERR_CODECARTE_INEXISTANT = -20393;
    public const C_ERR_CODECLIENT_DEJASUPP = -20394;
    public const C_ERR_PROGRAMME_OBLIGATOIRE = -20413;
    public const C_ERR_CHEQUE_PERIME = -20433;
    public const C_ERR_PROGRAMME_INVALIDE = -20434;
    public const C_ERR_MOTCLE_INVALIDE = -20435;
    public const C_ERR_VILLE_INVALIDE = -20436;
    public const C_ERR_PRENOM_OU_NOM_INVALIDE = -20437;
    public const C_ERR_IDCANALCHQFID_INVALIDE = -20438;
    public const C_ERR_OPTIN_INVALIDE = -20439;
    public const C_ERR_NUMERO_INVALIDE = -20453;
    public const C_ERR_LIBELLE_OBLIGATOIRE = -20473;
    public const C_ERR_ADRESSE_OBLIGATOIRE = -20474;
    public const C_ERR_REGION_OBLIGATOIRE = -20475;
    public const C_ERR_GROUPEMAG_OBLIGATOIRE = -20476;
    public const C_ERR_CODEINSEE_OBLIGATOIRE = -20477;
    public const C_ERR_AGREMENT_OBLIGATOIRE = -20478;
    public const C_ERR_NPAI_INVALIDE = -20493;
    public const C_ERR_MAGASIN_PROGRAMME_EXISTE = -20513;
    public const C_ERR_FAX_INVALIDE = -20514;
    public const C_ERR_GENRE_ENFANT_INVALIDE = -20515;
    public const C_ERR_MODEDEVIE_INVALIDE = -20516;
    public const C_ERR_MARQUE_INVALIDE = -20517;
    public const C_ERR_CENTRE_INT_BE_INVALIDE = -20518;
    public const C_ERR_CENTRE_INT_L_INVALIDE = -20519;
    public const C_ERR_INTERET_INVALIDE = -20573;
    public const C_ERR_CODEACCES_OBLIGATOIRE = -20574;
    public const C_ERR_CODEACCES_INVALIDE = -20575;
    public const C_ERR_CODECARTE_OU_NOMBW_OBLIG = -20576;
    public const C_ERR_CODEVENDEUR_INVALIDE = -20593;
    public const C_ERR_CLIENT_SUPPRIME = -20613;
    public const C_ERR_NO_CLIENT_RATTACH_CARTE = -20614;
    public const C_ERR_IDEXTERNE_OBLIGATOIRE = -20633;
    public const C_ERR_IDEXTERNE_INVALIDE = -20634;
    public const C_ERR_NUMEROCOMPTE_OBLIGATOIRE = -20653;
    public const C_ERR_CPT_OU_CHQ_OBLIGATOIRE = -20654;
    public const C_ERR_CONSTRUCT_ANIMAUX = -20673;
    public const C_ERR_NUMEROCARTE_INVALIDE = -20693;
    public const C_ERR_NUMEROCARTE_OBLIGATOIRE = -20694;
    public const C_ERR_NUMEROCARTE_DEJAUTILISE = -20695;
    public const C_ERR_NUMERO_CARTE_TRANCHE = -20697;
    public const C_ERR_NUMEROCARTE_INEXISTANT = -20698;
    public const C_ERR_MONTANTCARTE_POSITIF = -20699;
    public const C_ERR_MONTANTCARTE_SUPERIEUR = -20700;
    public const C_ERR_PARAM_ANNUL_INVALIDE = -20701;
    public const C_ERR_VENTE_DEJA_ANNULEE = -20702;
    public const C_ERR_LIGNE_VENTE_INTROUVABLE = -20713;
    public const C_ERR_LIGNE_VENTE_INVALIDE = -20714;
    public const C_ERR_NB_LIGNES_VENTE_INVALIDE = -20715;
    public const C_ERR_VENTE_INTROUVABLE = -20716;
    public const C_ERR_SEXE_OBLIGATOIRE = -20717;
    public const C_ERR_SEXE_INVALIDE = -20718;
    public const C_ERR_PRENOM_OBLIGATOIRE = -20719;
    public const C_ERR_PRENOM_INVALIDE = -20720;
    public const C_ERR_CIVILITE_INVALIDE = -20721;
    public const C_ERR_NUM_OP_OBLIGATOIRE = -20722;
    public const C_ERR_NUM_OP_INVALIDE = -20723;
    public const C_ERR_IDOPECOMM_OBLIGATOIRE = -20724;
    public const C_ERR_IDOPECOMM_INVALIDE = -20725;
    public const C_ERR_IDANIMAL_INVALIDE = -20726;
    public const C_ERR_IDSEXE_INVALIDE = -20727;
    public const C_ERR_IDRACE_INVALIDE = -20728;
    public const C_ERR_IDGROUPE_INVALIDE = -20729;
    public const C_ERR_IDHABITATION_INVALIDE = -20730;
    public const C_ERR_NB_ART_RETOUR_INVALIDE = -20731;
    public const C_ERR_TTC_ART_RETOUR_INVALIDE = -20732;
    public const C_ERR_ART_VENTE_INTROUVABLE = -20733;
    public const C_ERR_EMAIL_OU_MOBILE_OBLIG = -20754;
    public const C_ERR_IDVENTE_INVALIDE = -20755;
    public const C_ERR_CHIEN_DEJA_EXISTANT = -20756;
    public const C_ERR_SITEMAG_CODECLIENT_OBLIG = -20773;
    public const C_ERR_TTCVENTE_OBLIGATOIRE = -20774;
    public const C_ERR_BON_INEXISTANT = -20775;
    public const C_ERR_BON_ENCAISSE = -20776;
    public const C_ERR_IDCANAL_OBLIGATOIRE = -20777;
    public const C_ERR_IDCANAL_INEXISTANT = -20779;
    public const C_ERR_TEL_MOBILE_DEJA_UTIL = -20780;
    public const C_ERR_EMAIL_DEJA_UTIL = -20781;
    public const C_ERR_TYPECARBURANT_INEXISTANT = -20782;
    public const C_ERR_MARQUEVEHIC_INEXISTANT = -20783;
    public const C_ERR_TYPEVEHICULE_INEXISTANT = -20784;
    public const C_ERR_IDDOCUMENT_OBLIGATOIRE = -20785;
    public const C_ERR_IDPROSPECTION_OBLIG = -20786;
    public const C_ERR_IDCATALOGUE_OBLIGATOIRE = -20787;
    public const C_ERR_IDCATALOGUE_INEXISTANT = -20788;
    public const C_ERR_SITEPRINCIPAL_INVALIDE = -20805;
    public const C_ERR_MAGASINPRINCIPALINVALIDE = -20806;
    public const C_ERR_SUPERFICIE_INVALIDE = -20807;
    public const C_ERR_TEMPTRAJET_INVALIDE = -20808;
    public const C_ERR_IDSTATUTOCCUP_INVALIDE = -20809;
    public const C_ERR_IDTYPECHAUFFAGE_INVALIDE = -20810;
    public const C_ERR_IDLOISIR_INVALIDE = -20811;
    public const C_ERR_IDPOTAGISTE_INVALIDE = -20812;
    public const C_ERR_AGREMENT_INVALIDE = -20813;
    public const C_ERR_COOPERATIVE_INVALIDE = -20814;

    protected TranslatorInterface $translator;

    protected SessionInterface $session;
    protected \ReflectionClass $reflector;

    public function __construct(TranslatorInterface $translator, RequestStack $requestStack)
    {
        $this->translator = $translator;
        $this->session = $requestStack->getSession();
        $this->reflector = new \ReflectionClass($this);
    }

    public function hasError($id): bool
    {
        return in_array($id, $this->reflector->getConstants());
    }

    public function addError($id): void
    {
        if (!$this->hasError($id)) {
            $id = self::C_ERR_UNKNOWN;
        }
        $this->session->getFlashBag()->add('error', $this->translator->trans('errorMessage.'.$id, [], 'errors'));
    }
}
