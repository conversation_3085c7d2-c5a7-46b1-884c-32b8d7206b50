<?php

namespace App\Services;

use App\Services\Wallet\ZefidWallet;

class PkpassService
{
    public $_url = 'https://passbook.zefid.fr/generate';

    public $entity = 'ZeFid\PortailClientBundle\Entity\Pkpass';

    public $mapping = 'Pkpass';

    public $xmlRoot = 'pkpass';

    public $uniqueResponse = true;

    public function getQrcode(&$pkpass)
    {
        $pkpass = $this->actionGet($pkpass, -1);
    }

    public function calculateToken(ZefidWallet $pkpass)
    {
        $sel = $this->params->get('sel');
        $token = '?ENSEIGNE='.$pkpass->getIdEnseigne();
        $token .= '&CLIENT='.$pkpass->getIdUser();
        $token .= '&SALT='.$sel;
        $token = substr(md5($token), 9, 10);
        $pkpass->setToken($token);

        return $pkpass;
    }
}
