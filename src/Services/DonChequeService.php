<?php

namespace App\Services;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Service\Attribute\Required;

// @TODO : À adapter au nouveau fonctionnement
class DonChequeService
{
    private $magasin;

    private $dateencais;

    private $codecarte;

    private $numerocheque;

    private $idEnseigne;

    private readonly string $mdp;

    private ParameterBagInterface $parameterBag;

    #[Required]
    public function withParameterBag(ParameterBagInterface $parameterBag): void
    {
        $this->parameterBag = $parameterBag;
        if ($this->params['webservice_prod']) {
            $this->_url = $parameterBag->get('aquitem.doncheque.url_prod');
        } else {
            $this->_url = $parameterBag->get('aquitem.doncheque.url_preprod');
        }
        $this->mdp = $parameterBag->get('aquitem.doncheque.token.aquitem');
    }

    public function __construct(WsLogger $wsLogger, ErrorManagementService $errorManagementService, array $params, Security $security)
    {
        $this->disableError = true;
        $this->idEnseigne = $params['webservice_static_params']['programme']['value'];
    }

    public function create($entity = null, $magasin = null, $codecarte = null, $numerocheque = null)
    {
        $this->magasin = $magasin;
        $this->dateencais = (new \DateTime())->format('d/m/Y');
        $this->codecarte = $codecarte;
        $this->numerocheque = $numerocheque;

        return $this->action($entity);
    }

    public function callPost($url, $params)
    {
        $paramsDon = [];
        $paramsDon['programme'] = $this->idEnseigne;
        $paramsDon['magasin'] = $this->magasin;
        $paramsDon['dateencais'] = $this->dateencais;
        $paramsDon['codecarte'] = $this->codecarte;
        $paramsDon['numerocheque'] = $this->numerocheque;
        $paramsDon['token'] = $this->generateToken($paramsDon, $this->mdp);

        $contentData = $this->buildQuery($paramsDon);
        $httpContext = stream_context_create(
            [
                'http' => [
                    'method' => 'POST',
                    'header' => "
                        Connection: close\r\nContent-Length: ".
                        strlen((string) $contentData)."\r\nContent-type: application/x-www-form-urlencoded\r\n",
                    'content' => $contentData,
                ],
            ]
        );

        $numRequest = $this->wsLogger->logQueryStart();

        try {
            $flux = file_get_contents($url, false, $httpContext);
            $this->wsLogger->logQueryStop($numRequest, $url.'?'.$contentData, $params);
        } catch (\Exception $exception) {
            $this->wsLogger->logQueryStop($numRequest, $url.'?'.$contentData, $params);
            throw $exception;
        }

        return simplexml_load_string($flux, null, LIBXML_NOCDATA);
    }

    public function generateToken($params, $salt): string
    {
        ksort($params);

        return substr(md5(http_build_query($params, '', '&').'mdp='.$salt), 9, 10);
    }

    public function action($entity)
    {
        $vars = $this->parserService->entityToData($entity, $this->mapping);
        $vars = array_merge($vars, $this->getDefaultActionVars());
        if ($this->programme_generique) {
            $vars['PROGRAMME'] = urlencode((string) $this->programme_generique);
        }

        if ($this->x_api_key) {
            $vars['X-API-KEY'] = $this->x_api_key;
        }

        throw new \LogicException('TODO');
        $url = $this->buildUrl();
        $response = $this->callPost($url, $vars);
        if (!$this->checkResponseState($response) && !$this->disableError) {
            $this->manageResponseErrors($response);
        }

        return $response;
    }
}
