<?php

namespace App\Services\Cheque;

class ChequeAnniversaireSecurityChecker extends AbstractChequeFideliteSecurityChecker implements ChequeSecurityCheckerInterface
{
    public function isDownloadable(): bool
    {
        if (null !== $this->client->getDateFinValiditeDernierChequeAnniv()
            && $this->client->getDateFinValiditeDernierChequeAnniv() < $this->currentDate) {
            return false;
        }

        if (null != $this->client->getDateEncaissementDernierChequeAnniv()) {
            return false;
        }

        return true;
    }

    public function getFilename(): string
    {
        return 'echeque-anniv-'.$this->client->getNumDernierChequeAnniv().'.pdf';
    }
}
