<?php

namespace App\Services\Cheque;

class ChequeFideliteSecurityChecker extends AbstractChequeFideliteSecurityChecker implements ChequeSecurityCheckerInterface
{
    public function isDownloadable(): bool
    {
        if (null !== $this->client->getDateFinValiditeDernierCheque()
            && $this->client->getDateFinValiditeDernierCheque() < $this->currentDate) {
            return false;
        }

        if (null !== $this->client->getDateEncaissementDernierCheque()) {
            return false;
        }

        return true;
    }

    public function getFilename(): string
    {
        return sprintf('echeque-%s.pdf', $this->client->getNumDernierCheque());
    }
}
