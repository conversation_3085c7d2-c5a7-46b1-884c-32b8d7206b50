<?php

namespace App\Services\Cheque;

class ChequeBienvenueSecurityChecker extends AbstractChequeFideliteSecurityChecker implements ChequeSecurityCheckerInterface
{
    public function isDownloadable(): bool
    {
        if (null !== $this->client->getDateFinValiditeDernierChequeBvnu()
            && $this->client->getDateFinValiditeDernierChequeBvnu() < $this->currentDate) {
            return false;
        }

        if (null != $this->client->getDateEncasDernierChequeBvnu()) {
            return false;
        }

        return true;
    }

    public function getFilename(): string
    {
        return 'echeque-bienvenue-'.$this->client->getNumDernierChequeBvnu().'.pdf';
    }
}
