<?php

namespace App\Services\Cheque;

use App\Entity\Client;

class ChequeSecurityFactory implements ChequeSecurityFactoryInterface
{
    public function build(ChequeType $type, Client $client): ChequeSecurityCheckerInterface
    {
        return match ($type) {
            ChequeType::Bienvenue => new ChequeBienvenueSecurityChecker($client),
            ChequeType::Anniversaire => new ChequeAnniversaireSecurityChecker($client),
            ChequeType::Fidelite => new ChequeFideliteSecurityChecker($client),
        };
    }
}
