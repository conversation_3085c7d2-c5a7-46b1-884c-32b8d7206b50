<?php

namespace App\Services\Cheque;

use Symfony\Bundle\SecurityBundle\Security;

class AllDownloadSecurityChecker
{
    private array $res = [];

    public function __construct(
        private readonly ChequeSecurityFactoryInterface $chequeSecurityFactory,
        private readonly Security $security,
    ) {
    }

    public function listDownloadSecurityChecker(): array
    {
        if (!$user = $this->security->getUser()) {
            return [];
        }
        if (!empty($this->res)) {
            return $this->res;
        }
        foreach (ChequeType::cases() as $type) {
            $this->res[$type->value] = $this->chequeSecurityFactory->build($type, $user)->isDownloadable();
        }

        return $this->res;
    }
}
