<?php

namespace App\Services;

use App\DTO\WebserviceResponse;
use App\Entity\GlobalDefinitions;
use App\Exception\WebserviceEmptyException;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class NewGlobalDefinitionsService implements GlobalDefinitionsServiceInterface
{
    use StaticParamsTrait;

    public function __construct(
        #[Autowire(service: 'ws1')] private readonly HttpClientInterface $aquitemClient,
        private readonly SerializerInterface $serializer,
        private readonly NormalizerInterface $normalizer,
        private CacheInterface $cache,
        private readonly array $webserviceStaticParams,
    ) {
    }

    public function call()
    {
        return $this->cache->get('PhDClientStaticTablesSelect', function (ItemInterface $item) {
            $item->expiresAfter(3600);

            try {
                $response = $this->aquitemClient->request('GET', 'PhDClientStaticTablesSelect.php', [
                    'query' => $this->addStaticParams(),
                    'timeout' => 60,
                ])->getContent();
                $response = $this->serializer->deserialize($response, WebserviceResponse::class, 'xml');
            } catch (\Throwable $exception) {
                throw new WebserviceEmptyException(previous: $exception);
            }
            if (!$response || 'OK' !== $response->etat) {
                throw new WebserviceEmptyException();
            }

            return $this->normalizer->denormalize($response->resultat['staticTablesSelect'], $this->getDTOClass(), 'xml');
        });
    }

    public function getDTOClass(): string
    {
        return GlobalDefinitions::class;
    }
}
