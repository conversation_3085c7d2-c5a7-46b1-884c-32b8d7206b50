<?php

namespace App\Command;

use App\Tests\Fixtures\giropharm\MockLoader as MockLoaderBase;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use WireMock\Client\WireMock;

#[AsCommand(
    name: 'wiremock:start',
    description: 'Add a short description for your command',
)]
class WiremockStartCommand extends Command
{
    public function __construct(
        #[Autowire(env: 'ENSEIGNE')] private readonly string $enseigne,
        private readonly string $projectDir,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $wireMock = WireMock::create('wiremock', 8080);
        if (!$wireMock->isAlive()) {
            return Command::FAILURE;
        }

        $wireMock->reset();

        if ('' !== $this->enseigne) {
            $mockLoaderClass = ucfirst($this->enseigne)."\Tests\Fixtures\WireMock\MockLoader";

            if (class_exists($mockLoaderClass)) {
                $mockLoaderClass::configureMocks($wireMock, $this->projectDir);

                $io->success('MockLoader for enseigne '.$this->enseigne.' loaded');

                return Command::SUCCESS;
            }

            $io->warning('MockLoader not found for enseigne '.$this->enseigne);
        }

        MockLoaderBase::configureMocks($wireMock, $this->projectDir);

        $io->success('MockLoader for enseigne generic loaded');

        return Command::SUCCESS;
    }
}
