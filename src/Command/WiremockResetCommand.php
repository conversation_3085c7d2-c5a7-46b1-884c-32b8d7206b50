<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use WireMock\Client\WireMock;

#[AsCommand(
    name: 'wiremock:reset',
    description: 'Add a short description for your command',
)]
class WiremockResetCommand extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        new SymfonyStyle($input, $output);

        $wireMock = WireMock::create('wiremock', 8080);
        if (!$wireMock->isAlive()) {
            return Command::FAILURE;
        }

        $wireMock->reset();

        return Command::SUCCESS;
    }
}
