--- /dev/null
+++ ../src/Scrubber/JsonScrubber.php
@@ -12,6 +12,7 @@
         if (json_last_error() !== JSON_ERROR_NONE) {
             throw new \RuntimeException('Invalid JSON: ' . json_last_error_msg());
         }
+        $data = self::orderFieldNamesAlphabetically($data);
 
         // Handle members to ignore/scrub
         $this->handleMembers($data);
@@ -25,5 +26,22 @@
         $json = $this->applyAdditionalScrubbers($json);
 
         return $json;
+    }
+
+    private static function ksortRecursive(&$array, $sort_flags = SORT_REGULAR)
+    {
+        if (!is_array($array)) return false;
+        ksort($array, $sort_flags);
+        foreach ($array as &$arr) {
+            self::ksortRecursive($arr, $sort_flags);
+        }
+        return true;
+    }
+
+    private static function orderFieldNamesAlphabetically($array)
+    {
+        $result = $array;
+        self::ksortRecursive($result);
+        return $result;
     }
 }
