--- /dev/null
+++ ../Normalizer/AbstractObjectNormalizer.php
@@ -673,6 +673,23 @@
             }
         }
 
+        if (is_numeric($data) && XmlEncoder::FORMAT === $format) {
+            // encoder parsed them wrong, so they might need to be transformed back
+            foreach ($types as $type) {
+                $builtinType = $type->getBuiltinType();
+                switch ($builtinType) {
+                    case Type::BUILTIN_TYPE_STRING:
+                        return (string) $data;
+                    case Type::BUILTIN_TYPE_FLOAT:
+                        return (float) $data;
+                    case Type::BUILTIN_TYPE_INT:
+                        return (int) $data;
+                    case Type::BUILTIN_TYPE_BOOL:
+                        return (bool) $data;
+                }
+            }
+        }
+
         if ($isNullable) {
             return null;
         }
