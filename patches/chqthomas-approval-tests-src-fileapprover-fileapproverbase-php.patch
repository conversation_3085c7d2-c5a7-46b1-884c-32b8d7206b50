--- /dev/null
+++ ../src/FileApprover/FileApproverBase.php
@@ -37,8 +37,7 @@
 
     public function verify($received, ?Scrubber $scrubber = null, ?ApprovalWriter $writer = null): void
     {
-        $receivedText = $this->prepareReceivedText($received, $scrubber);
-        $writer = $this->prepareWriter($receivedText, $writer);
+        $writer = $this->prepareWriter($received, $writer);
 
         $files = $this->prepareFiles($writer);
         $this->writeReceivedFile($files['received'], $writer);
@@ -47,7 +46,7 @@
             $this->handleNewTest($files);
         }
 
-        $this->compareFiles($files, $receivedText);
+        $this->compareFiles($files, $received);
     }
 
     protected function prepareReceivedText($received, ?Scrubber $scrubber): string
@@ -108,9 +107,13 @@
         try {
             $isBinary = $this->isBinaryContent($receivedText) || $this->isBinaryContent($approvedText);
             $normalizedReceived = $isBinary ? $receivedText : $this->normalizeText($receivedText);
+//            $approvedText = $this->normalizeLineEndings($approvedText);
             $normalizedApproved = $isBinary ? $approvedText : $this->normalizeText($approvedText);
 
             if ($normalizedApproved !== $normalizedReceived) {
+//                dump('normalizedReceived', $normalizedReceived);
+//                dump('normalizedApproved', $normalizedApproved);
+//                die;
                 $this->reportFailure(
                     $receivedPath,
                     $approvedPath,
