/*
 * Welcome to your app's main JavaScript file!
 *
 * We recommend including the built version of this JavaScript file
 * (and its CSS file) in your base layout (base.html.twig).
 */

// any CSS you import will output into a single css file (app.css in this case)
// import './styles/foundation.min.css';
// import './styles/leaflet.css';
// import './styles/fontawesome-all.min.css';
// import './styles/styles.css';
// import './sass/styles.scss';
import '@fortawesome/fontawesome-free/css/all.css';
import './css/styles.css';
import 'leaflet/dist/leaflet.min.css';

// require jQuery normally
import $ from 'jquery';
import L from 'leaflet';
// https://github.com/Leaflet/Leaflet/issues/4968#issuecomment-483402699
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/leaflet/images/marker-icon-2x.png',
  iconUrl: '/leaflet/images/marker-icon.png',
  shadowUrl: '/leaflet/images/marker-shadow.png',
});

import('./js/main.js');
import('./js/streetmap.js');
import * as d3 from 'd3';

// create global $ and jQuery variables
window.$ = window.jQuery = $;
window.d3 = d3;
