<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $enseigne = $_ENV['ENSEIGNE'] ?? null;
    $paths = [];
    $defaultPath = '%kernel.project_dir%/translations';
    if ($enseigne) {
        $pathTranslationsDirEnseigne = sprintf(__DIR__.'/../../enseigne/%s/translations/', $enseigne);
        if (is_dir($pathTranslationsDirEnseigne)) {
            $defaultPath = sprintf('%%kernel.project_dir%%/enseigne/%s/translations/', $enseigne);
            $paths[] = $defaultPath;
        }
    }
    $paths[] = sprintf('%%kernel.project_dir%%/translations/', $enseigne);

    $containerConfigurator->extension('framework', [
        'default_locale' => 'fr',
        'translator' => [
            'default_path' => $defaultPath,
            'fallbacks' => [
                'fr',
            ],
            'providers' => null,
            'paths' => $paths,
        ],
    ]);
};
