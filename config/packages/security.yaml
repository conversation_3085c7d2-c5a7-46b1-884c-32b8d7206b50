security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
      App\Entity\Client: plaintext
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        portailClient:
            id: App\Security\User\AquitemUserProvider

    firewalls:
        dev:
            pattern:  ^/(_(profiler|wdt)|css|images|js)/
            security: false
        secured_area:
            lazy: true
            pattern:    ^/
            provider: portailClient
            custom_authenticators:
                - enseigne_authenticator
            form_login:
                login_path: /
                check_path: /
                # default_target_path: /vue/
                username_parameter: login[_username]
                password_parameter: login[_password]
            logout:
                path:   /logout
                target: /

            entry_point: App\Security\CaptchaAuthenticator

            #http_basic:
            #    realm: "Secured Demo Area"

    access_control:
        - { path: ^/$, roles: [PUBLIC_ACCESS] }
        - { path: ^/statut/, roles: [PUBLIC_ACCESS] }
        - { path: ^/activation, roles: [PUBLIC_ACCESS] }
        - { path: ^/, roles: [ROLE_USER] }
        # - { path: ^/login, roles: PUBLIC_ACCESS, requires_channel: https }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
