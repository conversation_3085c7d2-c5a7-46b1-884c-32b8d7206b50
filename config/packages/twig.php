<?php

declare(strict_types=1);

use App\Services\FeatureFlipping\FeatureCheckerInterface;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return static function (ContainerConfigurator $containerConfigurator): void {
    $paths = [];

    $enseigne = $_ENV['ENSEIGNE'] ?? null;
    if ($enseigne) {
        $pathTemplateDirEnseigne = sprintf(__DIR__.'/../../enseigne/%s/templates/', $enseigne);
        if (is_dir($pathTemplateDirEnseigne)) {
            $paths[] = $pathTemplateDirEnseigne;
        }
    }

    $containerConfigurator->extension('twig', [
        'default_path' => '%kernel.project_dir%/templates',
        'paths' => $paths,
        'debug' => '%kernel.debug%',
        'strict_variables' => '%kernel.debug%',
        'exception_controller' => null,
        'globals' => [
            'featureChecker' => service(FeatureCheckerInterface::class),
        ],
        'form_themes' => [
            '@MeteoConceptHCaptcha/hcaptcha_form.html.twig',
        ],
        'file_name_pattern' => '*.twig',
    ]);
    if ('test' === $containerConfigurator->env()) {
        $containerConfigurator->extension('twig', [
            'strict_variables' => true,
        ]);
    }
};
