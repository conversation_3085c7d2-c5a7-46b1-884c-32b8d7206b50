<?php

declare(strict_types=1);

use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

return static function (ContainerConfigurator $containerConfigurator): void {
    $enseigne = $_ENV['ENSEIGNE'] ?? null;
    $assetsPaths = ['assets/'];
    $importMapPath = '%kernel.project_dir%/importmap.php';
    $vendorDir = '%kernel.project_dir%/assets/vendor';
    if ($enseigne) {
        $assetsPaths = ['enseigne/'.$enseigne.'/assets/'];
        $importMapPath = '%kernel.project_dir%/enseigne/'.$enseigne.'/importmap.php';
        $vendorDir = '%kernel.project_dir%/enseigne/'.$enseigne.'/assets/vendor';
    }

    $containerConfigurator->extension('framework', [
        'asset_mapper' => [
            'paths' => $assetsPaths,
            'missing_import_mode' => 'strict',
            'importmap_path' => $importMapPath,
            'vendor_dir' => $vendorDir,
        ],
    ]);
    if ('prod' === $containerConfigurator->env()) {
        $containerConfigurator->extension('framework', [
            'asset_mapper' => [
                'missing_import_mode' => 'warn',
            ],
        ]);
    }
};
