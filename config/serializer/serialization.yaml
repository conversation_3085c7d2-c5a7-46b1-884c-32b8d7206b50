App\Entity\GlobalDefinitions:
  attributes:
    magasins:
      serialized_name: collectionMagasin
    civilites:
      serialized_name: collectionCivilite
    paysClients:
      serialized_name: collectionPaysClient
    params:
      serialized_name: collectionParam
    genres:
      serialized_name: collectionGenre

App\DTO\CollectionMagasin:
  attributes:
    magasins:
      serialized_name: "magasin"

App\Entity\Magasin:
  attributes:
    code:
      serialized_name: "@MAGASIN"

App\Entity\Client:
  attributes:
    mouvementPoints:
      serialized_name: "collectionMouvement"
    nbrePoints:
      serialized_name: "@CAGNOTTE"
    envoiCourrierInterne:
      serialized_name: "@ENVOIECOURRIERINTERNE"
    envoiCourrierExterne:
      serialized_name: "@ENVOIECOURRIEREXTERNE"

App\Entity\MouvementPoint:
  attributes:
    nbPoints:
      serialized_name: "@MONTANT"

App\Entity\Civilite:
  attributes:
    libelle:
      serialized_name: "@CIVILITE"
    id:
      serialized_name: "@CODECIVILITE"

App\Entity\Genre:
   attributes:
     libelle:
       serialized_name: "@LIBELLEGENRE"
     id:
       serialized_name: "@CODEGENRE"

App\Entity\PaysClient:
  attributes:
    libelle:
      serialized_name: "@NOMPAYS"
    id:
      serialized_name: "@CODEABREGE"

App\Entity\Param:
  attributes:
    id:
      serialized_name: "@IDPARAM"
