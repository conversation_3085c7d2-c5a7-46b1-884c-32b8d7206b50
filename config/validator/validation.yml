App\Entity\Client:
  constraints:
    - Callback: {callback: valideDateNaissance, groups: [validationEPC]}
    - Callback: {callback: valideCodePostalFr, groups: [validationEPC]}
    - Callback: {callback: valideMailOrMobile, groups: [validationEPC]}
  properties:
    codeCarte: ~
    civilite:
      - NotBlank: {groups: [validationEPC]}
    nom:
      - NotBlank: {groups: [validationEPC]}
      - Length: {min: 1, max: 32, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength32', groups: [validationEPC]}
    prenom:
      - Length: {min: 1, max: 20, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength20', groups: [validationEPC]}
    dateCreation:
      - NotBlank: {groups: [validationEPC]}
    nbreEnfants:
      - Regex: {pattern: '/^\d+$/', groups: [validationEPC]}
    numero:
      - Regex: {pattern: '/^(\d+)+/', groups: [validationEPC]}
      - Length: {min: 1, max: 20, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength20', groups: [validationEPC]}
    telephoneMobile:
      - NotBlank: {groups: [validationEPC]}
      - Regex: {pattern: '/^\d{10}$/', groups: [validationEPC]}
      - Length: {min: 1, max: 15, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength15', groups: [validationEPC]}
    email:
      - NotBlank: {groups: [validationEPC]}
      - Email: {groups: [validationEPC]}
      - Length: {min: 1, max: 90, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength90',groups: [validationEPC]}
    codepostal:
      - NotBlank: {groups: [validationEPC]}
    ville:
      - NotBlank: {groups: [validationEPC]}
      - Length: {min: 1, max: 40, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength40', groups: [validationEPC]}
#    centreInteretBienEtres:
#      - Count: {max: 3, maxMessage: 'client.errorCentreInterets', groups: [validationEPC]}
    codePaysClient: ~
    voie:
      - Length: {min: 1, max: 40, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength40', groups: [validationEPC]}
    lieuDit:
      - Length: {min: 1, max: 40, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength40', groups: [validationEPC]}
    batiment:
      - Length: {min: 1, max: 38, minMessage: 'client.errorMinLength1', maxMessage: 'client.errorMaxLength38', groups: [validationEPC]}
