<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
    Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['dev' => true, 'test' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    Symfony\Bundle\DebugBundle\DebugBundle::class => ['dev' => true],
    Symfony\Bundle\MakerBundle\MakerBundle::class => ['dev' => true],
    Skies\QRcodeBundle\SkiesQRcodeBundle::class => ['all' => true],
    Alienor\RequeteurAdresseBundle\AlienorRequeteurAdresseBundle::class => ['all' => true],
    Alienor\PDFBundle\AlienorPDFBundle::class => ['all' => true],
    Alienor\LoggerWebserviceBundle\AlienorLoggerWebserviceBundle::class => ['all' => true],
    Alienor\SymfonyProfilerExtraGit\SymfonyProfilerExtraGit::class => ['dev' => true],
    MeteoConcept\HCaptchaBundle\MeteoConceptHCaptchaBundle::class => ['all' => true],
];
