<?php

declare(strict_types=1);

namespace App\Tests;

use _PHPStan_95d365e52\Nette\Utils\AssertionException;
use App\Kernel;
use <PERSON><PERSON>us\Dindent\Indenter;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\HttpKernel\KernelInterface;

class NoIndenter extends Indenter
{
    public function indent($input)
    {
        return $input;
    }
}

class ApiTestCase extends WebTestCase
{
    protected $indenter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->indenter = new Indenter();
    }

    #[\Override]
    protected static function createKernel(array $options = []): KernelInterface
    {
        $env = $options['environment'] ?? $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'test';
        $debug = $options['debug'] ?? (bool) ($_ENV['APP_DEBUG'] ?? $_SERVER['APP_DEBUG'] ?? true);

        return new Kernel($env, $debug, $_ENV['ENSEIGNE']);
    }

    /**
     * @return array|string|string[]|null
     */
    public function scrubCaptcha(array|string|null $reindentedBlock): string|array|null
    {
        return preg_replace('/\d \+ \d =/', '"__captcha__"', $reindentedBlock);
    }

    /**
     * @return array|string|string[]|null
     */
    public function scrubTokens(string $reindentedBlock): string|array|null
    {
        return preg_replace('/"[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*"/', '"__token__"', $reindentedBlock);
    }

    /**
     * @throws \Gajus\Dindent\Exception\RuntimeException
     * @throws AssertionException
     */
    public function getHtmlBlock(?Crawler $crawler, string $blockId): string
    {
        try {
            return $crawler->filter('[data-block-id="'.$blockId.'"]')->outerHtml();
        } catch (\InvalidArgumentException $e) {
            throw new \InvalidArgumentException(sprintf('html block %s not found', $blockId));
        }
    }

    protected function scrubAssetMapperLinks(string $reindentedBlock): string
    {
        return preg_replace(
            '/-[^\/"\']+\.(js|css)/',
            '-__hash__.${1}',
            $reindentedBlock
        );
    }
}
