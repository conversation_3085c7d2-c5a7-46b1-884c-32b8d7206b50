<?php

namespace App\Tests;

use App\Entity\GlobalDefinitions;
use App\Services\NewGlobalDefinitionsService;
use ChqThomas\ApprovalTests\Approvals;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Cache\Adapter\NullAdapter;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Cache\CacheInterface;

class NewGlobalDefinitionsWebserviceTest extends ApiTestCase
{
    private NewGlobalDefinitionsService $globalDefinitionsService;

    #[\Override]
    protected function setUp(): void
    {
        $callback = (fn ($method, $url, $options) => new MockResponse(file_get_contents(__DIR__.'/Entity/Ressources/correctGlobalDefinitions.xml')));

        $httpClient = new MockHttpClient($callback);
        $serializer = self::getContainer()->get(SerializerInterface::class);
        $normalizer = self::getContainer()->get(NormalizerInterface::class);
        //        $cache = self::getContainer()->get(CacheInterface::class);
        $cache = new NullAdapter();
        $security = self::getContainer()->get(Security::class);
        $this->globalDefinitionsService = new NewGlobalDefinitionsService($httpClient, $serializer, $normalizer, $cache,
            ['programme' => ['key' => 'programme', 'value' => self::getContainer()->getParameter('programme')],
            ]);
        $this->globalDefinitionsService->setSecurity($security);
    }

    public function test_retrieves_magasins(): void
    {
        /** @var GlobalDefinitions $gd */
        $gd = $this->globalDefinitionsService->call();

        Approvals::verify(ObjectFormatter::format($gd->getMagasins()));
    }

    public function test_retrieves_civilites(): void
    {
        /** @var GlobalDefinitions $gd */
        $gd = $this->globalDefinitionsService->call();

        Approvals::verify(ObjectFormatter::format($gd->getCivilites()));
    }

    public function test_retrieves_pays_clients(): void
    {
        /** @var GlobalDefinitions $gd */
        $gd = $this->globalDefinitionsService->call();

        Approvals::verify(ObjectFormatter::format($gd->getPaysClients()));
    }

    public function test_retrieves_params(): void
    {
        /** @var GlobalDefinitions $gd */
        $gd = $this->globalDefinitionsService->call();

        Approvals::verify(ObjectFormatter::format($gd->getParams()));
    }
}
