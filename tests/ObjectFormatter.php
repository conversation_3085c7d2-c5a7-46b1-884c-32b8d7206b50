<?php

namespace App\Tests;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\PropertyAccess\PropertyAccessor;

class ObjectFormatter
{
    private static $propertyAccessor;

    public static function formatValue(mixed $value): string
    {
        switch (gettype($value)) {
            case 'string':
                return '"'.addslashes($value).'"';
            case 'boolean':
                return $value ? 'true' : 'false';
            case 'NULL':
                return 'null';
            case 'integer':
            case 'double':
                return (string) $value;
            case 'object':
                if ($value instanceof \DateTime) {
                    return '"'.$value->format('Y-m-d').'"';
                }

                return '"'.addslashes((string) $value).'"';
            default:
                return '"'.addslashes((string) $value).'"';
        }
    }

    public static function format(mixed $data, int $depth = 1): string
    {
        if (is_array($data)) {
            return self::formatArray($data, $depth);
        }

        if ($data instanceof ArrayCollection) {
            return self::formatArray($data->toArray(), $depth);
        }

        if (is_object($data)) {
            return self::formatObject($data, $depth);
        }

        return self::formatValue($data);
    }

    private static function formatArray(array $data, int $depth): string
    {
        if ([] === $data) {
            return '[]';
        }

        $formattedItems = array_map(fn ($item) => self::format($item, $depth + 1), $data);
        $indentedItems = implode(",\n".str_repeat("\t", $depth), $formattedItems);

        return "[\n".str_repeat("\t", $depth).$indentedItems."\n".str_repeat("\t", $depth - 1).']';
    }

    private static function formatObject(object $data, int $depth): string
    {
        if ($data instanceof \DateTime) {
            return '"'.$data->format('Y-m-d').'"';
        }

        $lines = [];
        $allLines = [];
        $reflectionStack = [];
        $currentClass = new \ReflectionClass($data);
        $reflectionClass = $currentClass;

        while ($currentClass) {
            array_unshift($reflectionStack, $currentClass);
            $currentClass = $currentClass->getParentClass();
        }

        $handledProperties = [];
        foreach ($reflectionStack as $reflectionClass) {
            $allLines = array_merge($allLines, self::getPropertiesLines($reflectionClass, $data, $depth, $handledProperties));
        }

        $lines[] = $reflectionClass->getName().' {';
        $lines = array_merge($lines, $allLines);
        $lines[] = str_repeat("\t", $depth - 1).'}';

        return implode("\n", $lines);
    }

    private static function getPropertiesLines(\ReflectionClass $reflectionClass, object $data, int $depth, array &$handledProperties = []): array
    {
        $lines = [];
        foreach ($reflectionClass->getProperties() as $property) {
            if (!in_array($property->getName(), $handledProperties)) {
                //                if($property->getName() === "envoiCourrierInterne") {
                //                    dd(
                //                        $property->getName(),
                //                        self::getPropertyAccessor()->isReadable($data, $property->getName()),
                //                        $property->isInitialized($data),
                //                        self::format(self::getPropertyAccessor()->getValue($data, $property->getName()), $depth + 1)
                //                    );
                //                }
                //                $value = $property->isInitialized($data) ? self::format($property->getValue($data), $depth + 1) : '(not initialized)';
                if (self::getPropertyAccessor()->isReadable($data, $property->getName())) {
                    $value = self::format(self::getPropertyAccessor()->getValue($data, $property->getName()), $depth + 1);
                } elseif ($property->isInitialized($data)) {
                    $value = self::format('(not readable)', $depth + 1);
                } else {
                    $value = self::format('(not initialized)', $depth + 1);
                }

                $lines[] = str_repeat("\t", $depth).$property->getName().': '.$value.',';
                $handledProperties[] = $property->getName();
            }
        }

        return $lines;
    }

    public static function getPropertyAccessor(): PropertyAccessor
    {
        if (!self::$propertyAccessor instanceof PropertyAccessor) {
            self::$propertyAccessor = new PropertyAccessor();
        }

        return self::$propertyAccessor;
    }
}
