<?php

namespace App\Tests\Fixtures\giropharm;

use WireMock\Client\WireMock;

class MockLoader
{
    public static function configureMocks(WireMock $wireMock, string $projectDir): void
    {
        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientStaticTablesSelect.php'))
            ->withQueryParam('programme', WireMock::equalTo('fakeprogrammekey'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($projectDir.'/tests/Fixtures/giropharm/tablestatic-select-successful.xml'))));

        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientClientSelect.php'))
            ->with<PERSON><PERSON>yParam('CodeCarte', WireMock::equalTo('1001001000012'))
            ->with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('DateNaissance', WireMock::equalTo('25/10/1949'))
            ->with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('programme', WireMock::equalTo('fakeprogrammekey'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($projectDir.'/tests/Fixtures/giropharm/client-select-successful.xml'))));
    }
}
