[
	App\Entity\Param {
		id: "CALCULPTSARRONDI",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "DATAVIZ_ACTIVE",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "SEUIL_LIMITEEXCLUSION",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "WALLET_URL",
		libelle: null,
		valeur: "https://wallet.zefid.fr/ws/card/[IDENSEIGNE]/[CODECARTE]/[TOKEN]",
	},
	App\Entity\Param {
		id: "JOUR_DECLENCHE_ANNIVERSAIRE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "GESTPROMOPLATEFORME",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "GEST_PHARMAFERMEE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "WALLET_SALT",
		libelle: null,
		valeur: "Giropharm9XvwgdwFmFctlX2PySOXrjK0tHJThK3fC3kkD0VV9vSAxuK500J9b3HrlIQkwIub",
	},
	App\Entity\Param {
		id: "UTILISATION_CARTECADEAU",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "CODECOUPON_ANNIVERSAIRE",
		libelle: null,
		valeur: "9001000000008",
	},
	App\Entity\Param {
		id: "SEUILMAGASIN",
		libelle: null,
		valeur: "1000",
	},
	App\Entity\Param {
		id: "AJOUT_DECIMALE_BATCHEQUE",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "CARTEKDOMIN",
		libelle: null,
		valeur: "15",
	},
	App\Entity\Param {
		id: "CARTEKDOMAX",
		libelle: null,
		valeur: "500",
	},
	App\Entity\Param {
		id: "CARTEKDOVALIDITEMOIS",
		libelle: null,
		valeur: "12",
	},
	App\Entity\Param {
		id: "WALLET_MESSAGE_BIENVENUE",
		libelle: null,
		valeur: "Merci, retrouvez votre carte de fidélité et suivez votre compte depuis votre mobile.",
	},
	App\Entity\Param {
		id: "PREFIXECHEQUE",
		libelle: null,
		valeur: "10010",
	},
	App\Entity\Param {
		id: "SEQ_NUMEROCHEQUE",
		libelle: null,
		valeur: "SEQ_NUMCHEQUE_10",
	},
	App\Entity\Param {
		id: "TAILLE_MONTANT_AVANT_VIRGULE",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "TAILLE_MONTANT_APRES_VIRGULE",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "EUROSOFFCLIENTSDECROISSANTS",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "NBMOISOFFCLIENTSDECROISSANTS",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "TYPEMONTANTCART",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "GEST_BIENVENUE",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "MONTANTRELANCEDECROISSANT",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "WS_ECRITURE_LOG",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "WS_ALL_ECRITURE_LOG",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "WS_CAISSE_ECRITURE_LOG",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "WS_CLIENT_ECRITURE_LOG",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "WS_ENSEIGNE_ECRITURE_LOG",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "PROMOTION_VALEUR_MULTIPLE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "CODE_RETOUR_CHARIOT",
		libelle: null,
		valeur: "\\n",
	},
	App\Entity\Param {
		id: "NBPASSAGES_BIENVENUE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "GESTCHQMULTICIV",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "MTCHHOMME",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "MTCHFEMME",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "NBRPOINTSPOURCHEQUEHOMME",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "NB_MOIS_BIENVENUE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_JOURS_BIENVENUE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "REGLES_BIENVENUE",
		libelle: null,
		valeur: "+",
	},
	App\Entity\Param {
		id: "COEFF_BIENVENUE",
		libelle: null,
		valeur: "20",
	},
	App\Entity\Param {
		id: "POINTS_NEGATIFS",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "FEN_ART_ELIG_INDIGO",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "ART_PROMO_DEPORTE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NBRPOINTSPOURCHEQUEFEMME",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "ACTIVE_LOGSWS",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "NBMOIS_CONSERVATION_LOGSWS",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "PROFIL_COMPLET",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "CODECOUPON_BIENVENUE",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "GEST_EMAILCAGNOTTE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "SEUIL_EMAILCAGNOTTE",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "GEST_ANNIVERSAIRE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_MOIS_ANNIVERSAIRE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_JOURS_ANNIVERSAIRE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "MT_MAXCHQ_AUTORISEHOMME",
		libelle: null,
		valeur: "15",
	},
	App\Entity\Param {
		id: "MT_MAXCHQ_AUTORISEFEMME",
		libelle: null,
		valeur: "15",
	},
	App\Entity\Param {
		id: "REGLES_ANNIVERSAIRE",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "COEFF_ANNIVERSAIRE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "CODETYPEMAILING_EMAILCAGNOTTE",
		libelle: null,
		valeur: "232",
	},
	App\Entity\Param {
		id: "GEST_RAZCAGNOTTE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "PERIODE_RAZCAGNOTTE",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "NBMOIS_RAZMONTANTCART",
		libelle: null,
		valeur: "13",
	},
	App\Entity\Param {
		id: "GEST_RAZMONTANTCART",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "MSG_VTE_BIENV_INDIGO",
		libelle: null,
		valeur: "Bienvenue",
	},
	App\Entity\Param {
		id: "ARTICLE_ELIGIBLE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_MOIS_CHQFID_PAPIER",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "NB_MOIS_CHQFID_EMAIL",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "MSG_VTE_ANNIV_INDIGO",
		libelle: null,
		valeur: "Coupon Anniversaire",
	},
	App\Entity\Param {
		id: "NB_MOIS_CHQFID_SMS",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "NB_JOURS_CHQFID_PAPIER",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_JOURS_CHQFID_EMAIL",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "NB_JOURS_CHQFID_SMS",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "CODECARTE_NONIDENTIFIEE",
		libelle: null,
		valeur: "9999999999994",
	},
	App\Entity\Param {
		id: "CREATION_ARTICLE_NONREF",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "SEUIL_DECLENCHEMENT",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "SEUIL_DECLENCHEMENTFEMME",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "SEUIL_DECLENCHEMENTHOMME",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "INVERSION_ADRESSE",
		libelle: null,
		valeur: "O",
	},
	App\Entity\Param {
		id: "BLOCAGE_MAJ_ANONYMOUS",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "IDANIMAL_AUTRE",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "RGPD_NBMOISSANSACHAT",
		libelle: null,
		valeur: "36",
	},
	App\Entity\Param {
		id: "GESTION_MODIF_ARTICLE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "ARTICLE_ELIGIBILITE_DEFAUT",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "CUMUL_AVANTAGE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "SEUIL_ART_NO_PTS",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "NOMPROGRAMME",
		libelle: null,
		valeur: "Giropharm Fidelite",
	},
	App\Entity\Param {
		id: "POSSIBILITE_MODIFIER_CLIENT",
		libelle: null,
		valeur: "O",
	},
	App\Entity\Param {
		id: "EXPORT_FICHIER_CLIENTS",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "TAILLESITE",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "TAILLEMAGASIN",
		libelle: null,
		valeur: "9",
	},
	App\Entity\Param {
		id: "TAILLECODEPOSTALFRANCE",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "OPERATIONBOOSTENCOURS",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "MTACHPOINTS",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "NBRPOINTSPOURCHEQUE",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "MTCH",
		libelle: null,
		valeur: "5",
	},
	App\Entity\Param {
		id: "MTCHAVERIF",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "EXPORT_FICHIER_VENTES",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "FORCE_ELIGIBILITE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "CODEFAMMILLE_ARTICLE_NONREF",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "DATEPASSAGEEURO",
		libelle: null,
		valeur: "01/01/2002",
	},
	App\Entity\Param {
		id: "CODELABORATOIRE_ARTICLE_NONREF",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "ACTIVER_REGLES_GESTION_COUPONS",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "ENVOIE_CHEQUE_FTP_INDIGO",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "ETAPE_REFERENCE",
		libelle: null,
		valeur: "18",
	},
	App\Entity\Param {
		id: "LANGUEDEFAUT",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "NbJoursRejetVentes",
		libelle: null,
		valeur: "900",
	},
	App\Entity\Param {
		id: "TAILLECODEPOSTAL",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "MONNAIEPAYS",
		libelle: null,
		valeur: "?",
	},
	App\Entity\Param {
		id: "MONNAIEPAYS2",
		libelle: null,
		valeur: "F",
	},
	App\Entity\Param {
		id: "ANCIENMONTANTPOURUNPOIT",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "CIVILITEDEFAUT",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "TAILLENUMTELEPHONE",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "NBRE_LOT_CHQ_RETOURNE",
		libelle: null,
		valeur: "1500",
	},
	App\Entity\Param {
		id: "DATECREATION",
		libelle: null,
		valeur: "01/01/1998",
	},
	App\Entity\Param {
		id: "NBRE_LIGNES_RETOURNEES",
		libelle: null,
		valeur: "100",
	},
	App\Entity\Param {
		id: "NIVEAU_UTILISE_CSP",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "MT_A_VERIFIER_TRAITE_VENTES",
		libelle: null,
		valeur: "1000",
	},
	App\Entity\Param {
		id: "NUMERO_CHEQUE_MIN_A_VERIFIER",
		libelle: null,
		valeur: "1500000",
	},
	App\Entity\Param {
		id: "CALCUL_RETOUR_MAILING",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "DATE_CHGT_CALCUL_PTS1",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "MTACHPOINTS_OLD1",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "DATE_CHGT_MONTANT_CREATION",
		libelle: null,
		valeur: "10/08/2020",
	},
	App\Entity\Param {
		id: "ANCIEN_MONTANT_DISQUETTE_BRUTE",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "GEST_PRESTATAIRE",
		libelle: null,
		valeur: "O",
	},
	App\Entity\Param {
		id: "NBJOURS_NPAI_RAPPORT_CHQ",
		libelle: null,
		valeur: "22",
	},
	App\Entity\Param {
		id: "DUREE VALIDITE CHEQUES ANNIV",
		libelle: null,
		valeur: "30",
	},
	App\Entity\Param {
		id: "HEURE_SMS",
		libelle: null,
		valeur: "09:00:00",
	},
	App\Entity\Param {
		id: "SEUIL_BOOST",
		libelle: null,
		valeur: "75",
	},
	App\Entity\Param {
		id: "IDMAILING_ETAPES",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "MAILING_LIMIT",
		libelle: null,
		valeur: "1000000",
	},
	App\Entity\Param {
		id: "CODETYPEGENE",
		libelle: null,
		valeur: "12",
	},
	App\Entity\Param {
		id: "TAILLE_MAILING",
		libelle: null,
		valeur: "10",
	},
	App\Entity\Param {
		id: "MAXINUMETAPECHQ",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "MT_MAXCHQ_AUTORISE",
		libelle: null,
		valeur: "15",
	},
	App\Entity\Param {
		id: "NBMOISMODIFTRTFICNPAI",
		libelle: null,
		valeur: "2",
	},
	App\Entity\Param {
		id: "PCT_CAGNOTTAGE",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "SEUIL_UTILISATION_CAGNOTTE",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "AGREMENT",
		libelle: null,
		valeur: null,
	},
	App\Entity\Param {
		id: "LIBELLETYPEMONTANTCART",
		libelle: null,
		valeur: "POINTS",
	},
	App\Entity\Param {
		id: "GESTOPTINPROGRAMME",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "MAGASIN_VIRTUEL",
		libelle: null,
		valeur: "999999999",
	},
	App\Entity\Param {
		id: "GEST_MAGASIN_VIRTUEL",
		libelle: null,
		valeur: "1",
	},
	App\Entity\Param {
		id: "BONUS_POINTS_WALLET",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "GEST_CREATION_DEMAT",
		libelle: null,
		valeur: "0",
	},
	App\Entity\Param {
		id: "SEUIL_EXCLUSION_PLATEFORME",
		libelle: null,
		valeur: "9999",
	},
	App\Entity\Param {
		id: "SSO_OPENID_URLAQUITEMDECTOKEN",
		libelle: null,
		valeur: "http://webl0036-back.aqui-wsphp-preprod.int.aquitem.net/CheckAccessToken.php",
	}
]