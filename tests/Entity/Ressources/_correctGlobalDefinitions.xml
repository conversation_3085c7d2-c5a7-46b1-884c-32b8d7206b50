<?xml version="1.0" encoding="utf-8"?>
<xml>
    <etat>OK</etat>
    <resultat>
    <collectionCivilite>
        <civilite CODELANGUE="1" CODECIVILITE="1" CIVILITE="MME" CIVILITEENTIER="MADAME" COMPLEMENTCIVILITE="Chere"/>
        <civilite CODELANGUE="1" CODECIVILITE="3" CIVILITE="M." CIVILITEENTIER="MONSIEUR" COMPLEMENTCIVILITE="Cher"/>
    </collectionCivilite>
    <collectionPaysClient>
        <paysClient CODE="ITA" NOMPAYS="ITALIE" CODEABREGE="IT"/>
        <paysClient CODE="JAM" NOMPAYS="JAMAIQUE" CODEABREGE="JM"/>
        <paysClient CODE="ZWE" NOMPAYS="ZIMBABWE" CODEABREGE="ZW"/>
    </collectionPaysClient>
    <collectionMagasin>
        <magasin MAGASIN="999999042" LIBELLE="TEST WINPHARMA" CODEPOSTAL="33110" VILLE="LE BOUSCAT" ADRESSE1="375 AV DE TIVOLI" LONGITUDE="-,5999047" LATITUDE="44,8742295">
            <SITE>0000000010</SITE>
        </magasin>
        <magasin MAGASIN="999999045" LIBELLE="TEST WINPHARMA" CODEPOSTAL="33110" VILLE="LE BOUSCAT" ADRESSE1="375 AV DE TIVOLI" LONGITUDE="-,5999047" LATITUDE="44,8742295">
            <SITE>0000000010</SITE>
        </magasin>
        <magasin MAGASIN="999999041" LIBELLE="TEST WINPHARMA" CODEPOSTAL="03311" VILLE="LE BOUSCAT" ADRESSE1="375 AV DE TIVOLI" LONGITUDE="-,5999047" LATITUDE="44,8742295">
            <SITE>0000000010</SITE>
        </magasin>
    </collectionMagasin>
    <collectionAnimal>
        <animal IDANIMAL="1" LIBELLEANIMAL="CHIEN"/>
        <animal IDANIMAL="2" LIBELLEANIMAL="CHAT"/>
        <animal IDANIMAL="3" LIBELLEANIMAL="NON"/>
        <animal IDANIMAL="5" LIBELLEANIMAL="AUTRES"/>
    </collectionAnimal>
    <collectionCategorieArticle>
        <categorieArticle CODECATEGORIE="1" LIBELLECATEGORIE="Soins du visage"/>
        <categorieArticle CODECATEGORIE="2" LIBELLECATEGORIE="Démaquillants et nettoyants visage"/>
        <categorieArticle CODECATEGORIE="3" LIBELLECATEGORIE="Anti-âge"/>
        <categorieArticle CODECATEGORIE="5" LIBELLECATEGORIE="Capillaires ou soins des cheveux"/>
        <categorieArticle CODECATEGORIE="6" LIBELLECATEGORIE="Compléments alimentaires et vitamines"/>
        <categorieArticle CODECATEGORIE="7" LIBELLECATEGORIE="Diététique minceur"/>
        <categorieArticle CODECATEGORIE="8" LIBELLECATEGORIE="Diététique du sportif"/>
        <categorieArticle CODECATEGORIE="9" LIBELLECATEGORIE="Hygiène bucco-dentaire"/>
        <categorieArticle CODECATEGORIE="10" LIBELLECATEGORIE="Hygiène corporelle"/>
        <categorieArticle CODECATEGORIE="12" LIBELLECATEGORIE="Maquillage"/>
        <categorieArticle CODECATEGORIE="14" LIBELLECATEGORIE="Parfums et coffrets"/>
        <categorieArticle CODECATEGORIE="15" LIBELLECATEGORIE="Phytothérapie/Aromathérapie"/>
        <categorieArticle CODECATEGORIE="16" LIBELLECATEGORIE="Premiers soins"/>
        <categorieArticle CODECATEGORIE="17" LIBELLECATEGORIE="Produits Vétérinaire"/>
        <categorieArticle CODECATEGORIE="18" LIBELLECATEGORIE="Soins du corps"/>
        <categorieArticle CODECATEGORIE="19" LIBELLECATEGORIE="Soins hommes"/>
        <categorieArticle CODECATEGORIE="21" LIBELLECATEGORIE="Soins bébé"/>
    </collectionCategorieArticle>
    <collectionGenre>
        <genre CODEGENRE="G" LIBELLEGENRE="Garçon"/>
        <genre CODEGENRE="F" LIBELLEGENRE="Fille"/>
    </collectionGenre>
    <collectionParam>
        <param IDPARAM="CALCULPTSARRONDI" VALEUR="1" COMMENTAIRE="Activation de l&apos;arrondi pour le calcul des points"/>
        <param IDPARAM="DATAVIZ_ACTIVE" VALEUR="1" COMMENTAIRE="Activer la sauvegarde dans TBLETTRAGESCLIENTS des valeurs modifiées pour envoi DATAVIZ PARTIEL"/>
        <param IDPARAM="SEUIL_LIMITEEXCLUSION" COMMENTAIRE="Seuil limite d&apos;exclusion produit par magasin"/>
        <param IDPARAM="WALLET_URL" VALEUR="https://wallet.zefid.fr/ws/card/[IDENSEIGNE]/[CODECARTE]/[TOKEN]" COMMENTAIRE="URL Wallet"/>
        <param IDPARAM="JOUR_DECLENCHE_ANNIVERSAIRE" VALEUR="0" COMMENTAIRE="Nombre de jour de déclenchement avant/après date anniversaire"/>
        <param IDPARAM="GESTPROMOPLATEFORME" VALEUR="0" COMMENTAIRE="1 pour basculer sur les structures promo plateforme"/>
        <param IDPARAM="GEST_PHARMAFERMEE" VALEUR="0" COMMENTAIRE="Permet la création d&apos;une pharmacie avec une date de fermeture obligatoirement renseignée"/>
        <param IDPARAM="WALLET_SALT" VALEUR="Giropharm9XvwgdwFmFctlX2PySOXrjK0tHJThK3fC3kkD0VV9vSAxuK500J9b3HrlIQkwIub" COMMENTAIRE="Salt Wallet"/>
        <param IDPARAM="UTILISATION_CARTECADEAU" VALEUR="0" COMMENTAIRE="Utilisation des cartes cadeaux"/>
        <param IDPARAM="CODECOUPON_ANNIVERSAIRE" VALEUR="9001000000008" COMMENTAIRE="Code du coupon Anniversaire"/>
        <param IDPARAM="SEUILMAGASIN" VALEUR="1000"/>
        <param IDPARAM="AJOUT_DECIMALE_BATCHEQUE" VALEUR="2" COMMENTAIRE="Nb de décimale ajouté au montantcheque sans décimale"/>
        <param IDPARAM="CARTEKDOMIN" VALEUR="15" COMMENTAIRE="Montant minimal de la carte cadeau"/>
        <param IDPARAM="CARTEKDOMAX" VALEUR="500" COMMENTAIRE="Montant maximal de la carte cadeau"/>
        <param IDPARAM="CARTEKDOVALIDITEMOIS" VALEUR="12" COMMENTAIRE="Nombre de mois de validité de la carte cadeau"/>
        <param IDPARAM="WALLET_MESSAGE_BIENVENUE" VALEUR="Merci, retrouvez votre carte de fidélité et suivez votre compte depuis votre mobile." COMMENTAIRE="Message de Bienvenue wallet"/>
        <param IDPARAM="PREFIXECHEQUE" VALEUR="10010"/>
        <param IDPARAM="SEQ_NUMEROCHEQUE" VALEUR="SEQ_NUMCHEQUE_10"/>
        <param IDPARAM="TAILLE_MONTANT_AVANT_VIRGULE" VALEUR="5" COMMENTAIRE="Permet de déterminer le nombre de chiffre avant la virgule retourné dans les WS"/>
        <param IDPARAM="TAILLE_MONTANT_APRES_VIRGULE" VALEUR="2" COMMENTAIRE="Permet de déterminer le nombre de chiffre après la virgule retourné dans les WS"/>
        <param IDPARAM="EUROSOFFCLIENTSDECROISSANTS" COMMENTAIRE="Nombre d&apos;euros offerts aux clients décroissants"/>
        <param IDPARAM="NBMOISOFFCLIENTSDECROISSANTS" VALEUR="0" COMMENTAIRE="permet de configurer le nombre de mois pour l&apos;opération offre décroissant"/>
        <param IDPARAM="TYPEMONTANTCART" VALEUR="2" COMMENTAIRE="Permet de paramétrer le compteur de points en cagnotte ou en montant"/>
        <param IDPARAM="GEST_BIENVENUE" VALEUR="1" COMMENTAIRE="Active la gestion des bienvenues lors de la création d&apos;un client ou lors d&apos;un passage en caisse"/>
        <param IDPARAM="MONTANTRELANCEDECROISSANT" VALEUR="0" COMMENTAIRE="Montant offert aux clients pour l&apos;opération relance décroissant"/>
        <param IDPARAM="WS_ECRITURE_LOG" VALEUR="1" COMMENTAIRE="Sert à couper l&apos;écriture es logs via les WS"/>
        <param IDPARAM="WS_ALL_ECRITURE_LOG" VALEUR="1" COMMENTAIRE="Sert à couper l&apos;écriture es logs via les WS"/>
        <param IDPARAM="WS_CAISSE_ECRITURE_LOG" VALEUR="1" COMMENTAIRE="Sert à couper l&apos;écriture es logs via les WS"/>
        <param IDPARAM="WS_CLIENT_ECRITURE_LOG" VALEUR="1" COMMENTAIRE="Sert à couper l&apos;écriture es logs via les WS"/>
        <param IDPARAM="WS_ENSEIGNE_ECRITURE_LOG" VALEUR="1" COMMENTAIRE="Sert à couper l&apos;écriture es logs via les WS"/>
        <param IDPARAM="PROMOTION_VALEUR_MULTIPLE" VALEUR="0" COMMENTAIRE="Bonus octroyé lors de la création de ventes depuis la caisse pour certains produits"/>
        <param IDPARAM="CODE_RETOUR_CHARIOT" VALEUR="\n" COMMENTAIRE="Utilisé dans les WS pour savoir quel caractère utilisé pour marquer les fins de lignes"/>
        <param IDPARAM="NBPASSAGES_BIENVENUE" VALEUR="0" COMMENTAIRE="Nombre de passage en caisse pour déclencher le bienvenue"/>
        <param IDPARAM="GESTCHQMULTICIV" VALEUR="1" COMMENTAIRE="Permet de sélectionner les paramètres à utiliser lors de la génération d&apos;un lot de chèque. Si 1 -&gt; un paramètre commun, Si 0 -&gt; un paramètre par civilité"/>
        <param IDPARAM="MTCHHOMME" VALEUR="5" COMMENTAIRE="Valeur de base d&apos;un chèque fidélité pour un homme"/>
        <param IDPARAM="MTCHFEMME" VALEUR="5" COMMENTAIRE="Valeur de base d&apos;un chèque fidélité pour une femme"/>
        <param IDPARAM="NBRPOINTSPOURCHEQUEHOMME" VALEUR="100" COMMENTAIRE="Nombre de points nécessaire pour obtenir un chèque pour un homme"/>
        <param IDPARAM="NB_MOIS_BIENVENUE" VALEUR="0" COMMENTAIRE="Nombre de mois durant lesquels on peut recevoir le bienvenue"/>
        <param IDPARAM="NB_JOURS_BIENVENUE" VALEUR="0" COMMENTAIRE="Nombre de jours durant lesquels on peut recevoir le bienvenue"/>
        <param IDPARAM="REGLES_BIENVENUE" VALEUR="+" COMMENTAIRE="Définition de la regle des bienvenues"/>
        <param IDPARAM="COEFF_BIENVENUE" VALEUR="20" COMMENTAIRE="Coefficient des bienvenues"/>
        <param IDPARAM="POINTS_NEGATIFS" VALEUR="0" COMMENTAIRE="Autorisation des points negatifs"/>
        <param IDPARAM="FEN_ART_ELIG_INDIGO" VALEUR="1" COMMENTAIRE="Utilisation de la fenêtre d&apos;articles éligibles dans Indigo"/>
        <param IDPARAM="ART_PROMO_DEPORTE" VALEUR="0" COMMENTAIRE="Articles des promotions déportés de la table des promotions - Utilisation dans la fenêtre d&apos;articles éligibles sous Indigo"/>
        <param IDPARAM="NBRPOINTSPOURCHEQUEFEMME" VALEUR="100" COMMENTAIRE="Nombre de points nécessaire pour obtenir un chèque pour une femme"/>
        <param IDPARAM="ACTIVE_LOGSWS" VALEUR="1" COMMENTAIRE="Paramétre indiquant si les logs des Ws sous la forme de fichiers générés par Oracle sont actif ou non (actif =1; non actif = 0)"/>
        <param IDPARAM="NBMOIS_CONSERVATION_LOGSWS" VALEUR="1" COMMENTAIRE="Nombre de mois durant lesquels on conserve les logs ws dans Oracle"/>
        <param IDPARAM="PROFIL_COMPLET" COMMENTAIRE="Valeur à affecter au client (tbclients.montantcart) si celui ci à remplit son profil"/>
        <param IDPARAM="CODECOUPON_BIENVENUE" COMMENTAIRE="Code du coupon Bienvenue"/>
        <param IDPARAM="GEST_EMAILCAGNOTTE" VALEUR="0" COMMENTAIRE="Active la gestion des flags pour les emails cagnotte"/>
        <param IDPARAM="SEUIL_EMAILCAGNOTTE" COMMENTAIRE="Montant de cagnotte du client permettant de déclencher l&apos;email cagnotte"/>
        <param IDPARAM="GEST_ANNIVERSAIRE" VALEUR="0" COMMENTAIRE="Active la gestion des anniversaires"/>
        <param IDPARAM="NB_MOIS_ANNIVERSAIRE" VALEUR="0" COMMENTAIRE="Nombre de mois de validité de l&apos;offre anniversaire"/>
        <param IDPARAM="NB_JOURS_ANNIVERSAIRE" VALEUR="0" COMMENTAIRE="Nombre de jours de validité de l&apos;offre anniversaire"/>
        <param IDPARAM="MT_MAXCHQ_AUTORISEHOMME" VALEUR="15" COMMENTAIRE="Montant maximum d&apos;un chèque homme"/>
        <param IDPARAM="MT_MAXCHQ_AUTORISEFEMME" VALEUR="15" COMMENTAIRE="Montant maximum d&apos;un chèque femme"/>
        <param IDPARAM="REGLES_ANNIVERSAIRE" COMMENTAIRE="Définition de la regle des anniversaires"/>
        <param IDPARAM="COEFF_ANNIVERSAIRE" VALEUR="0" COMMENTAIRE="Coefficient des anniversaires"/>
        <param IDPARAM="CODETYPEMAILING_EMAILCAGNOTTE" VALEUR="232" COMMENTAIRE="codetypemailing correspondant au mailing d&apos;alerte email cagnotte"/>
        <param IDPARAM="GEST_RAZCAGNOTTE" VALEUR="0" COMMENTAIRE="Active la gestion de la remises à zéro du montant de carte du client"/>
        <param IDPARAM="PERIODE_RAZCAGNOTTE" COMMENTAIRE="Nombre de mois considéré pour la remises à zéro du montant de carte du client"/>
        <param IDPARAM="NBMOIS_RAZMONTANTCART" VALEUR="13" COMMENTAIRE="Nombre de mois considéré pour la remises à zéro du montant de carte du client"/>
        <param IDPARAM="GEST_RAZMONTANTCART" VALEUR="1" COMMENTAIRE="Active la gestion de la remises à zéro du montant de carte du client"/>
        <param IDPARAM="MSG_VTE_BIENV_INDIGO" VALEUR="Bienvenue" COMMENTAIRE="Message affiché sur le ticket d&apos;une vente typé bienvenue dans la mosaïque client sur Indigo"/>
        <param IDPARAM="ARTICLE_ELIGIBLE" VALEUR="0" COMMENTAIRE="Eligibilité par défaut en création d&apos;un article non référencé en ws de ventes"/>
        <param IDPARAM="NB_MOIS_CHQFID_PAPIER" VALEUR="2" COMMENTAIRE="Nombre de mois de validité d&apos;un chèque fid papier"/>
        <param IDPARAM="NB_MOIS_CHQFID_EMAIL" VALEUR="2" COMMENTAIRE="Nombre de mois de validité d&apos;un chèque fid email"/>
        <param IDPARAM="MSG_VTE_ANNIV_INDIGO" VALEUR="Coupon Anniversaire" COMMENTAIRE="Message affiché sur le ticket d&apos;une vente typée anniversaire dans la mosaïque client sur Indigo"/>
        <param IDPARAM="NB_MOIS_CHQFID_SMS" VALEUR="2" COMMENTAIRE="Nombre de mois de validité d&apos;un chèque fid sms"/>
        <param IDPARAM="NB_JOURS_CHQFID_PAPIER" VALEUR="0" COMMENTAIRE="Nombre de jours de validité d&apos;un chèque fid papier"/>
        <param IDPARAM="NB_JOURS_CHQFID_EMAIL" VALEUR="0" COMMENTAIRE="Nombre de jours de validité d&apos;un chèque fid email"/>
        <param IDPARAM="NB_JOURS_CHQFID_SMS" VALEUR="0" COMMENTAIRE="Nombre de jours de validité d&apos;un chèque fid sms"/>
        <param IDPARAM="CODECARTE_NONIDENTIFIEE" VALEUR="9999999999994" COMMENTAIRE="Codecarte fourni pour création d&apos;une vente non attribuée"/>
        <param IDPARAM="CREATION_ARTICLE_NONREF" VALEUR="0" COMMENTAIRE="Permet la création d&apos;article en Non reférencé"/>
        <param IDPARAM="SEUIL_DECLENCHEMENT" VALEUR="100" COMMENTAIRE="Montantcart minimum pour obtenir un chèque"/>
        <param IDPARAM="SEUIL_DECLENCHEMENTFEMME" VALEUR="100" COMMENTAIRE="Montantcart minimum pour obtenir un chèque femme"/>
        <param IDPARAM="SEUIL_DECLENCHEMENTHOMME" VALEUR="100" COMMENTAIRE="Montantcart minimum pour obtenir un chèque homme"/>
        <param IDPARAM="INVERSION_ADRESSE" VALEUR="O" COMMENTAIRE="Flag indiquant si l&apos;on devait inverser l&apos;adresse1 et l&apos;adresse2 lors de l&apos;impression"/>
        <param IDPARAM="BLOCAGE_MAJ_ANONYMOUS" VALEUR="1" COMMENTAIRE="Permet de savoir si la modification d&apos;un client anonymisé est possible (0) ou non (1)"/>
        <param IDPARAM="IDANIMAL_AUTRE" VALEUR="5" COMMENTAIRE="Id de l&apos;animal autre pour le programme, utilisé pour les WS"/>
        <param IDPARAM="RGPD_NBMOISSANSACHAT" VALEUR="36" COMMENTAIRE="Les clients n&apos;ayant pas acheté depuis les &lt;RGPD_NBMOISSANSACHAT&gt; derniers mois seront anonymisés"/>
        <param IDPARAM="GESTION_MODIF_ARTICLE" VALEUR="0" COMMENTAIRE="Modification simple de l&apos;article dans le traitement fichier des articles"/>
        <param IDPARAM="ARTICLE_ELIGIBILITE_DEFAUT" VALEUR="1" COMMENTAIRE="Valeur par défaut de l&apos;éligibilité dans le traitement fichier des articles"/>
        <param IDPARAM="CUMUL_AVANTAGE" VALEUR="0" COMMENTAIRE="Activation du cumul de points"/>
        <param IDPARAM="SEUIL_ART_NO_PTS" COMMENTAIRE="Seuil du nombre d&apos;articles qui ne donne pas de points"/>
        <param IDPARAM="NOMPROGRAMME" VALEUR="Giropharm Fidelite" COMMENTAIRE="Nom du programme(WalLet)"/>
        <param IDPARAM="POSSIBILITE_MODIFIER_CLIENT" VALEUR="O" COMMENTAIRE="Possibilité de modifier le client pour les opérateurs utilisant Indigo par TSE"/>
        <param IDPARAM="EXPORT_FICHIER_CLIENTS" VALEUR="0" COMMENTAIRE="Format d&apos;export du fichier clients"/>
        <param IDPARAM="TAILLESITE" VALEUR="10" COMMENTAIRE="Longueur maximale d&apos;un code site"/>
        <param IDPARAM="TAILLEMAGASIN" VALEUR="9" COMMENTAIRE="Longueur maximale d&apos;un code magasin"/>
        <param IDPARAM="TAILLECODEPOSTALFRANCE" VALEUR="5" COMMENTAIRE="Taille maximal du codepostal français"/>
        <param IDPARAM="OPERATIONBOOSTENCOURS" VALEUR="0" COMMENTAIRE="Flag indiquant si une opération boost est en cours"/>
        <param IDPARAM="MTACHPOINTS" VALEUR="1" COMMENTAIRE="Valeur du point en Euros"/>
        <param IDPARAM="NBRPOINTSPOURCHEQUE" VALEUR="100" COMMENTAIRE="Nombre de points nécessaire pour obtenir un chèque"/>
        <param IDPARAM="MTCH" VALEUR="5" COMMENTAIRE="Valeur de base d&apos;un chèque fidélité"/>
        <param IDPARAM="MTCHAVERIF" VALEUR="10" COMMENTAIRE="Cette valeur permet de déterminer le seuil des chèques qui seront vérifié par le plateau fidélité (montantchèque base * 2  =&gt; 40€ tous les chèques d&apos;un montant de 40€ et + seront exportés dans un listing de vérification pour le plateau)"/>
        <param IDPARAM="EXPORT_FICHIER_VENTES" VALEUR="0" COMMENTAIRE="Format d&apos;export du fichier de ventes"/>
        <param IDPARAM="FORCE_ELIGIBILITE" VALEUR="0" COMMENTAIRE="Eligibilité par défaut en création d&apos;un article non référencé en traitement des ventes xml"/>
        <param IDPARAM="CODEFAMMILLE_ARTICLE_NONREF" COMMENTAIRE="CodeFamille par défaut lors de la création d&apos;un article non reférencé"/>
        <param IDPARAM="DATEPASSAGEEURO" VALEUR="01/01/2002" COMMENTAIRE="Date du passage à l&apos;Euro"/>
        <param IDPARAM="CODELABORATOIRE_ARTICLE_NONREF" COMMENTAIRE="CodeLaboratoire par défaut lors de la création d&apos;un article non reférencé"/>
        <param IDPARAM="ACTIVER_REGLES_GESTION_COUPONS" VALEUR="1" COMMENTAIRE="Permet d&apos;activer la recherche de coupon sur la date de validité"/>
        <param IDPARAM="ENVOIE_CHEQUE_FTP_INDIGO" VALEUR="0" COMMENTAIRE="Permet l&apos;envoie des chèques sur le FTP via INDIGO"/>
        <param IDPARAM="ETAPE_REFERENCE" VALEUR="18" COMMENTAIRE="Numéro de l&apos;étape de génération de la réference - Permet de retirer de la liste des lots le lot référencé, fenêtre des Réferences dans Indigo"/>
        <param IDPARAM="LANGUEDEFAUT" VALEUR="1" COMMENTAIRE="CODELANGUE affecté par defaut aux clients (si non renseigné)"/>
        <param IDPARAM="NbJoursRejetVentes" VALEUR="900" COMMENTAIRE="permet de configurer un nombre de jours  pour filtrer l&apos;intégration des ventes, une vente ne peut être intégrée que si sa date est comprise entre DATEDUJOUR - NbjoursRejetVentes et datedujour"/>
        <param IDPARAM="TAILLECODEPOSTAL" VALEUR="10" COMMENTAIRE="Taille maximal du codepostal"/>
        <param IDPARAM="MONNAIEPAYS" VALEUR="?" COMMENTAIRE="Symbole monétaire actif"/>
        <param IDPARAM="MONNAIEPAYS2" VALEUR="F" COMMENTAIRE="Ancien symbole monétaire"/>
        <param IDPARAM="ANCIENMONTANTPOURUNPOIT" VALEUR="10" COMMENTAIRE="Variable permettant de sauvegarder l&apos;ancien coefficient de l&apos;Euro transformer en point"/>
        <param IDPARAM="CIVILITEDEFAUT" VALEUR="1" COMMENTAIRE="Civilité par défaut affectée lorsque celle-ci n&apos;est pas saisie"/>
        <param IDPARAM="TAILLENUMTELEPHONE" VALEUR="10" COMMENTAIRE="Longueur maximale d&apos;un n° de téléphone"/>
        <param IDPARAM="NBRE_LOT_CHQ_RETOURNE" VALEUR="1500" COMMENTAIRE="Nombre de lots de chèques retournés dans Indigo lors de l&apos;affichage des lots de chèques "/>
        <param IDPARAM="DATECREATION" VALEUR="01/01/1998" COMMENTAIRE="Date de début de gestion de la fidélité chez Aquitem"/>
        <param IDPARAM="NBRE_LIGNES_RETOURNEES" VALEUR="100" COMMENTAIRE="Nombre de lignes retournés dans Indigo lors d&apos;une recherche clients"/>
        <param IDPARAM="NIVEAU_UTILISE_CSP" VALEUR="1" COMMENTAIRE="niveau utiliser dans catégories sociaux professionnels"/>
        <param IDPARAM="MT_A_VERIFIER_TRAITE_VENTES" VALEUR="1000" COMMENTAIRE="Permet de configurer le montant (TTC) de TBVENTES à partir duquel les ventes seront exportés dans un fichier pour vérification manuelle par le plateau"/>
        <param IDPARAM="NUMERO_CHEQUE_MIN_A_VERIFIER" VALEUR="1500000" COMMENTAIRE="N° de chèque à partir duquel sera traité les mises en NPAI automatique par fichiers suite à un lots de chèques"/>
        <param IDPARAM="CALCUL_RETOUR_MAILING" VALEUR="0" COMMENTAIRE="Utilisé dans Indigo pour indiquer si le client est revenu acheter durant la période du mailing"/>
        <param IDPARAM="DATE_CHGT_CALCUL_PTS1" COMMENTAIRE="Date de dernier changement du paramétrage du calcul des points"/>
        <param IDPARAM="MTACHPOINTS_OLD1" COMMENTAIRE="Ancienne valeur d&apos;un point en Euros"/>
        <param IDPARAM="DATE_CHGT_MONTANT_CREATION" VALEUR="10/08/2020" COMMENTAIRE="Date du dernier changement de la valeur de création en montantcart"/>
        <param IDPARAM="ANCIEN_MONTANT_DISQUETTE_BRUTE" VALEUR="0" COMMENTAIRE="Ancienne valeur affectée lors du traitement de disquette brute (si changement de la valeur du bonus octroyer à la création d&apos;une carte)"/>
        <param IDPARAM="GEST_PRESTATAIRE" VALEUR="O" COMMENTAIRE="Utiliser dans Indigo, pour savoir si l&apos;on doit afficher la liste des prestataires dans la fiche magasin"/>
        <param IDPARAM="NBJOURS_NPAI_RAPPORT_CHQ" VALEUR="22" COMMENTAIRE="permet de configuer le nombre de jours après une sortie de NPAi pour l&apos;exclusion des clients dans le rapport de chèques"/>
        <param IDPARAM="DUREE VALIDITE CHEQUES ANNIV" VALEUR="30" COMMENTAIRE="Nombre de jours de validité des chèques anniversaires"/>
        <param IDPARAM="HEURE_SMS" VALEUR="09:00:00" COMMENTAIRE="Heure d&apos;envoi des SMS"/>
        <param IDPARAM="SEUIL_BOOST" VALEUR="75" COMMENTAIRE="Montantcart minimum pour obtenir un chèque boost"/>
        <param IDPARAM="IDMAILING_ETAPES" COMMENTAIRE="Utilisé par Indigo"/>
        <param IDPARAM="MAILING_LIMIT" VALEUR="1000000" COMMENTAIRE="Nombre de lignes à traiter entre chaque COMMIT pour le tri cqc des mailings"/>
        <param IDPARAM="CODETYPEGENE" VALEUR="12" COMMENTAIRE="Utilisé par indigo pour enregister le fichier Word lié aux mailings"/>
        <param IDPARAM="TAILLE_MAILING" VALEUR="10" COMMENTAIRE="Taille maximale d&apos;un code mailing"/>
        <param IDPARAM="MAXINUMETAPECHQ" VALEUR="2" COMMENTAIRE="Nombre d&apos;étapes maximum pour l&apos;interface de chèques Indigo"/>
        <param IDPARAM="MT_MAXCHQ_AUTORISE" VALEUR="15" COMMENTAIRE="Montant maximal d&apos;un chèque généré"/>
        <param IDPARAM="NBMOISMODIFTRTFICNPAI" VALEUR="2" COMMENTAIRE="permet de paramétrer le nombre de mois pour la mise en NPAI automatique (à partir d&apos;un fichier de chèques par exemple) entre la date de dernière modification de la fiche client et la date de mise en NPAI"/>
        <param IDPARAM="PCT_CAGNOTTAGE" COMMENTAIRE="pourcentage de cagnottage"/>
        <param IDPARAM="SEUIL_UTILISATION_CAGNOTTE" COMMENTAIRE="Montant minimum pour pouvoir dépenser sa cagnotte"/>
        <param IDPARAM="AGREMENT" COMMENTAIRE="Prefixe à apposer au numero finess"/>
        <param IDPARAM="LIBELLETYPEMONTANTCART" VALEUR="POINTS" COMMENTAIRE="WALLET"/>
        <param IDPARAM="GESTOPTINPROGRAMME" VALEUR="0" COMMENTAIRE="Optin utilisé sur le groupement"/>
        <param IDPARAM="MAGASIN_VIRTUEL" VALEUR="999999999" COMMENTAIRE="Pharmacie virtuelle du groupement"/>
        <param IDPARAM="GEST_MAGASIN_VIRTUEL" VALEUR="1" COMMENTAIRE="Permet de gérer la bascule automatique des clients d&apos;une pharmacie qui ferme vers la pharmacie virtuelle et aussi permettre le rattachement des clients rattaché sur celle-ci d&apos;être rebasculé vers la pharmacie de leur dernier achat"/>
        <param IDPARAM="BONUS_POINTS_WALLET" VALEUR="0" COMMENTAIRE="Points bonus lors du téléchargement du wallet par le client"/>
        <param IDPARAM="GEST_CREATION_DEMAT" VALEUR="0"/>
        <param IDPARAM="SEUIL_EXCLUSION_PLATEFORME" VALEUR="9999" COMMENTAIRE="Nombre maximum d&apos;exclusions produits possible sur le moteur de règles"/>
        <param IDPARAM="SSO_OPENID_URLAQUITEMDECTOKEN" VALEUR="http://webl0036-back.aqui-wsphp-preprod.int.aquitem.net/CheckAccessToken.php" COMMENTAIRE="Url php Aquitem pour décoder l&apos;accesstoken"/>
    </collectionParam>
</resultat></xml>
