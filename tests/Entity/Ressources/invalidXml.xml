<?xml version="1.0" encoding="utf-8"?>
<ERROR>
    <CODE>-99</CODE>
    <MESSAGE>
        <![CDATA[DbsCmdDispatcher::Execute(): exception Oracle !    à Aquitem.WS4LightWeb.DbsCmdDispatcher.Execute()
   à Aquitem.WS4LightWeb.CmdDispatcher.ServiceGetDataInitialize()
   à Aquitem.WS4LightWeb.CmdDispatcher.Page_Load(Object sender, EventArgs e)ORA-03135: contact perdu pour la connexion
ID de processus : 0
ID de session : 382,  Numéro de série : 52879   à Oracle.DataAccess.Client.OracleException.HandleErrorHelper(Int32 errCode, OracleConnection conn, IntPtr opsErrCtx, OpoSqlValCtx* pOpoSqlValCtx, Object src, String procedure, Boolean bCheck, Int32 isRecoverable, OracleLogicalTransaction m_OracleLogicalTransaction)
   à Oracle.DataAccess.Types.OracleClob.CreateTempLob()
   à Oracle.DataAccess.Types.OracleClob.Write(Char[] buffer, Int32 offset, Int32 count, Boolean bIsFromEF)
   à Oracle.DataAccess.Client.OracleParameter.PreBind_Clob(OracleConnection conn, Boolean bIsFromEF, Boolean bIsSelectStmt)
   à Oracle.DataAccess.Client.OracleParameter.PreBind(OracleConnection conn, IntPtr errCtx, Int32 arraySize, Boolean bIsFromEF, Boolean bIsSelectStmt)
   à Oracle.DataAccess.Client.OracleCommand.ExecuteNonQuery()
   à Aquitem.WS4LightWeb.DbsCmdDispatcher.Execute()]]>
    </MESSAGE>
</ERROR>