<?php

namespace App\Tests\Services;

use App\Serializer\ClientDeserializer;
use App\Services\NewClientsService;
use App\Tests\ApiTestCase;
use App\Tests\ObjectFormatter;
use ChqThomas\ApprovalTests\Approvals;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;

class NewClientsServiceTest extends ApiTestCase
{
    private NewClientsService $newClientsService;

    #[\Override]
    protected function setUp(): void
    {
        $callback = function ($method, $url, $options) {
            $this->assertStringContainsString('PhDClientClientSelect.php', $url);

            if (
                $this->isRequestForAuthenticate($options['query'])
                || $this->isRequestForRetrievesClient($options['query'])
            ) {
                return new MockResponse(file_get_contents(__DIR__.'/../Entity/Ressources/correctClients.xml'));
            }

            if ($this->isRequestForAuthenticateError($options['query'])) {
                return new MockResponse(file_get_contents(__DIR__.'/../Entity/Ressources/incorrectClients.xml'));
            }

            if ($this->isRequestForInvalidXml($options['query'])) {
                return new MockResponse(file_get_contents(__DIR__.'/../Entity/Ressources/invalidXml.xml'));
            }

            throw new \Exception('Unknown request');
        };

        $httpClient = new MockHttpClient($callback);
        $serializer = self::getContainer()->get(SerializerInterface::class);
        $normalizer = self::getContainer()->get(NormalizerInterface::class);
        $security = self::getContainer()->get(\Symfony\Bundle\SecurityBundle\Security::class);
        // TODO : gérer les différents programmes
        $this->newClientsService = new NewClientsService($httpClient, $normalizer, new ClientDeserializer($serializer, $normalizer), $security, [
            'programme' => ['key' => 'programme', 'value' => 'fakeprogrammekey'],
        ]);
    }

    public function test_retrieves_client(): void
    {
        $client = $this->newClientsService->checkCodeClient('2102100796109');
        Approvals::verify(ObjectFormatter::format($client));
    }

    public function test_authenticate(): void
    {
        $client = $this->newClientsService->authenticate('2102100796109', '01/05/1972');
        Approvals::verify(ObjectFormatter::format($client));
    }

    public function test_authenticate_error(): void
    {
        $client = $this->newClientsService->authenticate('12345', '01/05/1972');
        Approvals::verify(ObjectFormatter::format($client));
    }

    public function test_invalid_xml(): void
    {
        self::markTestSkipped("Mieux catcher l'erreur et mettre en place un système d'alerte à Aquitem");
        $client = $this->newClientsService->authenticate('666', '01/05/1972');
        Approvals::verify(ObjectFormatter::format($client));
    }

    private function isRequestForAuthenticate($query): bool
    {
        return ($query['CodeCarte'] ?? null) == '2102100796109' && ($query['DateNaissance'] ?? null) == '01/05/1972';
    }

    private function isRequestForAuthenticateError($query): bool
    {
        return ($query['CodeCarte'] ?? null) == '12345';
    }

    private function isRequestForInvalidXml($query): bool
    {
        return ($query['CodeCarte'] ?? null) == '666';
    }

    private function isRequestForRetrievesClient($query): bool
    {
        return ($query['codeclient'] ?? null) == '2102100796109' && !isset($query['DateNaissance']);
    }
}
