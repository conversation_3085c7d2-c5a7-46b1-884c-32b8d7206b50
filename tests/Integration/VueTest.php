<?php

namespace App\Tests\Integration;

use App\Services\NewClientsService;
use App\Tests\ApiTestCase;
use App\Tests\Fixtures\giropharm\MockLoader;
use ChqThomas\ApprovalTests\Approvals;
use Symfony\Component\DomCrawler\Crawler;
use WireMock\Client\WireMock;

class VueTest extends ApiTestCase
{
    private $wireMock;
    private $client;

    public function getVue(): ?Crawler
    {
        return $this->client->request('GET', '/vue/');
    }

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        $this->wireMock = WireMock::create('wiremock', 8080);
        MockLoader::configureMocks($this->wireMock, self::getContainer()->getParameter('kernel.project_dir'));

        $this->loginUser();
    }

    private function loginUser(): void
    {
        $user = self::getContainer()->get(NewClientsService::class)->authenticate('1001001000012', '25/10/1949');
        $this->client->loginUser($user, 'secured_area');
    }

    public function test_client_resume(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'resume');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_magasin(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'magasin');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_carte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'macarte');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_passbook(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'passbook');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_mes_points(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'mesPoints');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_historique(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'historique');

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_client_mon_compte(): void
    {
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'monCompte');
        $reindentedBlock = $this->scrubTokens($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }
}
