<?php

namespace App\Tests\Integration;

use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;

class AnonymousUserTest extends ApiTestCase
{
    private $client;

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
    }

    public function test_index(): void
    {
        self::markTestSkipped("Problème d'aprovals avec asset mapper et hash dans fichiers css");
        $crawler = $this->client->request('GET', '/');
        $reindentedBlock = $this->indenter->indent($crawler->outerHtml());
        $reindentedBlock = $this->scrubTokens($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }

    //    public function test_login_success(): void
    //    {
    //        $crawler = $this->client->request('GET', '/');
    //        $this->client->submitForm('login_submit', [
    //            'login[_username]' => MockLoader::CLIENT_1_CODE_CARTE,
    //            'login[_password]' => MockLoader::CLIENT_1_PASSWORD,
    //            'login[_token]' => $crawler->filter('input[name="login[_token]"]')->attr('value'),
    //        ]);
    //        $this->client->followRedirect();
    //
    //        self::assertResponseIsSuccessful();
    //        self::assertRouteSame('home_vue');
    //    }
    //
    //    public function test_login_failed(): void
    //    {
    //        $crawler = $this->client->request('GET', '/');
    //        $this->client->submitForm('login_submit', [
    //            'login[_username]' => MockLoader::CLIENT_1_CODE_CARTE,
    //            'login[_password]' => MockLoader::CLIENT_1_PASSWORD_FAKE,
    //            'login[_token]' => $crawler->filter('input[name="login[_token]"]')->attr('value'),
    //        ]);
    //        $this->client->followRedirect();
    //
    //        self::assertResponseStatusCodeSame(401);
    //        self::assertRouteSame('home_login_statut');
    //    }

    public function test_activation(): void
    {
        self::markTestSkipped("Problème d'aprovals avec asset mapper et hash dans fichiers css");
        $crawler = $this->client->request('GET', '/activation');
        $reindentedBlock = $this->indenter->indent($crawler->outerHtml());
        $reindentedBlock = $this->scrubTokens($reindentedBlock);
        $reindentedBlock = $this->scrubCaptcha($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }
}
