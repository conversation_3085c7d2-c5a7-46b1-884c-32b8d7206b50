<?php

namespace unit;

use App\DTO\Result;
use App\DTO\WebserviceError;
use App\Entity\Client;
use PHPUnit\Framework\TestCase;

class ResultTest extends TestCase
{
    public function test_can_handle_success()
    {
        $result = Result::success(new Client());
        $this->assertTrue($result->isSuccess());
    }

    public function test_can_handle_error()
    {
        $result = Result::error(new WebserviceError(123));
        $this->assertTrue($result->isFailure());
    }

    public function test_can_handle_try_success()
    {
        $client = new Client();
        $result = Result::try(fn () => $client, fn ($e) => new WebserviceError(123));
        $this->assertTrue($result->isSuccess());

        $this->assertEquals($client, $result->get());
    }

    public function test_can_handle_try_error()
    {
        $result = Result::try(fn () => throw new \RuntimeException('invalid callable'), fn ($e) => new WebserviceError(123));
        $this->assertTrue($result->isFailure());

        $this->expectException(\RuntimeException::class);
        $result->get();
    }
}
