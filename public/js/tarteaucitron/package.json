{"name": "tarteau<PERSON><PERSON><PERSON><PERSON>", "version": "1.8.2", "description": "Comply to the European cookie law", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/AmauriC/tarteaucitron.js.git"}, "keywords": ["cookie", "law", "rgpd", "gdpr", "cookie"], "author": "Amauri.IO", "license": "MIT", "bugs": {"url": "https://github.com/AmauriC/tarteaucitron.js/issues"}, "homepage": "https://github.com/AmauriC/tarteaucitron.js#readme"}