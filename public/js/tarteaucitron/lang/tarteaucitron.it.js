/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Benvenuto! Questo sito ti permette di attivare i servizi di terzi di tua scelta.",
    "adblock_call": "Disabilita il tuo adblocker per iniziare la navigazione.",
    "reload": "Aggiorna la pagina",
    
    "alertBigScroll": "Continuando a scorrere,",
    "alertBigClick": "Continuando a navigare nel sito,",
    "alertBig": "autorizzi l’utilizzo dei cookies inviati da domini di terze parti",
    
    "alertBigPrivacy": "Questo sito fa uso di cookies e ti consente di decidere se accettarli o rifiutarli",
    "alertSmall": "Gestione dei servizi",
    "acceptAll": "Ok, accetta tutto",
    "personalize": "Personalizza",
    "close": "Chiudi",

    "privacyUrl": "Politica sulla riservatezza",
    
    "all": "Preferenze per tutti i servizi",
    
    "info": "Tutela della privacy",
    "disclaimer": "Abilitando l'uso dei servizi di terze parti, accetti la ricezione dei cookies e l'uso delle tecnologie analitici necessarie al loro funzionamento.",
    "allow": "Consenti",
    "deny": "Blocca",
    "noCookie": "Questo servizio non invia nessun cookie",
    "useCookie": "Questo servizio puo' inviare",
    "useCookieCurrent": "Questo servizio ha inviato",
    "useNoCookie": "Questo servizio non ha inviato nessun cookie",
    "more": "Saperne di più",
    "source": "Vai al sito ufficiale",
    "credit": "Gestione dei cookies da tarteaucitron.js",

    "toggleInfoBox": "Show/hide informations about cookie storage",
    "title": "Cookies management panel",
    "cookieDetail": "Cookie detail for",
    "ourSite": "on our site",
    "newWindow": "(new window)",
    "allowAll": "Allow all cookies",
    "denyAll": "Deny all cookies",
    
    "fallback": "è disattivato",
    
    "ads": {
        "title": "Regie pubblicitarie",
        "details": "Le regie pubblicitarie producono redditi gestendo la commercializzazione degli spazi del sito dedicati alle campagne pubblicitarie"
    },
    "analytic": {
        "title": "Misura del pubblico",
        "details": "I servizi di misura del pubblico permettono di raccogliere le statistiche utili al miglioramento del sito"
    },
    "social": {
        "title": "Reti sociali",
        "details": "Le reti sociali permettono di migliorare l'aspetto conviviale del sito e di sviluppare la condivisione dei contenuti da parte degli utenti a fini promozionali."
    },
    "video": {
        "title": "Video",
        "details": "I servizi di condivisione di video permettono di arricchire il sito di contenuti multimediali e di aumentare la sua visibilità"
    },
    "comment": {
        "title": "Commenti",
        "details": "La gestione dei commenti utente aiuta a gestire la pubblicazione dei commenti e a lottare contro lo spamming"
    },
    "support": {
        "title": "Supporto",
        "details": "I servizi di supporto ti consentono di contattare la team del sito e di contribuire al suo miglioramento"
    },
    "api": {
        "title": "API",
        "details": "Le API permettono di implementare script diversi : geolocalizzazione, motori di ricerca, traduttori..."
    },
    "other": {
        "title": "Altro",
        "details": "Servizi per visualizzare contenuti web."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
