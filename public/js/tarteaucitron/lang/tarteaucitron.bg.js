/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Здравей! Този сайт позволяа включването на бисквитки по избор.",
    "adblock_call": "Моля изключете вашият adblocker и изберете бисквитките които искате, или спрете всички.",
    "reload": "Презареди",
    
    "alertBigScroll": "Ако продължавате да скролвате,",
    "alertBigClick": "Ако продължавате да използвате този сайт,",
    "alertBig": "вив се съгласявате с всички бисквитки от трети лица.",
    
    "alertBigPrivacy": "Този сайт използва бисквитки и Ви дава право да изберете записването на определени или всички.",
    "alertSmall": "Управление на услуги",
    "personalize": "Ще избирам",
    "acceptAll": "ОК, приемам всички",
    "close": "Затвори",

    "privacyUrl": "Политика за поверителност",
    
    "all": "Услуги които записват бисквитки на този сайт",

    "info": "Зашитава вашата сигурност",
    "disclaimer": "Позволяването на тези бисквитки от трети лица, Вие приемате те да записват и използват услуги за проследяване нужни за правилното им функциониране.",
    "allow": "Разшреши",
    "deny": "Забрани",
    "noCookie": "Тази услуга не записва бисквитки.",
    "useCookie": "Тази услуга може да запише",
    "useCookieCurrent": "Тази услуга е записала",
    "useNoCookie": "Тази услуга не е записала бисквитки.",
    "more": "Прочети повече",
    "source": "Официален сайт",
    "credit": "Управление на бисквитките от tarteaucitron.js",

    "toggleInfoBox": "Покажи/скрий информация за записването на бисквитки",
    "title": "Управление на бисквитките",
    "cookieDetail": "Информация за",
    "ourSite": "в нашият сайт",
    "newWindow": "(нов прозорец)",
    "allowAll": "Разреши всички",
    "denyAll": "Забрани всички",
    
    "fallback": "е изключен.",

    "ads": {
        "title": "Рекламодатели",
        "details": "Ad networks can generate revenue by selling advertising space on the site."
    },
    "analytic": {
        "title": "Аналитични",
        "details": "The audience measurement services used to generate useful statistics attendance to improve the site."
    },
    "social": {
        "title": "Социални",
        "details": "Social networks can improve the usability of the site and help to promote it via the shares."
    },
    "video": {
        "title": "Видео платформи",
        "details": "Video sharing services help to add rich media on the site and increase its visibility."
    },
    "comment": {
        "title": "Коментари",
        "details": "Comments managers facilitate the filing of comments and fight against spam."
    },
    "support": {
        "title": "Поддръжка",
        "details": "Support services allow you to get in touch with the site team and help to improve it."
    },
    "api": {
        "title": "Функционални",
        "details": "APIs are used to load scripts: geolocation, search engines, translations, ..."
    },
    "other": {
        "title": "Други",
        "details": "Services to display web content."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
