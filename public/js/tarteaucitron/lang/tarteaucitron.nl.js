/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Hallo! Deze site is transparant en laat u de services van derden kiezen die u wilt toestaan.",
    "adblock_call": "<PERSON><PERSON><PERSON> uw adblocker uit om te beginnen met aanpassen.",
    "reload": "Ververs de pagina",
    
    "alertBigScroll": "Door te blijven scrollen,",
    "alertBigClick": "Als je doorgaat met het surfen op deze website,",
    "alertBig": "sta je alle diensten van derden toe",
    
    "alertBigPrivacy": "Deze site maakt gebruik van cookies en geeft u controle over wat u wilt activeren",
    "alertSmall": "Beheer instellingen",
    "personalize": "Personaliseer",
    "acceptAll": "OK, accepteer alle",
    "close": "Sluit",

    "privacyUrl": "Privacybeleid",

    "all": "Voorkeur voor alle diensten",

    "info": "Bescherming van uw privacy",
    "disclaimer": "Door deze services van derden toe te staan, accepteert u hun cookies en het gebruik van trackingtechnologieën die nodig zijn voor hun goede werking.",
    "allow": "Toestaan",
    "deny": "Weigeren",
    "noCookie": "Deze service gebruikt geen cookie",
    "useCookie": "Deze service kan worden geïnstalleerd",
    "useCookieCurrent": "Deze service is geïnstalleerd",
    "useNoCookie": "Deze service heeft geen cookies geïnstalleerd.",
    "more": "Lees meer",
    "source": "Bekijk de officiële website",
    "credit": "Cookie manager mogelijk gemaakt door tarteaucitron.js",
    
    "fallback": "is uitgeschakeld.",

    "toggleInfoBox": "Toon/verberg informatie over cookie opslag",
    "title": "Cookies beheer paneel",
    "cookieDetail": "Cookie detail voor",
    "ourSite": "op onze site",
    "newWindow": "(nieuw venster)",
    "allowAll": "Sta alle cookies toe",
    "denyAll": "Weiger alle cookies",

    "ads": {
        "title": "Advertentienetwerk",
        "details": "Advertentienetwerken kunnen inkomsten genereren door advertentieruimte op de site te verkopen."
    },
    "analytic": {
        "title": "Bezoekers meting",
        "details": "De bezoekersdiensten voor het publiek worden gebruikt om nuttige statistieken te genereren om de site te verbeteren."
    },
    "social": {
        "title": "Sociale netwerken",
        "details": "Sociale netwerken kunnen de bruikbaarheid van de site verbeteren en helpen deze via de shares te promoten."
    },
    "video": {
        "title": "Videos",
        "details": "Video sharing-services helpen om rich media op de site toe te voegen en de zichtbaarheid ervan te vergroten."
    },
    "comment": {
        "title": "Comments",
        "details": "Commentsmanagers faciliteren het indienen van opmerkingen en het bestrijden van spam."
    },
    "support": {
        "title": "Support",
        "details": "Support diensten stellen u in staat contact op te nemen met het team van de site en helpen het te verbeteren."
    },
    "api": {
        "title": "APIs",
        "details": "APIs worden gebruikt om scripts te laden: geolocatie, zoekmachines, vertalingen, ..."
    },
    "other": {
        "title": "Overig",
        "details": "Diensten om webinhoud weer te geven."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
