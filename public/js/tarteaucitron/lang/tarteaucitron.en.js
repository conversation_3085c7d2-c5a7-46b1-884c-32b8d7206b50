/*global tarteaucitron */
tarteaucitron.lang = {
    "middleBarHead": "☝ 🍪",
    "adblock": "Hello! This site is transparent and lets you choose the 3rd party services you want to allow.",
    "adblock_call": "Please disable your adblocker to start customizing.",
    "reload": "Refresh the page",
    
    "alertBigScroll": "By continuing to scroll,",
    "alertBigClick": "If you continue to browse this website,",
    "alertBig": "you are allowing all third-party services",
    
    "alertBigPrivacy": "This site uses cookies and gives you control over what you want to activate",
    "alertSmall": "Manage services",
    "personalize": "Personalize",
    "acceptAll": "OK, accept all",
    "close": "Close",

    "privacyUrl": "Privacy policy",
    
    "all": "Preference for all services",

    "info": "Protecting your privacy",
    "disclaimer": "By allowing these third party services, you accept their cookies and the use of tracking technologies necessary for their proper functioning.",
    "allow": "Allow",
    "deny": "Deny",
    "noCookie": "This service does not use cookie.",
    "useCookie": "This service can install",
    "useCookieCurrent": "This service has installed",
    "useNoCookie": "This service has not installed any cookie.",
    "more": "Read more",
    "source": "View the official website",
    "credit": "Cookies manager by tarteaucitron.js",
    "noServices": "This website does not use any cookie requiring your consent.",

    "toggleInfoBox": "Show/hide informations about cookie storage",
    "title": "Cookies management panel",
    "cookieDetail": "Cookie detail for",
    "ourSite": "on our site",
    "newWindow": "(new window)",
    "allowAll": "Allow all cookies",
    "denyAll": "Deny all cookies",
    
    "fallback": "is disabled.",

    "ads": {
        "title": "Advertising network",
        "details": "Ad networks can generate revenue by selling advertising space on the site."
    },
    "analytic": {
        "title": "Audience measurement",
        "details": "The audience measurement services used to generate useful statistics attendance to improve the site."
    },
    "social": {
        "title": "Social networks",
        "details": "Social networks can improve the usability of the site and help to promote it via the shares."
    },
    "video": {
        "title": "Videos",
        "details": "Video sharing services help to add rich media on the site and increase its visibility."
    },
    "comment": {
        "title": "Comments",
        "details": "Comments managers facilitate the filing of comments and fight against spam."
    },
    "support": {
        "title": "Support",
        "details": "Support services allow you to get in touch with the site team and help to improve it."
    },
    "api": {
        "title": "APIs",
        "details": "APIs are used to load scripts: geolocation, search engines, translations, ..."
    },
    "other": {
        "title": "Other",
        "details": "Services to display web content."
    },
    
    "mandatoryTitle": "Mandatory cookies",
    "mandatoryText": "This site uses cookies necessary for its proper functioning which cannot be deactivated."
};
