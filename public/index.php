<?php

use App\Kernel;

// require dirname(__DIR__).'/config/bootstrap.php';
//
// if ($_SERVER['APP_DEBUG']) {
//    umask(0000);
//
//    Debug::enable();
// }
//
// if ($trustedProxies = $_SERVER['TRUSTED_PROXIES'] ?? false) {
//    Request::setTrustedProxies(explode(',', $trustedProxies), Request::HEADER_X_FORWARDED_FOR | Request::HEADER_X_FORWARDED_PORT | Request::HEADER_X_FORWARDED_PROTO);
// }
//
// if ($trustedHosts = $_SERVER['TRUSTED_HOSTS'] ?? false) {
//    Request::setTrustedHosts([$trustedHosts]);
// }
//
// $kernel = new Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
// $request = Request::createFromGlobals();
// $response = $kernel->handle($request);
// $response->send();
// $kernel->terminate($request, $response);

$base = dirname(__DIR__);

// 1) Lire ENSEIGNE / ENV_TIER depuis l'env réel
$enseigne = $_SERVER['ENSEIGNE'] ?? $_ENV['ENSEIGNE'] ?? '';
$tier = $_SERVER['ENV_TIER'] ?? $_ENV['ENV_TIER'] ?? 'dev';

// 2) Fallback dev: .enseigne.local
if ('' === $enseigne && is_file($base.'/.enseigne.local')) {
    foreach (file($base.'/.enseigne.local', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) as $line) {
        if ('#' === $line[0] || !str_contains($line, '=')) {
            continue;
        }
        [$k, $v] = explode('=', $line, 2);
        $k = trim($k);
        $v = trim($v, " \t\n\r\0\x0B\"'");
        if ('ENSEIGNE' === $k) {
            $enseigne = $v;
        }
        if ('ENV_TIER' === $k) {
            $tier = $v;
        }
    }
}

$extra = [];
if ('' !== $enseigne) {
    foreach ([
        "enseigne/{$enseigne}/.env",
        "enseigne/{$enseigne}/.env.{$tier}",
        "enseigne/{$enseigne}/.env.local",
        "enseigne/{$enseigne}/.env.{$tier}.local",
    ] as $rel) {
        if (is_file($base.'/'.$rel)) {
            $extra[] = $rel;
        }
    }
}

$_SERVER['APP_RUNTIME_OPTIONS'] = [
    'dotenv_path' => '.env',
    'dotenv_extra_paths' => $extra,
    'dotenv_overload' => !in_array($tier, ['preprod', 'prod'], true),
];

require_once dirname(__DIR__).'/vendor/autoload_runtime.php';

return function (array $context) use ($enseigne): Kernel {
    return new Kernel($context['APP_ENV'], (bool) $context['APP_DEBUG'], $enseigne);
};
