#!/bin/bash

# Configuration
PROJECT_ID="$CI_PROJECT_ID"
BRANCH_NAME="$CI_COMMIT_REF_NAME"
TOKEN="$ACCESS_TOKEN_SECRET"
GITLAB_API="$CI_API_V4_URL"

echo $ACCESS_TOKEN_SECRET

echo "[INFO] 📡 Appel de l’API GitLab pour récupérer le dernier pipeline réussi sur '$BRANCH_NAME'..."

API_URL="$GITLAB_API/projects/$PROJECT_ID/pipelines?ref=$BRANCH_NAME&status=success&order_by=id&sort=desc"

echo "[INFO] 🔗 URL utilisée : $API_URL"

#curl --verbose --show-error --fail --header "PRIVATE-TOKEN: $TOKEN" "$API_URL"
RESPONSE=$(curl --silent --show-error --fail \
  --header "PRIVATE-TOKEN: $TOKEN" "$API_URL")

echo "[INFO] 🔁 Réponse API (début) :"
echo "$RESPONSE"

echo "$RESPONSE" | jq -r '.[0].sha'

LAST_SUCCESSFUL_SHA=$(echo "$RESPONSE" | jq -r '.[0].sha')

echo "[INFO] ✅ SHA extrait : $LAST_SUCCESSFUL_SHA"

if [ -z "$LAST_SUCCESSFUL_SHA" ] || [ "$LAST_SUCCESSFUL_SHA" = "null" ]; then
  echo "[ERREUR] ❌ Impossible de récupérer le SHA du dernier pipeline réussi."
  echo "Utiliation temporaire de b89f01e445686cca56a62f7b64b0e4d7d1a195d8."
  LAST_SUCCESSFUL_SHA="b89f01e445686cca56a62f7b64b0e4d7d1a195d8"
fi

echo "[INFO] 🔍 Exécution de git diff entre $LAST_SUCCESSFUL_SHA et $CI_COMMIT_SHA..."

# Lister les fichiers modifiés
CHANGED_FILES=$(git diff --name-only "$LAST_SUCCESSFUL_SHA" "$CI_COMMIT_SHA") || {
  echo "[ERREUR] ❌ git diff a échoué. Vérifie que les commits sont dans l'historique local."
  exit 1
}

echo "[INFO] 📄 Fichiers modifiés :"
echo "$CHANGED_FILES"

ALL_ENSEIGNES=("normandiepharma")
GLOBAL_PATHS_REGEX='^(config/|patches/|public/|src/|templates/|tests/|translations/|composer\.lock)'

declare -A SELECTED_ENSEIGNES=()

for file in $CHANGED_FILES; do
  if [[ "$file" =~ $GLOBAL_PATHS_REGEX ]]; then
    for e in "${ALL_ENSEIGNES[@]}"; do
      SELECTED_ENSEIGNES["$e"]=1
    done
    break
  fi

  if [[ "$file" =~ ^enseigne/([^/]+)/ ]]; then
    e=$(echo "$file" | cut -d '/' -f 2)
    for known in "${ALL_ENSEIGNES[@]}"; do
      if [[ "$e" == "$known" ]]; then
        SELECTED_ENSEIGNES["$e"]=1
      fi
    done
  fi
done

if [ ${#SELECTED_ENSEIGNES[@]} -eq 0 ]; then
  echo "❌ Aucun changement détecté sur une enseigne. Pas de matrice générée."
  touch .gitlab/generated/ci_without_modification_on_enseigne.yml
fi

# Génération YAML
echo "✅ Génération du pipeline dynamique avec ${#SELECTED_ENSEIGNES[@]} enseigne(s)…"

# Copie le template de base
cp .gitlab/gitlab-ci-template.yml .gitlab/generated/gitlab-ci.generated.yml

ENSEIGNES_YAML=""
for e in "${!SELECTED_ENSEIGNES[@]}"; do
  ENSEIGNES_YAML+="            - ENSEIGNE: \"$e\"\n"
done

# Remplace proprement dans le fichier le placeholder
sed -i "/#__ENSEIGNES_PLACEHOLDER__/ {
  s|.*|$ENSEIGNES_YAML|
}" .gitlab/generated/gitlab-ci.generated.yml
