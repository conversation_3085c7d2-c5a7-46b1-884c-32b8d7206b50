# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

#variables:
#    GIT_SUBMODULE_STRATEGY: recursive
#    GIT_SUBMODULE_FORCE_HTTPS: "true"

# todo : inclure le template gitlab-ci.yml de l'enseigne
#include:
#    - project: dev-projets-aquitem/jeux-fidelite
#      file: gitlab-ci-template.yml

image: gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:dev

.enseigne_matrix_template:
    parallel:
        matrix:
            #__ENSEIGNES_PLACEHOLDER__
    variables:
        ENSEIGNE: $ENSEIGNE
        # MANAGER_PP: $MANAGER_PP
        # WEB_ID_PP: $WEB_ID_PP
        # MANAGER_PROD: $MANAGER_PROD
        # WEB_ID_PROD: $WEB_ID_PROD

variables:
    WIREMOCK_HOST: wiremock  # Utilise cet alias pour accéder à WireMock
    WIREMOCK_PORT: 8080

default:
    tags:
        - anetdev

stages:          # List of stages for jobs, and their order of execution
    - install
    - test
#    - analyze
    - build-image-preprod
    - deploy
    - build-image-prod
    - deploy-prod

cache:
    paths:
        - vendor/
        - public/assets/
        - enseigne/*/vendor/
    key:
        files:
            - composer.lock

# ---- INSTALL ----
install-dependances:
    extends:
        - .enseigne_matrix_template
    stage: install
    script:
        - echo "Installing dependencies for $ENSEIGNE on ENV_TIER=$ENV_TIER"
        - env | sort | grep -E '^(ENSEIGNE|ENV_TIER)=' || true
        - sudo -HEu www-data composer install
    artifacts:
        paths:
            - "vendor"
            - "public/assets"
        expire_in: 10 minutes

# ---- TEST ----
test_enseignes:
    extends:
        - .enseigne_matrix_template
    stage: test
    needs:
        - job: install-dependances
          artifacts: true
    services:
        -   name: wiremock/wiremock:latest
            alias: wiremock
    script:
        - echo "Testing version for $ENSEIGNE"

        - sudo -HEu www-data php bin/console importmap:install
        
        - sudo -Eu www-data rm -rf public/assets/
    
        - sudo -HEu www-data APP_ENV=test php bin/console asset-map:compile

        # Démarrer le service WireMock
        - sudo -HEu www-data php bin/console wiremock:start
        
        # Lancer les tests PHPUnit pour l'enseigne spécifique
        - |
            if [ -f "enseigne/$ENSEIGNE/phpunit.xml.dist" ]; then
                sudo -HEu www-data php vendor/bin/phpunit \
                    -c "enseigne/$ENSEIGNE/phpunit.xml.dist" \
                    --log-junit enseigne.xml
                TEST_RESULT=$?
            else
                echo "No PHPUnit config for $ENSEIGNE, skipping tests."
                TEST_RESULT=0
            fi
        
        # Mettre à jour le fichier de statut des tests (localement ou vers un service externe)
        - |
            if [ $TEST_RESULT -eq 0 ]; then
              STATUS="success"
            else
              STATUS="failure"
            fi
            echo "$ENSEIGNE: $STATUS"

    artifacts:
        when: always
        paths:
            - "tests/approvals/*.received.html"
            - "tests/approvals/*.approved.html"
            - "enseigne/*/tests/Integration/approvals/*.received.html"
            - "enseigne/*/tests/Integration/approvals/*.approved.html"
        reports:
            junit:
                - enseigne.xml
        expire_in: 30 minutes
        
# ---- BUILD PREPROD ----
build-image-preprod:
    extends:
        - .enseigne_matrix_template
    stage: build-image-preprod
    image: docker:latest
    needs: ["test_enseignes"]
    variables:
        IMAGE_TAG: "$ENSEIGNE-preprod"
        DOCKER_TLS_CERTDIR: "/certs"
        COMPOSE_PROFILES: "*"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
        - echo "🏗 Building Docker image for $ENSEIGNE"
        - sed -i "s/ENSEIGNE=\"[A-Za-z]*\"/ENSEIGNE=\"$ENSEIGNE\"/g" .env
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml --progress=plain build --build-arg ENSEIGNE="$ENSEIGNE" --build-arg ENV_TIER="$ENV_TIER" --build-arg APP_ENV=prod php
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push php
        - echo "Image push successfully"
    rules:
        - if: '$CI_COMMIT_BRANCH == "preprod-socle"'
          when: always
        - when: never
     
# ---- DEPLOY PREPROD ----
deploy-preprod:
    extends:
        - .enseigne_matrix_template
    image: gitlab.alienor.net:5050/dev-docker/docker-tools
    stage: deploy
    needs: ["build-image-preprod"]
    environment:
        name: staging
    variables:
        GIT_STRATEGY: none
    script:
        - echo "Deploying $ENSEIGNE"
        # - "docker-tools update anet-dev web-portail-cicd_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:$ENSEIGNE-preprod"
        # - "docker-tools update $MANAGER_PP $WEB_ID_PP -i gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:$ENSEIGNE-preprod"
        - "docker-tools update aquitem-preprod web-4663_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:$ENSEIGNE-preprod"
    rules:
        - if: '$CI_COMMIT_BRANCH == "preprod-socle"'
          when: always
        - when: never

# ---- BUILD PROD ----
build-image-prod:
    extends:
        - .enseigne_matrix_template
    stage: build-image-prod
    image: docker:latest
    needs: ["test_enseignes"]
    variables:
        IMAGE_TAG: "$ENSEIGNE-prod"
        DOCKER_TLS_CERTDIR: "/certs"
        COMPOSE_PROFILES: "*"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
        - echo "🏗 Building Docker image for $ENSEIGNE"
        - sed -i "s/ENSEIGNE=\"[A-Za-z]*\"/ENSEIGNE=\"$ENSEIGNE\"/g" .env
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml --progress=plain build --build-arg ENSEIGNE="$ENSEIGNE" --build-arg ENV_TIER="$ENV_TIER" --build-arg APP_ENV=prod php
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push php
        - echo "Image push successfully"
    rules:
        - if: '$CI_COMMIT_BRANCH == "main-socle"'
          when: always
        - when: never

# ---- DEPLOY PROD ----
deploy-prod:
    extends:
        - .enseigne_matrix_template
    image: gitlab.alienor.net:5050/dev-docker/docker-tools
    stage: deploy-prod
    needs: ["build-image-prod"]
    environment:
        name: staging
    variables:
        GIT_STRATEGY: none
    script:
        - echo "Deploying $ENSEIGNE"
        - "docker-tools update aquitem-prod web-4664_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:$ENSEIGNE-prod"
        # - "docker-tools update $MANAGER_PROD $WEB_ID_PROD -i gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:$ENSEIGNE-prod"
    rules:
        - if: '$CI_COMMIT_BRANCH == "main-socle"'
          when: always
        - when: never

